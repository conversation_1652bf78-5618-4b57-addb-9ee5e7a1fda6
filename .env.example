# 数据库配置
DATABASE_URL=postgresql://username:password@localhost:5432/xiaoneng_aviation
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=xiaoneng_aviation
DATABASE_USER=username
DATABASE_PASSWORD=password

# Redis配置
REDIS_URL=redis://localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# RabbitMQ配置
RABBITMQ_URL=amqp://guest:guest@localhost:5672/
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest

# API配置
API_HOST=0.0.0.0
API_PORT=8000
API_DEBUG=false
API_RELOAD=false

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# 安全配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 算法配置
MAX_CALCULATION_TIME=3600
MAX_CONCURRENT_CALCULATIONS=10
DEFAULT_SIMULATION_RUNS=1000
