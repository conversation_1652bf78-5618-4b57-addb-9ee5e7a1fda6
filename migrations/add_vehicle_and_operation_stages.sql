-- 数据库迁移脚本：添加运输车辆配置表和设备环节适用性字段
-- 版本：1.1.0
-- 日期：2025-08-04
-- 描述：根据PRD更新，添加运输车辆配置管理和设备环节适用性支持

-- 1. 创建运输车辆配置表
CREATE TABLE IF NOT EXISTS vehicle_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- 车辆基本信息
    vehicle_type VARCHAR(50) NOT NULL,
    vehicle_model VARCHAR(100) NOT NULL,
    quantity INTEGER NOT NULL DEFAULT 1 CHECK (quantity > 0),
    
    -- 载重参数
    max_payload FLOAT NOT NULL CHECK (max_payload > 0),
    cargo_volume FLOAT NOT NULL CHECK (cargo_volume > 0),
    loading_constraints JSONB NOT NULL DEFAULT '{}',
    
    -- 性能参数
    max_speed FLOAT NOT NULL CHECK (max_speed > 0),
    fuel_consumption FLOAT NOT NULL CHECK (fuel_consumption > 0),
    maneuverability FLOAT NOT NULL DEFAULT 1.0 CHECK (maneuverability >= 0 AND maneuverability <= 1),
    
    -- 作业参数
    loading_time FLOAT NOT NULL CHECK (loading_time > 0),
    unloading_time FLOAT NOT NULL CHECK (unloading_time > 0),
    transfer_efficiency FLOAT NOT NULL DEFAULT 1.0 CHECK (transfer_efficiency >= 0 AND transfer_efficiency <= 1),
    
    -- 维护信息
    maintenance_cycle INTEGER NOT NULL CHECK (maintenance_cycle > 0),
    availability_rate FLOAT NOT NULL DEFAULT 0.95 CHECK (availability_rate >= 0 AND availability_rate <= 1),
    maintenance_info JSONB NOT NULL DEFAULT '{}',
    
    -- 运行状态
    operational_status VARCHAR(20) NOT NULL DEFAULT 'available',
    fault_statistics JSONB NOT NULL DEFAULT '{}',
    
    -- 备注信息
    remarks TEXT,
    
    -- 时间戳
    created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- 2. 为运输车辆配置表添加索引
CREATE INDEX IF NOT EXISTS idx_vehicle_configs_vehicle_type ON vehicle_configs(vehicle_type);
CREATE INDEX IF NOT EXISTS idx_vehicle_configs_operational_status ON vehicle_configs(operational_status);
CREATE INDEX IF NOT EXISTS idx_vehicle_configs_created_at ON vehicle_configs(created_at);
CREATE INDEX IF NOT EXISTS idx_vehicle_configs_vehicle_model ON vehicle_configs USING gin(to_tsvector('simple', vehicle_model));

-- 3. 为运输车辆配置表添加约束
ALTER TABLE vehicle_configs 
ADD CONSTRAINT chk_vehicle_type CHECK (
    vehicle_type IN (
        'flatbed_trailer',
        'container_trailer', 
        'cargo_truck',
        'tractor_unit'
    )
);

ALTER TABLE vehicle_configs 
ADD CONSTRAINT chk_operational_status CHECK (
    operational_status IN (
        'available',
        'in_use',
        'maintenance',
        'fault',
        'retired'
    )
);

-- 4. 为设备配置表添加环节适用性字段
ALTER TABLE equipment_configs 
ADD COLUMN IF NOT EXISTS applicable_stages JSONB NOT NULL DEFAULT '[]';

-- 5. 为设备配置表的新字段添加索引
CREATE INDEX IF NOT EXISTS idx_equipment_configs_applicable_stages ON equipment_configs USING gin(applicable_stages);

-- 6. 添加设备类型的新枚举值
-- 注意：PostgreSQL不支持直接修改枚举类型，这里使用约束检查
-- 如果使用的是枚举类型，需要先删除约束再重新创建

-- 更新设备类型约束以支持新的装卸载设备分类
ALTER TABLE equipment_configs DROP CONSTRAINT IF EXISTS chk_equipment_type;
ALTER TABLE equipment_configs 
ADD CONSTRAINT chk_equipment_type CHECK (
    equipment_type IN (
        -- 仓库内作业设备
        'warehouse_forklift',
        'warehouse_conveyor',
        'warehouse_platform',
        'warehouse_stacker',
        'warehouse_sorter',
        
        -- 运输车辆设备
        'transport_flatbed',
        'transport_container',
        'transport_cargo',
        'transport_tractor',
        
        -- 停机坪作业设备
        'apron_lift_platform',
        'apron_hydraulic_lift',
        'apron_boarding_bridge',
        'apron_cargo_loader',
        
        -- 辅助设备
        'auxiliary_pusher',
        'auxiliary_fixture',
        'auxiliary_safety',
        'auxiliary_communication',
        
        -- 保留原有类型以兼容现有数据
        'towing_vehicle',
        'lifting_equipment',
        'loading_equipment',
        'auxiliary_equipment',
        'loading_vehicle',
        'handling_equipment',
        'support_equipment'
    )
);

-- 7. 更新任务类型约束以支持装载/卸载作业
ALTER TABLE scenarios DROP CONSTRAINT IF EXISTS chk_task_type;
ALTER TABLE scenarios 
ADD CONSTRAINT chk_task_type CHECK (
    task_type IN (
        -- 主要作业类型
        'loading_operation',
        'unloading_operation',
        
        -- 保留原有类型以兼容现有数据
        'personnel_transport',
        'equipment_transport',
        'material_supply',
        'medical_evacuation',
        'special_transport'
    )
);

-- 8. 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 9. 为运输车辆配置表添加更新时间触发器
DROP TRIGGER IF EXISTS update_vehicle_configs_updated_at ON vehicle_configs;
CREATE TRIGGER update_vehicle_configs_updated_at
    BEFORE UPDATE ON vehicle_configs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- 10. 添加注释
COMMENT ON TABLE vehicle_configs IS '运输车辆配置表：存储机场地面运输车辆的详细配置信息';
COMMENT ON COLUMN vehicle_configs.vehicle_type IS '车辆类型：平板拖车、集装箱拖车、货物运输车、牵引车头';
COMMENT ON COLUMN vehicle_configs.applicable_stages IS '适用的作业环节列表：warehouse(仓库)、transport(运输)、apron(停机坪)、all_stages(全环节)';
COMMENT ON COLUMN equipment_configs.applicable_stages IS '设备适用的作业环节列表：warehouse(仓库)、transport(运输)、apron(停机坪)、all_stages(全环节)';

-- 11. 插入示例数据（可选，用于测试）
-- 注意：生产环境中应该通过应用程序或专门的数据脚本插入数据

-- 示例运输车辆配置
INSERT INTO vehicle_configs (
    vehicle_type, vehicle_model, quantity, max_payload, cargo_volume,
    max_speed, fuel_consumption, loading_time, unloading_time,
    maintenance_cycle, remarks
) VALUES 
(
    'flatbed_trailer', '平板拖车-FB001', 2, 25000, 45.0,
    60, 25.0, 15, 12, 200,
    '机场地面运输用平板拖车，适用于大型设备运输'
),
(
    'container_trailer', '集装箱拖车-CT001', 3, 30000, 67.5,
    50, 28.0, 20, 18, 250,
    '标准集装箱运输车，适用于标准化货物运输'
)
ON CONFLICT DO NOTHING;

-- 更新现有设备配置的环节适用性（示例）
UPDATE equipment_configs 
SET applicable_stages = '["warehouse", "transport"]'::jsonb
WHERE equipment_type IN ('warehouse_forklift', 'transport_flatbed')
AND applicable_stages = '[]'::jsonb;

UPDATE equipment_configs 
SET applicable_stages = '["apron"]'::jsonb
WHERE equipment_type IN ('apron_lift_platform', 'apron_cargo_loader')
AND applicable_stages = '[]'::jsonb;

-- 12. 验证迁移结果
-- 检查表是否创建成功
SELECT 
    table_name,
    table_type
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('vehicle_configs');

-- 检查字段是否添加成功
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'equipment_configs'
AND column_name = 'applicable_stages';

-- 输出迁移完成信息
SELECT 'PRD更新迁移完成：已添加运输车辆配置表和设备环节适用性字段' AS migration_status;
