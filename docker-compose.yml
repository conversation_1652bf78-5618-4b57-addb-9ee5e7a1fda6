version: '3.8'

services:
  # PostgreSQL数据库
  postgres:
    image: postgres:14
    container_name: xiaoneng_postgres
    environment:
      POSTGRES_DB: xiaoneng_aviation
      POSTGRES_USER: xiaoneng_user
      POSTGRES_PASSWORD: xiaoneng_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init_db.sql:/docker-entrypoint-initdb.d/init_db.sql
    networks:
      - xiaoneng_network

  # Redis缓存
  redis:
    image: redis:6-alpine
    container_name: xiaoneng_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - xiaoneng_network

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3-management
    container_name: xiaoneng_rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: xiaoneng_user
      RABBITMQ_DEFAULT_PASS: xiaoneng_password
    ports:
      - "5672:5672"
      - "15672:15672"
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - xiaoneng_network

  # 应用服务
  app:
    build: .
    container_name: xiaoneng_app
    environment:
      DATABASE_URL: **********************************************************/xiaoneng_aviation
      REDIS_URL: redis://redis:6379/0
      RABBITMQ_URL: amqp://xiaoneng_user:xiaoneng_password@rabbitmq:5672/
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
      - rabbitmq
    volumes:
      - ./src:/app/src
      - ./config:/app/config
    networks:
      - xiaoneng_network

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:

networks:
  xiaoneng_network:
    driver: bridge
