# Augment Memories 快速记忆条目

## 开发习惯记忆

### 编码节奏记忆
```
记住：写代码前先写测试，提交代码前运行全部测试，每天至少一次代码提交，保持小步快跑的开发节奏。
```

### 重构时机记忆
```
记住：发现重复代码立即重构，函数超过一屏立即拆分，看到TODO超过一周立即处理，代码审查发现问题立即修复。
```

### 调试技巧记忆
```
记住：先复现问题再修复，使用日志而不是print调试，保留调试代码作为测试用例，修复bug后添加回归测试。
```

## 技术决策记忆

### 技术选型记忆
```
记住：优先选择团队熟悉的技术，新技术必须有充分理由，考虑技术的学习成本和维护成本，评估技术的社区活跃度。
```

### 架构演进记忆
```
记住：从简单开始逐步演进，避免过度设计，保持架构的可测试性，定期评估架构的适用性。
```

### 性能优化记忆
```
记住：先测量再优化，优化最慢的部分，避免过早优化，保留性能测试基准。
```

## 团队协作记忆

### 沟通效率记忆
```
记住：问题描述要具体，提供复现步骤，分享解决方案，及时更新进度状态。
```

### 知识传承记忆
```
记住：重要决策写成文档，复杂逻辑添加注释，定期分享技术心得，建立团队知识库。
```

### 代码协作记忆
```
记住：小步提交便于回滚，清晰的提交信息，及时同步主分支，主动进行代码审查。
```

## 质量保证记忆

### 测试策略记忆
```
记住：单元测试覆盖核心逻辑，集成测试验证接口，端到端测试模拟用户场景，性能测试验证响应时间。
```

### 错误预防记忆
```
记住：输入验证在边界处，异常处理要具体，日志记录要充分，监控告警要及时。
```

### 代码审查记忆
```
记住：审查逻辑正确性，检查安全漏洞，验证性能影响，确认测试覆盖。
```

## 安全意识记忆

### 数据保护记忆
```
记住：敏感数据加密存储，传输过程使用HTTPS，定期轮换密钥，最小化数据访问权限。
```

### 输入安全记忆
```
记住：永远不信任用户输入，使用参数化查询，验证文件上传类型，限制请求频率。
```

### 权限控制记忆
```
记住：默认拒绝访问，最小权限原则，定期审查权限，记录敏感操作。
```

## 运维监控记忆

### 系统监控记忆
```
记住：监控关键业务指标，设置合理告警阈值，建立故障响应流程，定期演练应急预案。
```

### 日志管理记忆
```
记住：结构化日志格式，包含请求追踪ID，设置日志轮转策略，敏感信息不记录。
```

### 部署安全记忆
```
记住：生产环境独立部署，配置环境变量管理，实现蓝绿部署，保留回滚能力。
```

## 学习成长记忆

### 技术学习记忆
```
记住：关注行业技术趋势，定期学习新技术，参与开源项目，分享学习心得。
```

### 问题解决记忆
```
记住：先搜索已知解决方案，理解问题根本原因，记录解决过程，分享给团队成员。
```

### 职业发展记忆
```
记住：设定明确的技术目标，主动承担有挑战的任务，建立个人技术品牌，持续改进工作方法。
```

## 项目管理记忆

### 需求理解记忆
```
记住：充分理解业务需求，识别隐含的技术需求，评估实现复杂度，预留缓冲时间。
```

### 进度管理记忆
```
记住：任务拆分要具体，及时汇报进度，主动识别风险，寻求帮助不要拖延。
```

### 交付质量记忆
```
记住：功能完整性验证，用户体验测试，性能指标达标，文档更新同步。
```

## 工具使用记忆

### 开发工具记忆
```
记住：熟练使用IDE快捷键，配置代码格式化工具，使用版本控制最佳实践，自动化重复性任务。
```

### 调试工具记忆
```
记住：掌握断点调试技巧，使用性能分析工具，监控应用运行状态，分析错误日志信息。
```

### 协作工具记忆
```
记住：及时更新任务状态，清晰描述问题现象，主动分享有用信息，保持沟通渠道畅通。
```
