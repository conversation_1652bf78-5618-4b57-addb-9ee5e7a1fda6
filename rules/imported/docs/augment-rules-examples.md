---
type: "agent_requested"
description: "Example description"
---
# Augment AI助手实用开发规则示例


## 安全编码规范

### 输入验证规则
```
所有外部输入(用户输入、API参数、文件内容)必须进行验证和清理。使用白名单验证而不是黑名单。对于SQL查询使用参数化查询防止SQL注入。对于文件路径进行路径遍历攻击检查。
```

### 敏感信息处理规则
```
不得在代码中硬编码密码、API密钥、数据库连接字符串等敏感信息。使用环境变量或安全的配置管理系统。在日志中不得记录敏感信息。对于调试输出，确保不包含敏感数据。
```

### 异常处理安全规则
```
异常处理不得暴露系统内部信息给用户。使用通用的错误消息而不是详细的技术错误。记录详细错误信息到日志系统供开发人员调试。避免捕获过于宽泛的异常(如Exception)。
```

### 权限检查规则
```
所有需要权限的操作必须在执行前进行权限检查。不要仅依赖前端的权限控制。使用最小权限原则，只授予必要的权限。对于敏感操作，考虑实施双重验证。
```

## 性能和资源管理

### 资源释放规则
```
使用with语句管理文件、数据库连接等资源，确保自动释放。对于大文件处理，使用流式处理而不是一次性加载到内存。及时关闭不再使用的连接和文件句柄。
```

### 循环优化规则
```
避免在循环中进行重复的计算或数据库查询。使用列表推导式和生成器表达式提高性能。对于大数据集，考虑使用分页或批处理。避免无限循环，设置合理的循环次数限制。
```

### 内存使用规则
```
避免创建不必要的大对象。使用生成器而不是列表来处理大数据集。及时删除不再使用的大对象引用。对于缓存，设置合理的大小限制和过期时间。
```

## 错误处理和日志

### 异常处理规范规则
```
使用具体的异常类型而不是通用的Exception。在适当的层级处理异常，不要过早捕获。记录异常的完整堆栈信息。对于可恢复的错误，提供重试机制。
```

### 日志记录规则
```
使用适当的日志级别：DEBUG用于调试信息，INFO用于一般信息，WARNING用于警告，ERROR用于错误，CRITICAL用于严重错误。日志消息应该清晰描述发生了什么。包含足够的上下文信息用于问题排查。
```

### 错误消息规则
```
错误消息应该对用户友好，避免技术术语。提供可操作的建议来解决问题。包含错误代码或标识符便于支持人员查找。不要在错误消息中暴露敏感信息。
```

## 代码组织和架构

### 依赖注入规则
```
使用依赖注入而不是硬编码依赖关系。通过构造函数或方法参数传递依赖。避免使用全局变量和单例模式，除非确实必要。使依赖关系明确和可测试。
```

### 接口设计规则
```
定义清晰的接口和抽象基类。使用类型注解明确参数和返回值类型。保持接口稳定，避免频繁的破坏性变更。遵循里氏替换原则，确保子类可以替换父类。
```

### 配置管理规则
```
将配置与代码分离，使用配置文件或环境变量。为不同环境(开发、测试、生产)提供不同的配置。验证配置的完整性和正确性。提供配置的默认值和文档说明。
```

## 版本控制和协作

### 提交消息规则
```
提交消息格式：类型(范围): 简短描述。类型包括：feat(新功能)、fix(修复)、docs(文档)、style(格式)、refactor(重构)、test(测试)、chore(构建)。简短描述使用现在时态，不超过50字符。
```

### 分支管理规则
```
使用feature分支开发新功能，完成后合并到主分支。分支命名格式：类型/简短描述，如feature/user-authentication。保持分支生命周期短，及时合并和删除。定期从主分支同步更新。
```

### 代码审查规则
```
所有代码变更必须经过代码审查才能合并。审查者应检查代码质量、安全性、性能和可维护性。提供建设性的反馈和改进建议。审查通过后才能合并到主分支。
```

## 数据库和API设计

### 数据库查询优化规则
```
避免在循环中执行数据库查询，使用批量操作。为经常查询的字段添加索引。使用LIMIT限制查询结果数量。避免SELECT *，只查询需要的字段。使用连接池管理数据库连接。
```

### API设计规范规则
```
API端点使用RESTful设计原则。HTTP方法语义：GET获取、POST创建、PUT更新、DELETE删除。使用HTTP状态码表示操作结果。API响应使用JSON格式，包含统一的错误格式。
```

### 数据验证规则
```
在数据库层和应用层都进行数据验证。使用数据库约束确保数据完整性。验证数据类型、长度、格式和业务规则。对于用户输入，进行XSS和注入攻击防护。
```

## 前端开发规范

### React组件设计规则
```
React组件保持单一职责，一个组件只做一件事。使用函数组件和Hooks而不是类组件。Props使用TypeScript类型定义。组件文件名使用PascalCase，与组件名一致。
```

### 状态管理规则
```
使用useState管理组件内部状态，useContext管理跨组件状态。复杂状态使用useReducer。避免过度使用全局状态，优先使用组件本地状态。状态更新使用不可变数据模式。
```

### CSS样式规则
```
使用CSS模块或styled-components避免样式冲突。遵循BEM命名规范。避免使用!important，通过提高选择器特异性解决样式优先级问题。使用CSS变量管理主题色彩。
```

## 移动端开发规范

### 响应式设计规则
```
使用移动优先的设计方法。使用相对单位(rem、em、%)而不是固定像素。为不同屏幕尺寸提供适配方案。测试在不同设备和浏览器上的兼容性。
```

### 性能优化规则
```
图片使用适当的格式和尺寸，启用懒加载。减少HTTP请求数量，合并CSS和JavaScript文件。使用CDN加速静态资源加载。启用Gzip压缩减少传输大小。
```

## 微服务架构规范

### 服务拆分规则
```
按业务领域拆分服务，每个服务负责一个明确的业务功能。服务之间通过API通信，避免直接数据库访问。保持服务的独立部署能力。设计服务时考虑故障隔离。
```

### 服务通信规则
```
使用HTTP/REST或gRPC进行同步通信。使用消息队列进行异步通信。实现熔断器模式防止级联故障。设置合理的超时时间和重试策略。
```

### 配置和监控规则
```
每个服务有独立的配置管理。实现健康检查端点。记录详细的操作日志和性能指标。使用分布式追踪跟踪请求链路。设置告警机制监控服务状态。
```

## 容器化和部署

### Docker使用规则
```
使用多阶段构建减少镜像大小。不在镜像中包含敏感信息。使用非root用户运行容器。为容器设置资源限制。使用健康检查确保容器正常运行。
```

### Kubernetes部署规则
```
使用命名空间隔离不同环境。设置资源请求和限制。使用ConfigMap和Secret管理配置。实现滚动更新策略。配置自动扩缩容规则。
```

## 监控和运维

### 日志聚合规则
```
使用结构化日志格式(JSON)。包含请求ID用于链路追踪。记录关键业务操作和性能指标。设置日志轮转和清理策略。使用集中式日志管理系统。
```

### 监控告警规则
```
监控应用性能指标：响应时间、吞吐量、错误率。监控系统资源：CPU、内存、磁盘、网络。设置合理的告警阈值，避免告警疲劳。建立告警升级机制。
```

### 备份和恢复规则
```
定期备份重要数据，测试备份的完整性。制定灾难恢复计划，定期演练。使用版本控制管理配置文件。建立数据恢复的SLA标准。
```
