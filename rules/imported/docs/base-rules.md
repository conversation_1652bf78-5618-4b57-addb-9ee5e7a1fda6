---
type: "always_apply"
---

## 代码文件长度和结构控制

### 文件长度限制规则
```
每个代码文件不得超过500行。如果文件超过500行，必须拆分为多个模块。单个函数不得超过50行，单个类不得超过300行。如果需要更长的实现，请将其拆分为更小的函数或类。不得使用任何硬编码与模拟数据
```

### 模块职责单一性规则
```
每个Python模块只能包含一个主要的类或功能组。相关的辅助函数可以放在同一文件中，但不要在一个文件中混合不相关的功能。如果发现一个文件包含多个不相关的类或功能，请建议拆分文件。
```

### 导入语句组织规则
```
Python导入语句必须按以下顺序组织：1) 标准库导入 2) 第三方库导入 3) 本地应用导入。每组之间用空行分隔。导入语句不得超过10个，如果超过请考虑重构代码结构。
```
## 函数复杂度控制

### 圈复杂度限制规则
```
每个函数的圈复杂度不得超过10。如果函数包含过多的if/else、for、while、try/except语句，请将其拆分为更小的函数。使用早期返回(early return)模式减少嵌套层级。
```

### 函数参数限制规则
```
函数参数不得超过5个。如果需要更多参数，请使用数据类(dataclass)、字典或配置对象来传递参数。避免使用*args和**kwargs，除非是装饰器或框架代码。
```

### 嵌套层级限制规则
```
代码嵌套层级不得超过4层。如果出现过深的嵌套，请使用早期返回、提取函数或重构逻辑来减少嵌套。优先使用guard clauses而不是深层嵌套的if语句。
```

## 命名规范

### Python命名规则
```
Python代码必须遵循以下命名规范：变量和函数使用snake_case，类名使用PascalCase，常量使用UPPER_SNAKE_CASE，私有成员以单下划线开头，特殊方法以双下划线包围。避免使用单字母变量名，除了循环计数器i、j、k。
```

### 变量命名描述性规则
```
所有变量名必须具有描述性，能够清楚表达其用途。避免使用缩写，除非是广泛认知的缩写(如url、id、api)。布尔变量应以is_、has_、can_、should_等开头。避免使用data、info、item等模糊的名称。
```

### 函数命名动词规则
```
函数名必须以动词开头，清楚表达函数的行为。获取数据使用get_、设置数据使用set_、检查状态使用is_或has_、创建对象使用create_、删除对象使用delete_。避免使用handle_、process_等模糊的动词。
```

### 代码规则
```
统一考虑配置文件，代码中不能出现硬编码，如需模拟/测试数据，不能在代码里直接添加模拟/测试数据，需在配置文件或数据库中添加模拟/测试数据
```

## 注释和文档要求

### 函数文档字符串规则
```
所有公共函数和方法必须包含docstring，使用Google风格格式。必须包含：简要描述、Args参数说明、Returns返回值说明、Raises异常说明(如果有)。私有函数如果逻辑复杂也应添加docstring。
```

### 类文档字符串规则
```
所有类必须包含docstring，说明类的用途、主要功能和使用示例。如果类有特殊的初始化要求或使用限制，必须在docstring中说明。抽象基类必须说明子类需要实现的方法。
```

### 复杂逻辑注释规则
```
对于复杂的算法、业务逻辑或非显而易见的代码，必须添加行内注释说明。注释应该解释"为什么"而不是"是什么"。正则表达式、复杂的数学计算、性能优化代码必须有详细注释。
```

### TODO和FIXME标记规则
```
使用TODO标记未完成的功能，格式为"TODO: 描述 (负责人, 日期)"。使用FIXME标记需要修复的问题，格式为"FIXME: 问题描述 (负责人, 日期)"。所有TODO和FIXME必须在代码审查时讨论处理方案。
```
## 测试覆盖率和质量

### 测试覆盖率要求规则
```
所有新增代码的单元测试覆盖率必须达到90%以上。每个公共方法都必须有对应的测试用例。测试必须覆盖正常情况、边界条件和异常情况。不允许为了提高覆盖率而编写无意义的测试。
```

### 测试命名规范规则
```
测试方法命名格式：test_方法名_条件_期望结果。例如：test_calculate_discount_with_valid_amount_returns_correct_value。测试类命名格式：Test + 被测试的类名。测试文件命名格式：test_ + 被测试的模块名。
```

### 测试独立性规则
```
每个测试用例必须独立运行，不依赖其他测试的执行顺序或结果。使用setUp和tearDown方法或pytest的fixture来准备和清理测试环境。避免在测试中使用真实的外部服务，使用mock对象代替。
```

### 断言清晰性规则
```
每个测试用例应该只测试一个具体的行为。使用具体的断言方法而不是通用的assertTrue。为断言添加描述性的错误消息。避免在一个测试方法中包含过多的断言。
```
