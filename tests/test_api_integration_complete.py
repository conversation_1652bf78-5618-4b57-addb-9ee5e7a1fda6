"""
API集成测试完整用例

包含所有模块API接口的集成测试
遵循base-rules.md规范：测试独立性、断言清晰性、命名规范
"""

import pytest
from httpx import AsyncClient
from typing import Dict, Any


class TestAPIIntegration:
    """API集成测试类"""
    
    @pytest.fixture
    def sample_scenario_data(self) -> Dict[str, Any]:
        """场景测试数据fixture"""
        return {
            "scenario_name": "集成测试场景",
            "scenario_type": "integration_test",
            "description": "用于API集成测试的场景",
            "task_type": "equipment_transport",
            "environment_conditions": {
                "weather": {"visibility": 10000, "wind_speed": 15},
                "terrain": "plain"
            },
            "resource_constraints": {
                "transport_capacity": {"max_aircraft": 5}
            },
            "mission_requirements": {
                "cargo_weight": 100000,
                "origin": "integration_test_A",
                "destination": "integration_test_B"
            },
            "threat_factors": {
                "enemy_threats": {"air_defense": "medium"}
            },
            "threat_level": "medium",
            "created_by": "integration_tester"
        }
    
    @pytest.fixture
    def sample_equipment_data(self) -> Dict[str, Any]:
        """设备配置测试数据fixture"""
        return {
            "equipment_type": "loading_vehicle",
            "equipment_model": "集成测试装载车-INT001",
            "quantity": 2,
            "max_load_capacity": 20000,
            "loading_speed": 5000,
            "unloading_speed": 6000,
            "operation_radius": 100,
            "power_consumption": 80,
            "unit_operation_duration": 1.5,
            "efficiency_factor": 0.88,
            "maintenance_interval": 250,
            "operational_status": "available",
            "maintenance_info": {"test": "integration"},
            "remarks": "集成测试用设备配置"
        }
    
    @pytest.fixture
    def sample_aircraft_data(self) -> Dict[str, Any]:
        """飞机配置测试数据fixture"""
        return {
            "aircraft_model": "Y-8",
            "quantity": 1,
            "payload_capacity": 20000,
            "operational_range": 5700,
            "cruise_speed": 500,
            "fuel_consumption": 1800,
            "loading_time": 40,
            "unloading_time": 30,
            "ground_taxi_time": 15,
            "crew_requirements": {
                "pilots": 2,
                "flight_engineers": 0,
                "loadmasters": 1
            },
            "compatible_equipment": ["集成测试装载车"],
            "maintenance_schedule": {
                "routine_maintenance_hours": 100,
                "major_maintenance_hours": 1000,
                "last_maintenance_date": "2025-07-15"
            },
            "status": "available",
            "remarks": "集成测试用飞机配置"
        }
    
    @pytest.fixture
    def sample_configuration_data(self) -> Dict[str, Any]:
        """配置方案测试数据fixture"""
        return {
            "scheme_name": "集成测试配置方案",
            "scheme_type": "mixed_transport",
            "description": "用于API集成测试的配置方案",
            "equipment_config_ids": [],
            "aircraft_config_ids": [],
            "personnel_config_ids": [],
            "weather_condition_ids": [],
            "scenario_parameters": {"test": "integration"},
            "operational_parameters": {
                "max_operation_duration": 12,
                "safety_margin": 0.2,
                "efficiency_target": 0.85,
                "priority_level": "medium"
            },
            "tags": ["集成测试", "API测试"],
            "category": "测试",
            "created_by": "integration_tester"
        }
    
    async def test_scenario_api_complete_workflow_executes_successfully(
        self,
        get_test_client: AsyncClient,
        sample_scenario_data: Dict[str, Any]
    ):
        """测试场景API完整工作流程执行成功"""
        # 1. 创建场景
        create_response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=sample_scenario_data
        )
        assert create_response.status_code == 201
        created_scenario = create_response.json()
        scenario_id = created_scenario["id"]
        assert created_scenario["scenario_name"] == sample_scenario_data["scenario_name"]
        
        # 2. 获取场景列表
        list_response = await get_test_client.get("/api/v1/scenarios/")
        assert list_response.status_code == 200
        scenario_list = list_response.json()
        assert scenario_list["total"] >= 1
        assert len(scenario_list["scenarios"]) >= 1
        
        # 3. 获取场景详情
        detail_response = await get_test_client.get(f"/api/v1/scenarios/{scenario_id}")
        assert detail_response.status_code == 200
        scenario_detail = detail_response.json()
        assert scenario_detail["id"] == scenario_id
        assert scenario_detail["scenario_name"] == sample_scenario_data["scenario_name"]
        
        # 4. 更新场景
        update_data = {
            "scenario_name": "更新后的集成测试场景",
            "description": "已更新的集成测试场景描述"
        }
        update_response = await get_test_client.put(
            f"/api/v1/scenarios/{scenario_id}",
            json=update_data
        )
        assert update_response.status_code == 200
        updated_scenario = update_response.json()
        assert updated_scenario["scenario_name"] == update_data["scenario_name"]
        
        # 5. 删除场景
        delete_response = await get_test_client.delete(f"/api/v1/scenarios/{scenario_id}")
        assert delete_response.status_code == 204
    
    async def test_equipment_api_complete_workflow_executes_successfully(
        self,
        get_test_client: AsyncClient,
        sample_equipment_data: Dict[str, Any]
    ):
        """测试设备配置API完整工作流程执行成功"""
        # 1. 创建设备配置
        create_response = await get_test_client.post(
            "/api/v1/equipment-configs/",
            json=sample_equipment_data
        )
        assert create_response.status_code == 201
        created_equipment = create_response.json()
        equipment_id = created_equipment["id"]
        assert created_equipment["equipment_model"] == sample_equipment_data["equipment_model"]
        
        # 2. 获取设备配置列表
        list_response = await get_test_client.get("/api/v1/equipment-configs/")
        assert list_response.status_code == 200
        equipment_list = list_response.json()
        assert equipment_list["total"] >= 1
        assert len(equipment_list["equipment_configs"]) >= 1
        
        # 3. 获取设备配置详情
        detail_response = await get_test_client.get(f"/api/v1/equipment-configs/{equipment_id}")
        assert detail_response.status_code == 200
        equipment_detail = detail_response.json()
        assert equipment_detail["id"] == equipment_id
        assert equipment_detail["equipment_model"] == sample_equipment_data["equipment_model"]
        
        # 4. 更新设备配置
        update_data = {
            "equipment_model": "更新后的集成测试装载车-INT001-V2",
            "quantity": 3,
            "efficiency_factor": 0.92
        }
        update_response = await get_test_client.put(
            f"/api/v1/equipment-configs/{equipment_id}",
            json=update_data
        )
        assert update_response.status_code == 200
        updated_equipment = update_response.json()
        assert updated_equipment["equipment_model"] == update_data["equipment_model"]
        assert updated_equipment["quantity"] == update_data["quantity"]
        
        # 5. 删除设备配置
        delete_response = await get_test_client.delete(f"/api/v1/equipment-configs/{equipment_id}")
        assert delete_response.status_code == 204
    
    async def test_aircraft_api_complete_workflow_executes_successfully(
        self,
        get_test_client: AsyncClient,
        sample_aircraft_data: Dict[str, Any]
    ):
        """测试飞机配置API完整工作流程执行成功"""
        # 1. 创建飞机配置
        create_response = await get_test_client.post(
            "/api/v1/aircraft-configs/",
            json=sample_aircraft_data
        )
        assert create_response.status_code == 201
        created_aircraft = create_response.json()
        aircraft_id = created_aircraft["id"]
        assert created_aircraft["aircraft_model"] == sample_aircraft_data["aircraft_model"]
        
        # 2. 获取飞机配置列表
        list_response = await get_test_client.get("/api/v1/aircraft-configs/")
        assert list_response.status_code == 200
        aircraft_list = list_response.json()
        assert aircraft_list["total"] >= 1
        assert len(aircraft_list["aircraft_configs"]) >= 1
        
        # 3. 获取飞机配置详情
        detail_response = await get_test_client.get(f"/api/v1/aircraft-configs/{aircraft_id}")
        assert detail_response.status_code == 200
        aircraft_detail = detail_response.json()
        assert aircraft_detail["id"] == aircraft_id
        assert aircraft_detail["aircraft_model"] == sample_aircraft_data["aircraft_model"]
        
        # 4. 更新飞机配置
        update_data = {
            "quantity": 2,
            "fuel_consumption": 1750,
            "loading_time": 35
        }
        update_response = await get_test_client.put(
            f"/api/v1/aircraft-configs/{aircraft_id}",
            json=update_data
        )
        assert update_response.status_code == 200
        updated_aircraft = update_response.json()
        assert updated_aircraft["quantity"] == update_data["quantity"]
        assert updated_aircraft["fuel_consumption"] == update_data["fuel_consumption"]
        
        # 5. 删除飞机配置
        delete_response = await get_test_client.delete(f"/api/v1/aircraft-configs/{aircraft_id}")
        assert delete_response.status_code == 204
    
    async def test_configuration_api_complete_workflow_executes_successfully(
        self,
        get_test_client: AsyncClient,
        sample_configuration_data: Dict[str, Any]
    ):
        """测试配置方案API完整工作流程执行成功"""
        # 1. 创建配置方案
        create_response = await get_test_client.post(
            "/api/v1/configuration-schemes/",
            json=sample_configuration_data
        )
        assert create_response.status_code == 201
        created_scheme = create_response.json()
        scheme_id = created_scheme["id"]
        assert created_scheme["scheme_name"] == sample_configuration_data["scheme_name"]
        
        # 2. 获取配置方案列表
        list_response = await get_test_client.get("/api/v1/configuration-schemes/")
        assert list_response.status_code == 200
        scheme_list = list_response.json()
        assert scheme_list["total"] >= 1
        assert len(scheme_list["schemes"]) >= 1
        
        # 3. 获取配置方案详情
        detail_response = await get_test_client.get(f"/api/v1/configuration-schemes/{scheme_id}")
        assert detail_response.status_code == 200
        scheme_detail = detail_response.json()
        assert scheme_detail["id"] == scheme_id
        assert scheme_detail["scheme_name"] == sample_configuration_data["scheme_name"]
        
        # 4. 验证配置方案
        validate_response = await get_test_client.post(
            f"/api/v1/configuration-schemes/{scheme_id}/validate"
        )
        assert validate_response.status_code == 200
        validation_result = validate_response.json()
        assert "overall_status" in validation_result
        assert "validation_errors" in validation_result
        assert "validation_warnings" in validation_result
        
        # 5. 获取方案版本列表
        versions_response = await get_test_client.get(
            f"/api/v1/configuration-schemes/{scheme_id}/versions"
        )
        assert versions_response.status_code == 200
        version_list = versions_response.json()
        assert version_list["total"] >= 1
        assert len(version_list["versions"]) >= 1
        
        # 6. 更新配置方案
        update_data = {
            "scheme_name": "更新后的集成测试配置方案",
            "description": "已更新的集成测试配置方案描述",
            "rating": 4.0
        }
        update_response = await get_test_client.put(
            f"/api/v1/configuration-schemes/{scheme_id}",
            json=update_data
        )
        assert update_response.status_code == 200
        updated_scheme = update_response.json()
        assert updated_scheme["scheme_name"] == update_data["scheme_name"]
        assert updated_scheme["rating"] == update_data["rating"]
        
        # 7. 删除配置方案
        delete_response = await get_test_client.delete(f"/api/v1/configuration-schemes/{scheme_id}")
        assert delete_response.status_code == 204
    
    async def test_cross_module_integration_workflow_executes_successfully(
        self,
        get_test_client: AsyncClient,
        sample_scenario_data: Dict[str, Any],
        sample_equipment_data: Dict[str, Any],
        sample_aircraft_data: Dict[str, Any]
    ):
        """测试跨模块集成工作流程执行成功"""
        created_ids = {}
        
        try:
            # 1. 创建场景
            scenario_response = await get_test_client.post(
                "/api/v1/scenarios/",
                json=sample_scenario_data
            )
            assert scenario_response.status_code == 201
            created_ids["scenario"] = scenario_response.json()["id"]
            
            # 2. 创建设备配置
            equipment_response = await get_test_client.post(
                "/api/v1/equipment-configs/",
                json=sample_equipment_data
            )
            assert equipment_response.status_code == 201
            created_ids["equipment"] = equipment_response.json()["id"]
            
            # 3. 创建飞机配置
            aircraft_response = await get_test_client.post(
                "/api/v1/aircraft-configs/",
                json=sample_aircraft_data
            )
            assert aircraft_response.status_code == 201
            created_ids["aircraft"] = aircraft_response.json()["id"]
            
            # 4. 创建整合配置方案
            integrated_scheme_data = {
                "scheme_name": "跨模块集成测试配置方案",
                "scheme_type": "mixed_transport",
                "description": "整合场景、设备、飞机的综合配置方案",
                "equipment_config_ids": [created_ids["equipment"]],
                "aircraft_config_ids": [created_ids["aircraft"]],
                "personnel_config_ids": [],
                "weather_condition_ids": [],
                "scenario_parameters": {
                    "reference_scenario_id": created_ids["scenario"],
                    "integration_test": True
                },
                "operational_parameters": {
                    "max_operation_duration": 14,
                    "safety_margin": 0.25,
                    "efficiency_target": 0.88,
                    "priority_level": "high"
                },
                "tags": ["跨模块", "集成测试", "综合方案"],
                "category": "集成测试",
                "created_by": "integration_tester"
            }
            
            scheme_response = await get_test_client.post(
                "/api/v1/configuration-schemes/",
                json=integrated_scheme_data
            )
            assert scheme_response.status_code == 201
            created_scheme = scheme_response.json()
            created_ids["scheme"] = created_scheme["id"]
            
            # 验证配置方案包含了正确的资源ID
            assert created_ids["equipment"] in created_scheme["equipment_config_ids"]
            assert created_ids["aircraft"] in created_scheme["aircraft_config_ids"]
            
            # 5. 验证整合配置方案
            validate_response = await get_test_client.post(
                f"/api/v1/configuration-schemes/{created_ids['scheme']}/validate"
            )
            assert validate_response.status_code == 200
            validation_result = validate_response.json()
            
            # 验证结果应该包含资源兼容性分析
            assert "resource_compatibility" in validation_result
            assert "performance_prediction" in validation_result
            
            # 6. 测试资源更新对配置方案的影响
            # 更新设备配置
            equipment_update = {
                "efficiency_factor": 0.95,
                "remarks": "已优化的集成测试设备"
            }
            equipment_update_response = await get_test_client.put(
                f"/api/v1/equipment-configs/{created_ids['equipment']}",
                json=equipment_update
            )
            assert equipment_update_response.status_code == 200
            
            # 重新验证配置方案（应该反映设备更新）
            revalidate_response = await get_test_client.post(
                f"/api/v1/configuration-schemes/{created_ids['scheme']}/validate"
            )
            assert revalidate_response.status_code == 200
            
        finally:
            # 清理创建的资源
            for resource_type, resource_id in created_ids.items():
                if resource_type == "scenario":
                    await get_test_client.delete(f"/api/v1/scenarios/{resource_id}")
                elif resource_type == "equipment":
                    await get_test_client.delete(f"/api/v1/equipment-configs/{resource_id}")
                elif resource_type == "aircraft":
                    await get_test_client.delete(f"/api/v1/aircraft-configs/{resource_id}")
                elif resource_type == "scheme":
                    await get_test_client.delete(f"/api/v1/configuration-schemes/{resource_id}")
    
    async def test_api_error_handling_returns_proper_error_responses(
        self,
        get_test_client: AsyncClient
    ):
        """测试API错误处理返回正确的错误响应"""
        # 1. 测试404错误
        response = await get_test_client.get("/api/v1/scenarios/non-existent-id")
        assert response.status_code == 404
        error_data = response.json()
        assert error_data["detail"] == "场景不存在"
        
        # 2. 测试400错误（无效数据）
        invalid_scenario_data = {
            "scenario_name": "",  # 空名称
            "scenario_type": "invalid_test",
            "task_type": "equipment_transport",
            "mission_requirements": {
                "cargo_weight": -1000,  # 无效重量
                "origin": "",
                "destination": ""
            }
        }
        
        response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=invalid_scenario_data
        )
        assert response.status_code == 400
        error_data = response.json()
        assert "errors" in error_data["detail"]
        
        # 3. 测试422错误（数据验证失败）
        malformed_data = {
            "scenario_name": "测试场景",
            "invalid_field": "invalid_value",
            "task_type": "invalid_task_type"  # 无效的枚举值
        }
        
        response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=malformed_data
        )
        assert response.status_code in [400, 422]  # 可能是400或422，取决于验证层级
    
    async def test_api_pagination_and_filtering_works_correctly(
        self,
        get_test_client: AsyncClient,
        sample_scenario_data: Dict[str, Any]
    ):
        """测试API分页和筛选功能正常工作"""
        created_ids = []
        
        try:
            # 创建多个测试场景
            for i in range(5):
                scenario_data = sample_scenario_data.copy()
                scenario_data["scenario_name"] = f"分页测试场景_{i+1}"
                scenario_data["scenario_type"] = f"pagination_test_{i%2}"  # 两种类型
                
                response = await get_test_client.post(
                    "/api/v1/scenarios/",
                    json=scenario_data
                )
                assert response.status_code == 201
                created_ids.append(response.json()["id"])
            
            # 测试分页
            response = await get_test_client.get(
                "/api/v1/scenarios/",
                params={"page": 1, "page_size": 3}
            )
            assert response.status_code == 200
            data = response.json()
            assert data["page"] == 1
            assert data["page_size"] == 3
            assert len(data["scenarios"]) <= 3
            
            # 测试筛选
            response = await get_test_client.get(
                "/api/v1/scenarios/",
                params={"scenario_type": "pagination_test_0"}
            )
            assert response.status_code == 200
            data = response.json()
            
            # 验证筛选结果
            for scenario in data["scenarios"]:
                assert scenario["scenario_type"] == "pagination_test_0"
            
            # 测试搜索
            response = await get_test_client.get(
                "/api/v1/scenarios/",
                params={"search": "分页测试"}
            )
            assert response.status_code == 200
            data = response.json()
            assert data["total"] >= 5  # 至少包含我们创建的5个场景
            
        finally:
            # 清理测试数据
            for scenario_id in created_ids:
                await get_test_client.delete(f"/api/v1/scenarios/{scenario_id}")
