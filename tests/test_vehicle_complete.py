"""
运输车辆配置管理模块完整测试用例

包含数据模型、服务层和API层的全面测试
遵循base-rules.md规范：测试独立性、断言清晰性、命名规范
"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.models.vehicle import (
    VehicleConfig,
    VehicleTypeEnum,
    VehicleStatusEnum
)
from src.services.vehicle import VehicleService
from src.schemas.vehicle import (
    VehicleCreateSchema,
    VehicleUpdateSchema
)


class TestVehicleModel:
    """运输车辆配置数据模型测试类"""
    
    def test_vehicle_model_creation_with_valid_data_creates_instance(self):
        """测试使用有效数据创建运输车辆配置模型实例"""
        # 准备测试数据
        vehicle = VehicleConfig(
            vehicle_type=VehicleTypeEnum.FLATBED_TRAILER,
            vehicle_model="测试平板拖车-TEST001",
            quantity=2,
            max_payload=25000,
            cargo_volume=45.0,
            loading_constraints={"max_height": 3.5},
            max_speed=60,
            fuel_consumption=25.0,
            maneuverability=0.85,
            loading_time=15,
            unloading_time=12,
            transfer_efficiency=0.92,
            maintenance_cycle=200,
            availability_rate=0.95,
            maintenance_info={"last_maintenance": "2025-07-01"},
            operational_status=VehicleStatusEnum.AVAILABLE,
            fault_statistics={"total_faults": 0},
            remarks="测试用运输车辆配置"
        )
        
        # 验证实例创建
        assert vehicle.vehicle_type == VehicleTypeEnum.FLATBED_TRAILER
        assert vehicle.vehicle_model == "测试平板拖车-TEST001"
        assert vehicle.quantity == 2
        assert vehicle.max_payload == 25000
        assert vehicle.cargo_volume == 45.0
        assert vehicle.max_speed == 60
        assert vehicle.fuel_consumption == 25.0
        assert vehicle.operational_status == VehicleStatusEnum.AVAILABLE
    
    def test_vehicle_model_to_dict_returns_complete_dictionary(self):
        """测试运输车辆配置模型转换为字典返回完整数据"""
        vehicle = VehicleConfig(
            vehicle_type=VehicleTypeEnum.CONTAINER_TRAILER,
            vehicle_model="测试集装箱拖车-TEST002",
            quantity=1,
            max_payload=30000,
            cargo_volume=67.5,
            max_speed=50,
            fuel_consumption=28.0,
            loading_time=20,
            unloading_time=18,
            maintenance_cycle=250,
            operational_status=VehicleStatusEnum.AVAILABLE
        )
        
        vehicle_dict = vehicle.to_dict()
        
        # 验证字典包含所有必要字段
        assert "vehicle_type" in vehicle_dict
        assert "vehicle_model" in vehicle_dict
        assert "quantity" in vehicle_dict
        assert "max_payload" in vehicle_dict
        assert "cargo_volume" in vehicle_dict
        assert "max_speed" in vehicle_dict
        assert "fuel_consumption" in vehicle_dict
        assert "operational_status" in vehicle_dict
        
        # 验证字典值正确
        assert vehicle_dict["vehicle_model"] == "测试集装箱拖车-TEST002"
        assert vehicle_dict["quantity"] == 1
        assert vehicle_dict["max_payload"] == 30000
    
    def test_vehicle_model_string_representation_contains_type_and_model(self):
        """测试运输车辆配置模型字符串表示包含类型和型号"""
        vehicle = VehicleConfig(
            vehicle_type=VehicleTypeEnum.CARGO_TRUCK,
            vehicle_model="测试货物运输车-TEST003",
            quantity=1,
            max_payload=15000,
            cargo_volume=35.0,
            max_speed=80,
            fuel_consumption=20.0,
            loading_time=10,
            unloading_time=8,
            maintenance_cycle=180,
            operational_status=VehicleStatusEnum.AVAILABLE
        )
        
        str_repr = str(vehicle)
        
        assert "CARGO_TRUCK" in str_repr
        assert "测试货物运输车-TEST003" in str_repr


class TestVehicleService:
    """运输车辆配置服务层测试类"""
    
    @pytest.fixture
    def sample_create_data(self) -> VehicleCreateSchema:
        """创建示例运输车辆配置数据fixture"""
        return VehicleCreateSchema(
            vehicle_type=VehicleTypeEnum.FLATBED_TRAILER,
            vehicle_model="服务测试平板拖车-SERVICE001",
            quantity=2,
            max_payload=22000,
            cargo_volume=42.0,
            loading_constraints={"max_height": 3.2, "max_width": 2.5},
            max_speed=65,
            fuel_consumption=24.0,
            maneuverability=0.88,
            loading_time=18,
            unloading_time=15,
            transfer_efficiency=0.90,
            maintenance_cycle=220,
            availability_rate=0.93,
            maintenance_info={"test": "service_data"},
            operational_status=VehicleStatusEnum.AVAILABLE,
            fault_statistics={"total_faults": 0},
            remarks="服务层测试用运输车辆配置"
        )
    
    async def test_create_vehicle_config_with_valid_data_returns_created_config(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: VehicleCreateSchema
    ):
        """测试使用有效数据创建运输车辆配置返回创建的配置"""
        service = VehicleService(get_test_database_session)
        
        result = await service.create_vehicle_config(sample_create_data)
        
        assert result is not None
        assert result.vehicle_type == sample_create_data.vehicle_type
        assert result.vehicle_model == sample_create_data.vehicle_model
        assert result.quantity == sample_create_data.quantity
        assert result.max_payload == sample_create_data.max_payload
        assert result.cargo_volume == sample_create_data.cargo_volume
        assert result.max_speed == sample_create_data.max_speed
        assert result.operational_status == sample_create_data.operational_status
        assert result.id is not None
        assert result.created_at is not None
    
    async def test_get_vehicle_config_by_id_with_existing_id_returns_config(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: VehicleCreateSchema
    ):
        """测试使用存在的ID获取运输车辆配置返回配置信息"""
        service = VehicleService(get_test_database_session)
        
        # 先创建运输车辆配置
        created_config = await service.create_vehicle_config(sample_create_data)
        config_id = created_config.id
        
        # 获取运输车辆配置
        result = await service.get_vehicle_config_by_id(config_id)
        
        assert result is not None
        assert result.id == config_id
        assert result.vehicle_model == sample_create_data.vehicle_model
    
    async def test_get_vehicle_config_by_id_with_non_existent_id_returns_none(
        self,
        get_test_database_session: AsyncSession
    ):
        """测试使用不存在的ID获取运输车辆配置返回None"""
        service = VehicleService(get_test_database_session)
        
        result = await service.get_vehicle_config_by_id("non-existent-id")
        
        assert result is None
    
    async def test_get_vehicle_config_list_with_filters_returns_filtered_results(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: VehicleCreateSchema
    ):
        """测试使用筛选条件获取运输车辆配置列表返回筛选结果"""
        service = VehicleService(get_test_database_session)
        
        # 创建测试运输车辆配置
        await service.create_vehicle_config(sample_create_data)
        
        # 获取筛选列表
        result = await service.get_vehicle_config_list(
            page=1,
            page_size=10,
            vehicle_type=VehicleTypeEnum.FLATBED_TRAILER,
            status=VehicleStatusEnum.AVAILABLE,
            search_keyword="服务测试"
        )
        
        assert result is not None
        assert result.total >= 1
        assert result.page == 1
        assert result.page_size == 10
        assert len(result.vehicle_configs) >= 1
        
        # 验证筛选结果
        for config in result.vehicle_configs:
            assert config.vehicle_type == VehicleTypeEnum.FLATBED_TRAILER
            assert config.operational_status == VehicleStatusEnum.AVAILABLE
    
    async def test_update_vehicle_config_with_valid_data_returns_updated_config(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: VehicleCreateSchema
    ):
        """测试使用有效数据更新运输车辆配置返回更新后的配置"""
        service = VehicleService(get_test_database_session)
        
        # 创建运输车辆配置
        created_config = await service.create_vehicle_config(sample_create_data)
        config_id = created_config.id
        
        # 更新运输车辆配置
        update_data = VehicleUpdateSchema(
            vehicle_model="更新后的服务测试平板拖车-SERVICE001-V2",
            quantity=3,
            fuel_consumption=22.0,
            operational_status=VehicleStatusEnum.MAINTENANCE,
            remarks="已更新的服务层测试用运输车辆配置"
        )
        
        result = await service.update_vehicle_config(config_id, update_data)
        
        assert result is not None
        assert result.id == config_id
        assert result.vehicle_model == "更新后的服务测试平板拖车-SERVICE001-V2"
        assert result.quantity == 3
        assert result.fuel_consumption == 22.0
        assert result.operational_status == VehicleStatusEnum.MAINTENANCE
        assert result.updated_at > result.created_at
    
    async def test_delete_vehicle_config_with_existing_id_returns_true(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: VehicleCreateSchema
    ):
        """测试删除存在的运输车辆配置返回True"""
        service = VehicleService(get_test_database_session)
        
        # 创建运输车辆配置
        created_config = await service.create_vehicle_config(sample_create_data)
        config_id = created_config.id
        
        # 删除运输车辆配置
        result = await service.delete_vehicle_config(config_id)
        
        assert result is True
        
        # 验证运输车辆配置已被删除
        deleted_config = await service.get_vehicle_config_by_id(config_id)
        assert deleted_config is None
    
    async def test_validate_vehicle_parameters_with_invalid_data_returns_errors(
        self,
        get_test_database_session: AsyncSession
    ):
        """测试验证无效运输车辆配置参数返回错误列表"""
        service = VehicleService(get_test_database_session)
        
        invalid_data = VehicleCreateSchema(
            vehicle_type=VehicleTypeEnum.FLATBED_TRAILER,
            vehicle_model="无效测试车辆",
            quantity=1,
            max_payload=1000,         # 小载重
            cargo_volume=50.0,        # 大容积，导致密度过低
            max_speed=150,            # 高速
            fuel_consumption=15.0,    # 低油耗，不匹配高速
            loading_time=2,           # 过短的装载时间
            unloading_time=1,         # 过短的卸载时间
            maintenance_cycle=50,     # 过短的维护周期
            availability_rate=0.3     # 过低的可用率
        )
        
        errors = await service.validate_vehicle_parameters(invalid_data)
        
        assert len(errors) > 0
        assert any("载重与容积比例不合理，密度过低" in error for error in errors)
        assert any("高速车辆的燃油消耗设置过低" in error for error in errors)
        assert any("装载时间不能少于5分钟" in error for error in errors)
        assert any("卸载时间不能少于3分钟" in error for error in errors)
        assert any("维护周期不能少于100小时" in error for error in errors)
        assert any("可用率不能低于50%" in error for error in errors)
    
    async def test_validate_vehicle_parameters_with_valid_data_returns_empty_errors(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: VehicleCreateSchema
    ):
        """测试验证有效运输车辆配置参数返回空错误列表"""
        service = VehicleService(get_test_database_session)
        
        errors = await service.validate_vehicle_parameters(sample_create_data)
        
        assert len(errors) == 0


class TestVehicleAPI:
    """运输车辆配置API接口测试类"""
    
    @pytest.fixture
    def sample_api_data(self) -> dict:
        """创建API测试数据fixture"""
        return {
            "vehicle_type": "flatbed_trailer",
            "vehicle_model": "API测试平板拖车-API001",
            "quantity": 2,
            "max_payload": 24000,
            "cargo_volume": 44.0,
            "loading_constraints": {"max_height": 3.3},
            "max_speed": 62,
            "fuel_consumption": 26.0,
            "maneuverability": 0.87,
            "loading_time": 16,
            "unloading_time": 13,
            "transfer_efficiency": 0.91,
            "maintenance_cycle": 210,
            "availability_rate": 0.94,
            "maintenance_info": {"test": "api_data"},
            "operational_status": "available",
            "fault_statistics": {"total_faults": 0},
            "remarks": "API接口测试用运输车辆配置"
        }
    
    async def test_create_vehicle_config_api_with_valid_data_returns_201_and_config(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试创建运输车辆配置API使用有效数据返回201和配置信息"""
        response = await get_test_client.post(
            "/api/v1/transport-vehicle-configs/",
            json=sample_api_data
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["vehicle_type"] == sample_api_data["vehicle_type"]
        assert data["vehicle_model"] == sample_api_data["vehicle_model"]
        assert data["quantity"] == sample_api_data["quantity"]
        assert data["max_payload"] == sample_api_data["max_payload"]
        assert "id" in data
        assert "created_at" in data
    
    async def test_get_vehicle_config_list_api_returns_200_and_paginated_list(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试获取运输车辆配置列表API返回200和分页列表"""
        # 先创建一个运输车辆配置
        await get_test_client.post("/api/v1/transport-vehicle-configs/", json=sample_api_data)
        
        # 获取运输车辆配置列表
        response = await get_test_client.get("/api/v1/transport-vehicle-configs/")
        
        assert response.status_code == 200
        data = response.json()
        assert "vehicle_configs" in data
        assert "total" in data
        assert "page" in data
        assert "page_size" in data
        assert "total_pages" in data
        assert isinstance(data["vehicle_configs"], list)
    
    async def test_get_vehicle_config_detail_api_with_valid_id_returns_200_and_config(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试获取运输车辆配置详情API使用有效ID返回200和配置信息"""
        # 先创建运输车辆配置
        create_response = await get_test_client.post(
            "/api/v1/transport-vehicle-configs/",
            json=sample_api_data
        )
        config_id = create_response.json()["id"]
        
        # 获取运输车辆配置详情
        response = await get_test_client.get(f"/api/v1/transport-vehicle-configs/{config_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == config_id
        assert data["vehicle_model"] == sample_api_data["vehicle_model"]
    
    async def test_update_vehicle_config_api_with_valid_data_returns_200_and_updated_config(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试更新运输车辆配置API使用有效数据返回200和更新后的配置"""
        # 先创建运输车辆配置
        create_response = await get_test_client.post(
            "/api/v1/transport-vehicle-configs/",
            json=sample_api_data
        )
        config_id = create_response.json()["id"]
        
        # 更新运输车辆配置
        update_data = {
            "vehicle_model": "更新后的API测试平板拖车-API001-V2",
            "quantity": 3,
            "fuel_consumption": 24.0
        }
        
        response = await get_test_client.put(
            f"/api/v1/transport-vehicle-configs/{config_id}",
            json=update_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["vehicle_model"] == update_data["vehicle_model"]
        assert data["quantity"] == update_data["quantity"]
        assert data["fuel_consumption"] == update_data["fuel_consumption"]
    
    async def test_delete_vehicle_config_api_with_valid_id_returns_204(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试删除运输车辆配置API使用有效ID返回204"""
        # 先创建运输车辆配置
        create_response = await get_test_client.post(
            "/api/v1/transport-vehicle-configs/",
            json=sample_api_data
        )
        config_id = create_response.json()["id"]
        
        # 删除运输车辆配置
        response = await get_test_client.delete(f"/api/v1/transport-vehicle-configs/{config_id}")
        
        assert response.status_code == 204
    
    async def test_create_vehicle_config_api_with_invalid_data_returns_400_with_errors(
        self,
        get_test_client: AsyncClient
    ):
        """测试创建运输车辆配置API使用无效数据返回400和错误信息"""
        invalid_data = {
            "vehicle_type": "flatbed_trailer",
            "vehicle_model": "无效API测试车辆",
            "quantity": 1,
            "max_payload": 500,       # 小载重
            "cargo_volume": 50.0,     # 大容积，密度过低
            "max_speed": 150,         # 高速
            "fuel_consumption": 10.0, # 低油耗
            "loading_time": 2,        # 过短时间
            "unloading_time": 1,      # 过短时间
            "maintenance_cycle": 50   # 过短周期
        }
        
        response = await get_test_client.post(
            "/api/v1/transport-vehicle-configs/",
            json=invalid_data
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "errors" in data["detail"]
    
    async def test_get_vehicle_config_detail_api_with_invalid_id_returns_404(
        self,
        get_test_client: AsyncClient
    ):
        """测试获取运输车辆配置详情API使用无效ID返回404"""
        response = await get_test_client.get("/api/v1/transport-vehicle-configs/invalid-id")
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "运输车辆配置不存在"
