"""
场景管理测试模块

遵循base-rules.md规范：
- 测试命名规范：test_方法名_条件_期望结果
- 测试独立性规则：每个测试用例必须独立运行
- 断言清晰性规则：使用具体的断言方法
"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.models.scenario import TaskTypeEnum, ThreatLevelEnum


class TestScenarioAPI:
    """场景API测试类"""
    
    @pytest.fixture
    def sample_scenario_data(self) -> dict:
        """
        创建示例场景数据
        
        Returns:
            dict: 示例场景数据
        """
        return {
            "scenario_name": "测试战略投送场景",
            "scenario_type": "strategic_transport",
            "description": "用于测试的战略投送场景",
            "task_type": TaskTypeEnum.EQUIPMENT_TRANSPORT,
            "environment_conditions": {
                "weather": {
                    "visibility": 10000,
                    "wind_speed": 15,
                    "precipitation": 0
                },
                "terrain": "plain",
                "airspace_control": {
                    "control_level": "normal"
                },
                "electromagnetic_environment": {
                    "interference_level": "low"
                },
                "airport_condition": {
                    "runway_length": 3500,
                    "runway_width": 45
                }
            },
            "resource_constraints": {
                "transport_capacity": {
                    "max_aircraft": 5
                },
                "support_resources": {
                    "fuel_capacity": 100000
                },
                "time_window": {
                    "max_duration": 24
                }
            },
            "mission_requirements": {
                "cargo_weight": 180000,
                "origin": "base_001",
                "destination": "base_002",
                "time_window": {
                    "start_time": "2025-08-01T06:00:00Z",
                    "end_time": "2025-08-01T18:00:00Z"
                },
                "priority": "high"
            },
            "threat_factors": {
                "enemy_threats": {
                    "air_defense": "medium"
                },
                "threat_probabilities": {
                    "air_defense_threat": 0.3
                },
                "countermeasures": ["electronic_warfare", "route_planning"]
            },
            "threat_level": ThreatLevelEnum.MEDIUM,
            "created_by": "test_user"
        }
    
    async def test_create_scenario_with_valid_data_returns_created_scenario(
        self,
        get_test_client: AsyncClient,
        sample_scenario_data: dict
    ) -> None:
        """测试使用有效数据创建场景返回创建的场景信息"""
        response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=sample_scenario_data
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["scenario_name"] == sample_scenario_data["scenario_name"]
        assert data["scenario_type"] == sample_scenario_data["scenario_type"]
        assert data["task_type"] == sample_scenario_data["task_type"]
        assert data["threat_level"] == sample_scenario_data["threat_level"]
        assert "id" in data
        assert "created_at" in data
        assert "updated_at" in data
    
    async def test_create_scenario_with_invalid_cargo_weight_returns_bad_request(
        self,
        get_test_client: AsyncClient,
        sample_scenario_data: dict
    ) -> None:
        """测试使用无效货物重量创建场景返回错误请求"""
        sample_scenario_data["mission_requirements"]["cargo_weight"] = -1000
        
        response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=sample_scenario_data
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "errors" in data["detail"]
    
    async def test_get_scenario_list_returns_paginated_scenarios(
        self,
        get_test_client: AsyncClient,
        sample_scenario_data: dict
    ) -> None:
        """测试获取场景列表返回分页的场景数据"""
        # 先创建一个场景
        create_response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=sample_scenario_data
        )
        assert create_response.status_code == 201
        
        # 获取场景列表
        response = await get_test_client.get("/api/v1/scenarios/")
        
        assert response.status_code == 200
        data = response.json()
        assert "scenarios" in data
        assert "total" in data
        assert "page" in data
        assert "page_size" in data
        assert "total_pages" in data
        assert len(data["scenarios"]) >= 1
    
    async def test_get_scenario_detail_with_valid_id_returns_scenario_info(
        self,
        get_test_client: AsyncClient,
        sample_scenario_data: dict
    ) -> None:
        """测试使用有效ID获取场景详情返回场景信息"""
        # 先创建一个场景
        create_response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=sample_scenario_data
        )
        assert create_response.status_code == 201
        scenario_id = create_response.json()["id"]
        
        # 获取场景详情
        response = await get_test_client.get(f"/api/v1/scenarios/{scenario_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == scenario_id
        assert data["scenario_name"] == sample_scenario_data["scenario_name"]
    
    async def test_get_scenario_detail_with_invalid_id_returns_not_found(
        self,
        get_test_client: AsyncClient
    ) -> None:
        """测试使用无效ID获取场景详情返回未找到错误"""
        response = await get_test_client.get("/api/v1/scenarios/invalid-id")
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "场景不存在"
    
    async def test_update_scenario_with_valid_data_returns_updated_scenario(
        self,
        get_test_client: AsyncClient,
        sample_scenario_data: dict
    ) -> None:
        """测试使用有效数据更新场景返回更新后的场景信息"""
        # 先创建一个场景
        create_response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=sample_scenario_data
        )
        assert create_response.status_code == 201
        scenario_id = create_response.json()["id"]
        
        # 更新场景
        update_data = {
            "scenario_name": "更新后的场景名称",
            "description": "更新后的描述"
        }
        
        response = await get_test_client.put(
            f"/api/v1/scenarios/{scenario_id}",
            json=update_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["scenario_name"] == update_data["scenario_name"]
        assert data["description"] == update_data["description"]
    
    async def test_delete_scenario_with_valid_id_returns_no_content(
        self,
        get_test_client: AsyncClient,
        sample_scenario_data: dict
    ) -> None:
        """测试使用有效ID删除场景返回无内容状态"""
        # 先创建一个场景
        create_response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=sample_scenario_data
        )
        assert create_response.status_code == 201
        scenario_id = create_response.json()["id"]
        
        # 删除场景
        response = await get_test_client.delete(f"/api/v1/scenarios/{scenario_id}")
        
        assert response.status_code == 204
        
        # 验证场景已被删除（软删除）
        get_response = await get_test_client.get(f"/api/v1/scenarios/{scenario_id}")
        # 软删除的场景仍然可以查询到，但状态为deleted
        assert get_response.status_code == 200
        data = get_response.json()
        assert data["status"] == "deleted"
    
    async def test_get_scenario_list_with_filters_returns_filtered_scenarios(
        self,
        get_test_client: AsyncClient,
        sample_scenario_data: dict
    ) -> None:
        """测试使用筛选条件获取场景列表返回筛选后的场景"""
        # 先创建一个场景
        create_response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=sample_scenario_data
        )
        assert create_response.status_code == 201
        
        # 使用筛选条件获取场景列表
        response = await get_test_client.get(
            "/api/v1/scenarios/",
            params={
                "scenario_type": "strategic_transport",
                "task_type": "equipment_transport"
            }
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["scenarios"]) >= 1
        
        # 验证筛选结果
        for scenario in data["scenarios"]:
            assert scenario["scenario_type"] == "strategic_transport"
            assert scenario["task_type"] == "equipment_transport"
