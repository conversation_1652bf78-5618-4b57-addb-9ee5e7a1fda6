"""
场景管理模块完整测试用例

包含数据模型、服务层和API层的全面测试
遵循base-rules.md规范：测试独立性、断言清晰性、命名规范
"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.models.scenario import (
    Scenario, 
    TaskTypeEnum, 
    ScenarioStatusEnum, 
    ThreatLevelEnum
)
from src.services.scenario import ScenarioService
from src.schemas.scenario import (
    ScenarioCreateSchema,
    ScenarioUpdateSchema,
    EnvironmentConditionsSchema,
    ResourceConstraintsSchema,
    MissionRequirementsSchema,
    ThreatFactorsSchema
)


class TestScenarioModel:
    """场景数据模型测试类"""
    
    def test_scenario_model_creation_with_valid_data_creates_instance(self):
        """测试使用有效数据创建场景模型实例"""
        # 准备测试数据
        scenario = Scenario(
            scenario_name="测试场景",
            scenario_type="test_scenario",
            description="测试用场景描述",
            task_type=TaskTypeEnum.EQUIPMENT_TRANSPORT,
            environment_conditions={"weather": {"visibility": 10000}},
            resource_constraints={"transport_capacity": {"max_aircraft": 5}},
            mission_requirements={"cargo_weight": 50000, "origin": "A", "destination": "B"},
            threat_factors={"enemy_threats": {"air_defense": "low"}},
            threat_level=ThreatLevelEnum.LOW,
            created_by="test_user",
            status=ScenarioStatusEnum.DRAFT
        )
        
        # 验证实例创建
        assert scenario.scenario_name == "测试场景"
        assert scenario.scenario_type == "test_scenario"
        assert scenario.task_type == TaskTypeEnum.EQUIPMENT_TRANSPORT
        assert scenario.threat_level == ThreatLevelEnum.LOW
        assert scenario.status == ScenarioStatusEnum.DRAFT
        assert scenario.created_by == "test_user"
    
    def test_scenario_model_to_dict_returns_complete_dictionary(self):
        """测试场景模型转换为字典返回完整数据"""
        scenario = Scenario(
            scenario_name="字典测试场景",
            scenario_type="dict_test",
            task_type=TaskTypeEnum.PERSONNEL_TRANSPORT,
            environment_conditions={"test": "data"},
            resource_constraints={"test": "data"},
            mission_requirements={"cargo_weight": 1000, "origin": "A", "destination": "B"},
            threat_factors={"test": "data"},
            threat_level=ThreatLevelEnum.MEDIUM
        )
        
        scenario_dict = scenario.to_dict()
        
        # 验证字典包含所有必要字段
        assert "scenario_name" in scenario_dict
        assert "scenario_type" in scenario_dict
        assert "task_type" in scenario_dict
        assert "environment_conditions" in scenario_dict
        assert "resource_constraints" in scenario_dict
        assert "mission_requirements" in scenario_dict
        assert "threat_factors" in scenario_dict
        assert "threat_level" in scenario_dict
        
        # 验证字典值正确
        assert scenario_dict["scenario_name"] == "字典测试场景"
        assert scenario_dict["scenario_type"] == "dict_test"
    
    def test_scenario_model_string_representation_contains_name_and_type(self):
        """测试场景模型字符串表示包含名称和类型"""
        scenario = Scenario(
            scenario_name="字符串测试场景",
            scenario_type="string_test",
            task_type=TaskTypeEnum.MATERIAL_SUPPLY,
            environment_conditions={},
            resource_constraints={},
            mission_requirements={"cargo_weight": 1000, "origin": "A", "destination": "B"},
            threat_factors={},
            threat_level=ThreatLevelEnum.LOW
        )
        
        str_repr = str(scenario)
        
        assert "字符串测试场景" in str_repr
        assert "string_test" in str_repr


class TestScenarioService:
    """场景服务层测试类"""
    
    @pytest.fixture
    def sample_create_data(self) -> ScenarioCreateSchema:
        """创建示例场景数据fixture"""
        return ScenarioCreateSchema(
            scenario_name="服务测试场景",
            scenario_type="service_test",
            description="服务层测试用场景",
            task_type=TaskTypeEnum.EQUIPMENT_TRANSPORT,
            environment_conditions=EnvironmentConditionsSchema(
                weather={"visibility": 10000, "wind_speed": 15},
                terrain="plain"
            ),
            resource_constraints=ResourceConstraintsSchema(
                transport_capacity={"max_aircraft": 3}
            ),
            mission_requirements=MissionRequirementsSchema(
                cargo_weight=80000,
                origin="service_test_A",
                destination="service_test_B"
            ),
            threat_factors=ThreatFactorsSchema(
                enemy_threats={"air_defense": "medium"}
            ),
            threat_level=ThreatLevelEnum.MEDIUM,
            created_by="service_tester"
        )
    
    async def test_create_scenario_with_valid_data_returns_created_scenario(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: ScenarioCreateSchema
    ):
        """测试使用有效数据创建场景返回创建的场景"""
        service = ScenarioService(get_test_database_session)
        
        result = await service.create_scenario(sample_create_data)
        
        assert result is not None
        assert result.scenario_name == sample_create_data.scenario_name
        assert result.scenario_type == sample_create_data.scenario_type
        assert result.task_type == sample_create_data.task_type
        assert result.threat_level == sample_create_data.threat_level
        assert result.created_by == sample_create_data.created_by
        assert result.status == ScenarioStatusEnum.DRAFT
        assert result.id is not None
        assert result.created_at is not None
    
    async def test_get_scenario_by_id_with_existing_id_returns_scenario(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: ScenarioCreateSchema
    ):
        """测试使用存在的ID获取场景返回场景信息"""
        service = ScenarioService(get_test_database_session)
        
        # 先创建场景
        created_scenario = await service.create_scenario(sample_create_data)
        scenario_id = created_scenario.id
        
        # 获取场景
        result = await service.get_scenario_by_id(scenario_id)
        
        assert result is not None
        assert result.id == scenario_id
        assert result.scenario_name == sample_create_data.scenario_name
    
    async def test_get_scenario_by_id_with_non_existent_id_returns_none(
        self,
        get_test_database_session: AsyncSession
    ):
        """测试使用不存在的ID获取场景返回None"""
        service = ScenarioService(get_test_database_session)
        
        result = await service.get_scenario_by_id("non-existent-id")
        
        assert result is None
    
    async def test_get_scenario_list_with_filters_returns_filtered_results(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: ScenarioCreateSchema
    ):
        """测试使用筛选条件获取场景列表返回筛选结果"""
        service = ScenarioService(get_test_database_session)
        
        # 创建测试场景
        await service.create_scenario(sample_create_data)
        
        # 获取筛选列表
        result = await service.get_scenario_list(
            page=1,
            page_size=10,
            scenario_type="service_test",
            task_type=TaskTypeEnum.EQUIPMENT_TRANSPORT.value,
            search_keyword="服务测试"
        )
        
        assert result is not None
        assert result.total >= 1
        assert result.page == 1
        assert result.page_size == 10
        assert len(result.scenarios) >= 1
        
        # 验证筛选结果
        for scenario in result.scenarios:
            assert scenario.scenario_type == "service_test"
            assert scenario.task_type == TaskTypeEnum.EQUIPMENT_TRANSPORT
    
    async def test_update_scenario_with_valid_data_returns_updated_scenario(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: ScenarioCreateSchema
    ):
        """测试使用有效数据更新场景返回更新后的场景"""
        service = ScenarioService(get_test_database_session)
        
        # 创建场景
        created_scenario = await service.create_scenario(sample_create_data)
        scenario_id = created_scenario.id
        
        # 更新场景
        update_data = ScenarioUpdateSchema(
            scenario_name="更新后的服务测试场景",
            description="已更新的描述",
            status=ScenarioStatusEnum.ACTIVE
        )
        
        result = await service.update_scenario(scenario_id, update_data)
        
        assert result is not None
        assert result.id == scenario_id
        assert result.scenario_name == "更新后的服务测试场景"
        assert result.description == "已更新的描述"
        assert result.status == ScenarioStatusEnum.ACTIVE
        assert result.updated_at > result.created_at
    
    async def test_delete_scenario_with_existing_id_returns_true_and_marks_deleted(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: ScenarioCreateSchema
    ):
        """测试删除存在的场景返回True并标记为删除"""
        service = ScenarioService(get_test_database_session)
        
        # 创建场景
        created_scenario = await service.create_scenario(sample_create_data)
        scenario_id = created_scenario.id
        
        # 删除场景
        result = await service.delete_scenario(scenario_id)
        
        assert result is True
        
        # 验证场景被标记为删除
        deleted_scenario = await service.get_scenario_by_id(scenario_id)
        assert deleted_scenario is not None
        assert deleted_scenario.status == ScenarioStatusEnum.DELETED
    
    async def test_validate_scenario_parameters_with_invalid_data_returns_errors(
        self,
        get_test_database_session: AsyncSession
    ):
        """测试验证无效场景参数返回错误列表"""
        service = ScenarioService(get_test_database_session)
        
        invalid_data = {
            "mission_requirements": {
                "cargo_weight": -1000,  # 无效的负重量
                "origin": "",           # 空的出发地
                "destination": "",      # 空的目的地
            }
        }
        
        errors = await service.validate_scenario_parameters(invalid_data)
        
        assert len(errors) > 0
        assert any("货物重量必须大于0" in error for error in errors)
        assert any("必须指定出发地" in error for error in errors)
        assert any("必须指定目的地" in error for error in errors)
    
    async def test_validate_scenario_parameters_with_valid_data_returns_empty_errors(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: ScenarioCreateSchema
    ):
        """测试验证有效场景参数返回空错误列表"""
        service = ScenarioService(get_test_database_session)
        
        errors = await service.validate_scenario_parameters(
            sample_create_data.model_dump()
        )
        
        assert len(errors) == 0


class TestScenarioAPI:
    """场景API接口测试类"""
    
    @pytest.fixture
    def sample_api_data(self) -> dict:
        """创建API测试数据fixture"""
        return {
            "scenario_name": "API测试场景",
            "scenario_type": "api_test",
            "description": "API接口测试用场景",
            "task_type": "equipment_transport",
            "environment_conditions": {
                "weather": {"visibility": 12000, "wind_speed": 12},
                "terrain": "mountain"
            },
            "resource_constraints": {
                "transport_capacity": {"max_aircraft": 4}
            },
            "mission_requirements": {
                "cargo_weight": 90000,
                "origin": "api_test_A",
                "destination": "api_test_B"
            },
            "threat_factors": {
                "enemy_threats": {"air_defense": "low"}
            },
            "threat_level": "low",
            "created_by": "api_tester"
        }
    
    async def test_create_scenario_api_with_valid_data_returns_201_and_scenario(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试创建场景API使用有效数据返回201和场景信息"""
        response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=sample_api_data
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["scenario_name"] == sample_api_data["scenario_name"]
        assert data["scenario_type"] == sample_api_data["scenario_type"]
        assert data["task_type"] == sample_api_data["task_type"]
        assert "id" in data
        assert "created_at" in data
    
    async def test_get_scenario_list_api_returns_200_and_paginated_list(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试获取场景列表API返回200和分页列表"""
        # 先创建一个场景
        await get_test_client.post("/api/v1/scenarios/", json=sample_api_data)
        
        # 获取场景列表
        response = await get_test_client.get("/api/v1/scenarios/")
        
        assert response.status_code == 200
        data = response.json()
        assert "scenarios" in data
        assert "total" in data
        assert "page" in data
        assert "page_size" in data
        assert "total_pages" in data
        assert isinstance(data["scenarios"], list)
    
    async def test_get_scenario_detail_api_with_valid_id_returns_200_and_scenario(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试获取场景详情API使用有效ID返回200和场景信息"""
        # 先创建场景
        create_response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=sample_api_data
        )
        scenario_id = create_response.json()["id"]
        
        # 获取场景详情
        response = await get_test_client.get(f"/api/v1/scenarios/{scenario_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == scenario_id
        assert data["scenario_name"] == sample_api_data["scenario_name"]
    
    async def test_update_scenario_api_with_valid_data_returns_200_and_updated_scenario(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试更新场景API使用有效数据返回200和更新后的场景"""
        # 先创建场景
        create_response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=sample_api_data
        )
        scenario_id = create_response.json()["id"]
        
        # 更新场景
        update_data = {
            "scenario_name": "更新后的API测试场景",
            "description": "已更新的API测试场景描述"
        }
        
        response = await get_test_client.put(
            f"/api/v1/scenarios/{scenario_id}",
            json=update_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["scenario_name"] == update_data["scenario_name"]
        assert data["description"] == update_data["description"]
    
    async def test_delete_scenario_api_with_valid_id_returns_204(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试删除场景API使用有效ID返回204"""
        # 先创建场景
        create_response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=sample_api_data
        )
        scenario_id = create_response.json()["id"]
        
        # 删除场景
        response = await get_test_client.delete(f"/api/v1/scenarios/{scenario_id}")
        
        assert response.status_code == 204
    
    async def test_create_scenario_api_with_invalid_data_returns_400_with_errors(
        self,
        get_test_client: AsyncClient
    ):
        """测试创建场景API使用无效数据返回400和错误信息"""
        invalid_data = {
            "scenario_name": "无效数据测试",
            "scenario_type": "invalid_test",
            "task_type": "equipment_transport",
            "mission_requirements": {
                "cargo_weight": -1000,  # 无效的负重量
                "origin": "",           # 空的出发地
                "destination": ""       # 空的目的地
            }
        }
        
        response = await get_test_client.post(
            "/api/v1/scenarios/",
            json=invalid_data
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "errors" in data["detail"]
    
    async def test_get_scenario_detail_api_with_invalid_id_returns_404(
        self,
        get_test_client: AsyncClient
    ):
        """测试获取场景详情API使用无效ID返回404"""
        response = await get_test_client.get("/api/v1/scenarios/invalid-id")
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "场景不存在"
