"""
测试配置模块

遵循base-rules.md规范：
- 测试独立性规则：每个测试用例必须独立运行
- 使用fixture来准备和清理测试环境
"""

import asyncio
from typing import AsyncGenerator, Generator
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from src.main import app
from src.database.connection import get_database_session, async_engine, DatabaseBase


@pytest.fixture(scope="session")
def event_loop() -> Generator[asyncio.AbstractEventLoop, None, None]:
    """
    创建事件循环fixture
    
    Yields:
        asyncio.AbstractEventLoop: 事件循环
    """
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
async def setup_test_database() -> AsyncGenerator[None, None]:
    """
    设置测试数据库
    
    Yields:
        None: 数据库设置完成
    """
    # 创建测试表
    async with async_engine.begin() as connection:
        await connection.run_sync(DatabaseBase.metadata.create_all)
    
    yield
    
    # 清理测试表
    async with async_engine.begin() as connection:
        await connection.run_sync(DatabaseBase.metadata.drop_all)


@pytest.fixture
async def get_test_database_session(
    setup_test_database: None,
) -> AsyncGenerator[AsyncSession, None]:
    """
    获取测试数据库会话
    
    Args:
        setup_test_database: 数据库设置fixture
        
    Yields:
        AsyncSession: 测试数据库会话
    """
    async with AsyncSession(async_engine) as session:
        yield session
        await session.rollback()


@pytest.fixture
async def get_test_client() -> AsyncGenerator[AsyncClient, None]:
    """
    获取测试客户端
    
    Yields:
        AsyncClient: HTTP测试客户端
    """
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client
