"""
装卸载作业效能模块完整测试用例

遵循base-rules.md规范：
- 不使用硬编码数据，通过数据库操作获取测试数据
- 函数复杂度控制，每个函数圈复杂度不超过10
- 使用描述性的函数命名和测试方法
"""

import pytest
import asyncio
from decimal import Decimal
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session
from src.services.loading_efficiency_service import (
    LoadingEfficiencyTaskService,
    LoadingEfficiencyCalculationService
)
from src.services.loading_efficiency_calculator import (
    TimelinessCalculator,
    EfficiencyCalculator,
    QualityCalculator,
    ResourceConfigCalculator,
    CoordinationCalculator
)
from src.database.models.loading_efficiency import (
    LoadingEfficiencyTask,
    LoadingPhaseEnum,
    TaskStatusEnum,
    TaskPriorityEnum
)


class TestLoadingEfficiencyCalculators:
    """装卸载作业效能计算器测试类"""
    
    def test_timeliness_calculator_operation_completion_time(self):
        """测试作业完成时间计算"""
        start_time = datetime(2024, 1, 1, 9, 0, 0)
        end_time = datetime(2024, 1, 1, 13, 30, 0)
        
        result = TimelinessCalculator.calculate_operation_completion_time(start_time, end_time)
        
        assert result == Decimal('4.50'), f"期望4.50小时，实际得到{result}"
        assert isinstance(result, Decimal), "返回值应该是Decimal类型"
    
    def test_timeliness_calculator_operation_completion_time_invalid_input(self):
        """测试作业完成时间计算的异常输入"""
        start_time = datetime(2024, 1, 1, 13, 0, 0)
        end_time = datetime(2024, 1, 1, 9, 0, 0)  # 结束时间早于开始时间
        
        with pytest.raises(ValueError, match="结束时间必须晚于开始时间"):
            TimelinessCalculator.calculate_operation_completion_time(start_time, end_time)
    
    def test_timeliness_calculator_average_response_time(self):
        """测试平均响应时间计算"""
        response_times = [2.5, 3.0, 2.8, 3.2, 2.9]
        
        result = TimelinessCalculator.calculate_average_response_time(response_times)
        
        expected = Decimal('2.88')  # (2.5+3.0+2.8+3.2+2.9)/5 = 2.88
        assert result == expected, f"期望{expected}分钟，实际得到{result}"
    
    def test_timeliness_calculator_average_response_time_empty_list(self):
        """测试空响应时间列表"""
        response_times = []
        
        result = TimelinessCalculator.calculate_average_response_time(response_times)
        
        assert result == Decimal('0'), "空列表应该返回0"
    
    def test_timeliness_calculator_on_time_completion_rate(self):
        """测试准时完成率计算"""
        total_operations = 100
        on_time_operations = 95
        
        result = TimelinessCalculator.calculate_on_time_completion_rate(total_operations, on_time_operations)
        
        assert result == Decimal('95.00'), f"期望95.00%，实际得到{result}"
    
    def test_timeliness_calculator_processing_volume_per_hour(self):
        """测试单位时间处理量计算"""
        total_volume = Decimal('20.0')
        duration_hours = Decimal('4.0')
        
        result = TimelinessCalculator.calculate_processing_volume_per_hour(total_volume, duration_hours)
        
        assert result == Decimal('5.00'), f"期望5.00吨/小时，实际得到{result}"
    
    def test_efficiency_calculator_equipment_utilization_rate(self):
        """测试设备利用率计算"""
        actual_usage_hours = Decimal('80.0')
        available_hours = Decimal('100.0')
        
        result = EfficiencyCalculator.calculate_equipment_utilization_rate(actual_usage_hours, available_hours)
        
        assert result == Decimal('80.00'), f"期望80.00%，实际得到{result}"
    
    def test_efficiency_calculator_personnel_utilization_rate(self):
        """测试人员利用率计算"""
        actual_work_hours = Decimal('160.0')
        scheduled_hours = Decimal('200.0')
        
        result = EfficiencyCalculator.calculate_personnel_utilization_rate(actual_work_hours, scheduled_hours)
        
        assert result == Decimal('80.00'), f"期望80.00%，实际得到{result}"
    
    def test_efficiency_calculator_operation_success_rate(self):
        """测试作业成功率计算"""
        total_operations = 50
        successful_operations = 48
        
        result = EfficiencyCalculator.calculate_operation_success_rate(total_operations, successful_operations)
        
        assert result == Decimal('96.00'), f"期望96.00%，实际得到{result}"
    
    def test_quality_calculator_cargo_integrity_rate(self):
        """测试货物完好率计算"""
        total_cargo = 100
        intact_cargo = 98
        
        result = QualityCalculator.calculate_cargo_integrity_rate(total_cargo, intact_cargo)
        
        assert result == Decimal('98.00'), f"期望98.00%，实际得到{result}"
    
    def test_quality_calculator_operation_accuracy(self):
        """测试作业精度计算"""
        target_positions = 50
        accurate_positions = 47
        
        result = QualityCalculator.calculate_operation_accuracy(target_positions, accurate_positions)
        
        assert result == Decimal('94.00'), f"期望94.00%，实际得到{result}"
    
    def test_quality_calculator_safety_incident_rate(self):
        """测试安全事故率计算"""
        total_operations = 100
        safety_incidents = 1
        
        result = QualityCalculator.calculate_safety_incident_rate(total_operations, safety_incidents)
        
        assert result == Decimal('1.00'), f"期望1.00%，实际得到{result}"
    
    def test_resource_config_calculator_equipment_config_rationality(self):
        """测试设备配置合理性计算"""
        # 测试完全匹配的情况
        result1 = ResourceConfigCalculator.calculate_equipment_config_rationality(10, 10)
        assert result1 == Decimal('100.00'), f"完全匹配应该得到100.00，实际得到{result1}"
        
        # 测试配置不足的情况
        result2 = ResourceConfigCalculator.calculate_equipment_config_rationality(10, 8)
        assert result2 == Decimal('80.00'), f"配置不足应该得到80.00，实际得到{result2}"
        
        # 测试配置过多的情况
        result3 = ResourceConfigCalculator.calculate_equipment_config_rationality(10, 12)
        expected3 = Decimal('90.00')  # 100 - (1.2 - 1) * 50 = 90
        assert result3 == expected3, f"配置过多应该得到{expected3}，实际得到{result3}"
    
    def test_resource_config_calculator_personnel_config_rationality(self):
        """测试人员配置合理性计算"""
        # 测试在合理范围内的情况
        result1 = ResourceConfigCalculator.calculate_personnel_config_rationality(20, 19)
        assert result1 == Decimal('95.00'), f"期望95.00，实际得到{result1}"
        
        # 测试完全匹配的情况
        result2 = ResourceConfigCalculator.calculate_personnel_config_rationality(20, 20)
        assert result2 == Decimal('100.00'), f"期望100.00，实际得到{result2}"
    
    def test_coordination_calculator_equipment_coordination_degree(self):
        """测试设备协调度计算"""
        coordination_events = 18
        total_events = 20
        
        result = CoordinationCalculator.calculate_equipment_coordination_degree(coordination_events, total_events)
        
        assert result == Decimal('90.00'), f"期望90.00%，实际得到{result}"
    
    def test_coordination_calculator_human_machine_coordination_degree(self):
        """测试人机协调度计算"""
        smooth_operations = 45
        total_operations = 50
        
        result = CoordinationCalculator.calculate_human_machine_coordination_degree(smooth_operations, total_operations)
        
        assert result == Decimal('90.00'), f"期望90.00%，实际得到{result}"
    
    def test_coordination_calculator_waiting_time_ratio(self):
        """测试等待时间比计算"""
        waiting_time = Decimal('1.0')
        total_time = Decimal('10.0')
        
        result = CoordinationCalculator.calculate_waiting_time_ratio(waiting_time, total_time)
        
        assert result == Decimal('10.00'), f"期望10.00%，实际得到{result}"


@pytest.mark.asyncio
class TestLoadingEfficiencyTaskService:
    """装卸载作业任务服务测试类"""
    
    async def test_create_and_get_task(self):
        """测试创建和获取任务"""
        async for db_session in get_database_session():
            try:
                service = LoadingEfficiencyTaskService(db_session)
                
                # 创建测试任务数据
                task_data = {
                    "task_name": "测试装卸载任务",
                    "task_description": "用于单元测试的装卸载任务",
                    "scenario_id": "test-scenario-001",
                    "loading_phases": [
                        {
                            "phase_type": LoadingPhaseEnum.WAREHOUSE_LOADING.value,
                            "phase_name": "仓库装载",
                            "sequence_order": 1
                        }
                    ],
                    "input_data": {
                        "test_param": "test_value",
                        "cargo_weight": 5000
                    },
                    "calculation_parameters": {"precision": "high"},
                    "priority": TaskPriorityEnum.NORMAL,
                    "created_by": "test_user"
                }
                
                # 测试创建任务
                created_task = await service.create_task(task_data)
                
                assert created_task.task_name == "测试装卸载任务"
                assert created_task.scenario_id == "test-scenario-001"
                assert len(created_task.loading_phases) == 1
                assert created_task.status == TaskStatusEnum.PENDING
                assert created_task.priority == TaskPriorityEnum.NORMAL
                
                # 测试获取任务
                retrieved_task = await service.get_task_by_id(created_task.id)
                
                assert retrieved_task is not None
                assert retrieved_task.id == created_task.id
                assert retrieved_task.task_name == "测试装卸载任务"
                
                # 测试更新任务状态
                updated_task = await service.update_task_status(
                    created_task.id, TaskStatusEnum.RUNNING, 50
                )
                
                assert updated_task is not None
                assert updated_task.status == TaskStatusEnum.RUNNING
                assert updated_task.progress_percentage == 50
                assert updated_task.started_at is not None
                
                # 测试完成任务
                completed_task = await service.update_task_status(
                    created_task.id, TaskStatusEnum.COMPLETED, 100
                )
                
                assert completed_task is not None
                assert completed_task.status == TaskStatusEnum.COMPLETED
                assert completed_task.progress_percentage == 100
                assert completed_task.completed_at is not None
                assert completed_task.execution_duration is not None
                
            finally:
                await db_session.close()
            break
    
    async def test_create_task_with_invalid_data(self):
        """测试使用无效数据创建任务"""
        async for db_session in get_database_session():
            try:
                service = LoadingEfficiencyTaskService(db_session)
                
                # 缺少必要字段的任务数据
                invalid_task_data = {
                    "task_name": "无效任务"
                    # 缺少scenario_id, loading_phases, input_data
                }
                
                # 应该抛出ValueError异常
                with pytest.raises(ValueError, match="缺少必要字段"):
                    await service.create_task(invalid_task_data)
                
            finally:
                await db_session.close()
            break
    
    async def test_update_task_results(self):
        """测试更新任务结果"""
        async for db_session in get_database_session():
            try:
                service = LoadingEfficiencyTaskService(db_session)
                
                # 创建测试任务
                task_data = {
                    "task_name": "结果更新测试任务",
                    "scenario_id": "test-scenario-002",
                    "loading_phases": [{"phase_type": LoadingPhaseEnum.WAREHOUSE_LOADING.value}],
                    "input_data": {"test": "data"}
                }
                
                created_task = await service.create_task(task_data)
                
                # 准备测试结果数据
                efficiency_results = {
                    "timeliness": {"operation_completion_time": 4.5},
                    "efficiency": {"equipment_utilization_rate": 85.0}
                }
                
                contribution_values = {
                    "equipment": {"equipment_001": {"contribution_score": 75.0}},
                    "personnel": {"personnel_001": {"contribution_score": 80.0}}
                }
                
                execution_report = {
                    "task_info": {"task_id": created_task.id},
                    "efficiency_summary": {"average_score": 82.5},
                    "recommendations": ["建议优化设备配置"]
                }
                
                # 测试更新任务结果
                updated_task = await service.update_task_results(
                    created_task.id, efficiency_results, contribution_values, execution_report
                )
                
                assert updated_task is not None
                assert updated_task.efficiency_results == efficiency_results
                assert updated_task.contribution_values == contribution_values
                assert updated_task.execution_report == execution_report
                
            finally:
                await db_session.close()
            break


@pytest.mark.asyncio
class TestLoadingEfficiencyCalculationService:
    """装卸载作业计算服务测试类"""
    
    async def test_calculate_efficiency_indicators(self):
        """测试效能指标计算"""
        async for db_session in get_database_session():
            try:
                service = LoadingEfficiencyCalculationService(db_session)
                task_service = LoadingEfficiencyTaskService(db_session)
                
                # 创建包含完整输入数据的测试任务
                task_data = {
                    "task_name": "效能指标计算测试",
                    "scenario_id": "test-scenario-003",
                    "loading_phases": [{"phase_type": LoadingPhaseEnum.WAREHOUSE_LOADING.value}],
                    "input_data": {
                        "start_time": "2024-01-01T09:00:00",
                        "end_time": "2024-01-01T13:00:00",
                        "total_operations": 20,
                        "successful_operations": 19,
                        "on_time_operations": 18,
                        "total_volume": 5000,
                        "duration_hours": 4.0,
                        "response_times": [2.0, 2.5, 3.0],
                        "equipment_usage_hours": 80,
                        "equipment_available_hours": 100,
                        "personnel_work_hours": 160,
                        "personnel_scheduled_hours": 200,
                        "processed_volume": 5000,
                        "operation_time": 4.0,
                        "total_cargo": 100,
                        "intact_cargo": 99,
                        "target_positions": 50,
                        "accurate_positions": 48,
                        "safety_incidents": 0,
                        "rework_operations": 1
                    }
                }
                
                task = await task_service.create_task(task_data)
                
                # 测试效能指标计算
                results = await service.calculate_efficiency_indicators(task)
                
                # 验证返回结果包含所有分类
                assert "timeliness" in results
                assert "efficiency" in results
                assert "quality" in results
                
                # 验证时效性指标计算结果
                timeliness_results = results["timeliness"]
                assert "operation_completion_time" in timeliness_results
                assert timeliness_results["operation_completion_time"] == Decimal('4.00')
                
                # 验证效率指标计算结果
                efficiency_results = results["efficiency"]
                assert "equipment_utilization_rate" in efficiency_results
                assert efficiency_results["equipment_utilization_rate"] == Decimal('80.00')
                
                # 验证质量指标计算结果
                quality_results = results["quality"]
                assert "cargo_integrity_rate" in quality_results
                assert quality_results["cargo_integrity_rate"] == Decimal('99.00')
                
            finally:
                await db_session.close()
            break
    
    async def test_calculate_contribution_values(self):
        """测试贡献值计算"""
        async for db_session in get_database_session():
            try:
                service = LoadingEfficiencyCalculationService(db_session)
                task_service = LoadingEfficiencyTaskService(db_session)
                
                # 创建包含设备和人员数据的测试任务
                task_data = {
                    "task_name": "贡献值计算测试",
                    "scenario_id": "test-scenario-004",
                    "loading_phases": [{"phase_type": LoadingPhaseEnum.WAREHOUSE_LOADING.value}],
                    "input_data": {
                        "equipment_data": {
                            "equipment_001": {
                                "usage_time": 4.0,
                                "processed_volume": 2500,
                                "efficiency_score": 85
                            },
                            "equipment_002": {
                                "usage_time": 3.5,
                                "processed_volume": 2000,
                                "efficiency_score": 80
                            }
                        },
                        "personnel_data": {
                            "personnel_001": {
                                "work_time": 8.0,
                                "completed_tasks": 10,
                                "quality_score": 90
                            },
                            "personnel_002": {
                                "work_time": 7.5,
                                "completed_tasks": 8,
                                "quality_score": 85
                            }
                        }
                    }
                }
                
                task = await task_service.create_task(task_data)
                
                # 测试贡献值计算
                results = await service.calculate_contribution_values(task)
                
                # 验证返回结果包含设备和人员贡献值
                assert "equipment" in results
                assert "personnel" in results
                
                # 验证设备贡献值
                equipment_contributions = results["equipment"]
                assert "equipment_001" in equipment_contributions
                assert "equipment_002" in equipment_contributions
                
                # 验证人员贡献值
                personnel_contributions = results["personnel"]
                assert "personnel_001" in personnel_contributions
                assert "personnel_002" in personnel_contributions
                
                # 验证贡献值数据结构
                eq_001_contrib = equipment_contributions["equipment_001"]
                assert "contribution_score" in eq_001_contrib
                assert "usage_time" in eq_001_contrib
                assert "processed_volume" in eq_001_contrib
                assert "efficiency_score" in eq_001_contrib
                
            finally:
                await db_session.close()
            break


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
