"""
效能指标模块完整测试用例

遵循base-rules.md规范：
- 测试覆盖率要求：单元测试覆盖率必须达到90%以上
- 测试命名规范：test_方法名_条件_期望结果
- 测试独立性：每个测试用例独立运行，不依赖其他测试
- 断言清晰性：使用具体的断言方法，添加描述性错误消息
"""

import pytest
import asyncio
from decimal import Decimal
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.models.efficiency import (
    EfficiencyIndicator,
    IndicatorCalculationResult,
    IndicatorWeightConfig,
    IndicatorCategoryEnum,
    IndicatorStatusEnum,
    CalculationStatusEnum
)
from src.database.models.scenario import Scenario, TaskTypeEnum, ScenarioStatusEnum, ThreatLevelEnum
from src.services.efficiency import (
    EfficiencyIndicatorService,
    IndicatorCalculationService,
    IndicatorWeightConfigService
)
from src.services.efficiency_calculator import (
    TimelinessCalculator,
    CapabilityCalculator,
    EconomyCalculator,
    RobustnessCalculator,
    SafetyCalculator
)
from src.services.efficiency_evaluator import EfficiencyEvaluator
from src.schemas.efficiency import (
    EfficiencyIndicatorCreateSchema,
    EfficiencyIndicatorUpdateSchema,
    IndicatorWeightConfigCreateSchema
)


class TestEfficiencyIndicatorService:
    """效能指标服务测试类"""
    
    @pytest.mark.asyncio
    async def test_create_indicator_with_valid_data_returns_indicator(self, db_session: AsyncSession):
        """测试创建效能指标：使用有效数据应返回指标信息"""
        service = EfficiencyIndicatorService(db_session)
        
        indicator_data = EfficiencyIndicatorCreateSchema(
            indicator_name="测试指标",
            indicator_code="TEST_INDICATOR",
            category=IndicatorCategoryEnum.TIMELINESS,
            description="测试用效能指标",
            unit="小时",
            default_weight=Decimal('0.2000'),
            priority_level=1,
            created_by="test_user"
        )
        
        result = await service.create_indicator(indicator_data)
        
        assert result.indicator_name == "测试指标", "指标名称应该正确设置"
        assert result.indicator_code == "TEST_INDICATOR", "指标编码应该正确设置"
        assert result.category == IndicatorCategoryEnum.TIMELINESS, "指标分类应该正确设置"
        assert result.status == IndicatorStatusEnum.ACTIVE, "新创建的指标应该是激活状态"
    
    @pytest.mark.asyncio
    async def test_create_indicator_with_duplicate_name_raises_error(self, db_session: AsyncSession):
        """测试创建效能指标：重复名称应抛出错误"""
        service = EfficiencyIndicatorService(db_session)
        
        # 先创建一个指标
        indicator_data = EfficiencyIndicatorCreateSchema(
            indicator_name="重复指标",
            indicator_code="DUPLICATE_INDICATOR",
            category=IndicatorCategoryEnum.CAPABILITY,
            unit="百分比"
        )
        await service.create_indicator(indicator_data)
        
        # 尝试创建同名指标
        duplicate_data = EfficiencyIndicatorCreateSchema(
            indicator_name="重复指标",
            indicator_code="DUPLICATE_INDICATOR_2",
            category=IndicatorCategoryEnum.ECONOMY,
            unit="元"
        )
        
        with pytest.raises(ValueError, match="指标名称或编码已存在"):
            await service.create_indicator(duplicate_data)
    
    @pytest.mark.asyncio
    async def test_get_indicators_by_category_returns_filtered_list(self, db_session: AsyncSession):
        """测试按分类获取指标：应返回过滤后的指标列表"""
        service = EfficiencyIndicatorService(db_session)
        
        # 创建不同分类的指标
        timeliness_data = EfficiencyIndicatorCreateSchema(
            indicator_name="时效性指标",
            indicator_code="TIMELINESS_TEST",
            category=IndicatorCategoryEnum.TIMELINESS,
            unit="小时"
        )
        capability_data = EfficiencyIndicatorCreateSchema(
            indicator_name="能力指标",
            indicator_code="CAPABILITY_TEST",
            category=IndicatorCategoryEnum.CAPABILITY,
            unit="百分比"
        )
        
        await service.create_indicator(timeliness_data)
        await service.create_indicator(capability_data)
        
        # 获取时效性指标
        timeliness_indicators = await service.get_indicators_by_category(IndicatorCategoryEnum.TIMELINESS)
        
        assert len(timeliness_indicators) >= 1, "应该返回至少一个时效性指标"
        for indicator in timeliness_indicators:
            assert indicator.category == IndicatorCategoryEnum.TIMELINESS, "所有返回的指标都应该是时效性分类"
    
    @pytest.mark.asyncio
    async def test_update_indicator_with_valid_data_returns_updated_indicator(self, db_session: AsyncSession):
        """测试更新效能指标：使用有效数据应返回更新后的指标"""
        service = EfficiencyIndicatorService(db_session)
        
        # 先创建一个指标
        create_data = EfficiencyIndicatorCreateSchema(
            indicator_name="待更新指标",
            indicator_code="UPDATE_TEST",
            category=IndicatorCategoryEnum.SAFETY,
            unit="次数"
        )
        created_indicator = await service.create_indicator(create_data)
        
        # 更新指标
        update_data = EfficiencyIndicatorUpdateSchema(
            description="更新后的描述",
            target_value=Decimal('50.00'),
            priority_level=2
        )
        
        updated_indicator = await service.update_indicator(created_indicator.id, update_data)
        
        assert updated_indicator is not None, "更新应该成功"
        assert updated_indicator.description == "更新后的描述", "描述应该被更新"
        assert updated_indicator.target_value == Decimal('50.00'), "目标值应该被更新"
        assert updated_indicator.priority_level == 2, "优先级应该被更新"
    
    @pytest.mark.asyncio
    async def test_delete_indicator_sets_status_to_deprecated(self, db_session: AsyncSession):
        """测试删除效能指标：应将状态设置为已弃用"""
        service = EfficiencyIndicatorService(db_session)
        
        # 先创建一个指标
        create_data = EfficiencyIndicatorCreateSchema(
            indicator_name="待删除指标",
            indicator_code="DELETE_TEST",
            category=IndicatorCategoryEnum.ROBUSTNESS,
            unit="指数"
        )
        created_indicator = await service.create_indicator(create_data)
        
        # 删除指标
        success = await service.delete_indicator(created_indicator.id)
        
        assert success is True, "删除操作应该成功"
        
        # 验证指标状态已更改
        deleted_indicator = await service.get_indicator_by_id(created_indicator.id)
        assert deleted_indicator.status == IndicatorStatusEnum.DEPRECATED, "指标状态应该被设置为已弃用"


class TestEfficiencyCalculators:
    """效能指标计算器测试类"""
    
    def test_calculate_task_completion_time_with_valid_times_returns_duration(self):
        """测试任务完成时间计算：使用有效时间应返回持续时间"""
        start_time = datetime(2024, 1, 1, 10, 0, 0)
        end_time = datetime(2024, 1, 1, 14, 30, 0)
        
        result = TimelinessCalculator.calculate_task_completion_time(start_time, end_time)
        
        assert result == Decimal('4.50'), "4.5小时的任务应该返回4.50"
    
    def test_calculate_task_completion_time_with_invalid_times_raises_error(self):
        """测试任务完成时间计算：无效时间应抛出错误"""
        start_time = datetime(2024, 1, 1, 14, 0, 0)
        end_time = datetime(2024, 1, 1, 10, 0, 0)  # 结束时间早于开始时间
        
        with pytest.raises(ValueError, match="结束时间必须晚于开始时间"):
            TimelinessCalculator.calculate_task_completion_time(start_time, end_time)
    
    def test_calculate_on_time_delivery_rate_with_valid_data_returns_percentage(self):
        """测试准时交付率计算：使用有效数据应返回百分比"""
        total_tasks = 100
        on_time_tasks = 95
        
        result = TimelinessCalculator.calculate_on_time_delivery_rate(total_tasks, on_time_tasks)
        
        assert result == Decimal('95.00'), "95/100的准时率应该返回95.00%"
    
    def test_calculate_mission_success_rate_with_zero_missions_returns_zero(self):
        """测试任务达成率计算：零任务数应返回零"""
        result = CapabilityCalculator.calculate_mission_success_rate(0, 0)
        
        assert result == Decimal('0'), "零任务数应该返回0%"
    
    def test_calculate_fuel_efficiency_with_valid_data_returns_efficiency(self):
        """测试燃油效率计算：使用有效数据应返回效率值"""
        transport_volume = Decimal('1000')  # 1000吨
        fuel_consumption = Decimal('500')   # 500升
        
        result = EconomyCalculator.calculate_fuel_efficiency(transport_volume, fuel_consumption)
        
        assert result == Decimal('2.00'), "1000吨/500升应该返回2.00吨/升"
    
    def test_calculate_damage_resistance_with_valid_data_returns_percentage(self):
        """测试抗毁伤能力计算：使用有效数据应返回百分比"""
        original_capacity = Decimal('1000')
        damaged_capacity = Decimal('750')
        
        result = RobustnessCalculator.calculate_damage_resistance(damaged_capacity, original_capacity)
        
        assert result == Decimal('75.00'), "750/1000的抗毁伤能力应该返回75.00%"
    
    def test_calculate_loss_rate_with_valid_data_returns_percentage(self):
        """测试损失率计算：使用有效数据应返回百分比"""
        lost_volume = Decimal('5')
        total_volume = Decimal('1000')
        
        result = SafetyCalculator.calculate_loss_rate(lost_volume, total_volume)
        
        assert result == Decimal('0.50'), "5/1000的损失率应该返回0.50%"


class TestIndicatorWeightConfigService:
    """指标权重配置服务测试类"""
    
    @pytest.mark.asyncio
    async def test_create_weight_config_with_valid_weights_returns_config(self, db_session: AsyncSession):
        """测试创建权重配置：使用有效权重应返回配置信息"""
        service = IndicatorWeightConfigService(db_session)
        
        config_data = IndicatorWeightConfigCreateSchema(
            config_name="测试权重配置",
            scenario_type="test_scenario",
            description="测试用权重配置",
            weight_settings={
                "indicator1": Decimal('0.3'),
                "indicator2": Decimal('0.3'),
                "indicator3": Decimal('0.4')
            },
            is_default=True,
            created_by="test_user"
        )
        
        result = await service.create_weight_config(config_data)
        
        assert result.config_name == "测试权重配置", "配置名称应该正确设置"
        assert result.scenario_type == "test_scenario", "场景类型应该正确设置"
        assert result.is_default is True, "默认配置标志应该正确设置"
    
    @pytest.mark.asyncio
    async def test_create_weight_config_with_invalid_weights_raises_error(self, db_session: AsyncSession):
        """测试创建权重配置：无效权重总和应抛出错误"""
        service = IndicatorWeightConfigService(db_session)
        
        config_data = IndicatorWeightConfigCreateSchema(
            config_name="无效权重配置",
            scenario_type="invalid_scenario",
            weight_settings={
                "indicator1": Decimal('0.3'),
                "indicator2": Decimal('0.3'),
                "indicator3": Decimal('0.3')  # 总和为0.9，不等于1.0
            }
        )
        
        with pytest.raises(ValueError, match="权重总和必须等于1.0"):
            await service.create_weight_config(config_data)


class TestEfficiencyEvaluator:
    """效能综合评估器测试类"""
    
    @pytest.mark.asyncio
    async def test_calculate_weighted_score_with_valid_data_returns_score(self, db_session: AsyncSession):
        """测试加权评分计算：使用有效数据应返回评分"""
        evaluator = EfficiencyEvaluator(db_session)
        
        indicator_values = {
            "indicator1": Decimal('80'),
            "indicator2": Decimal('90'),
            "indicator3": Decimal('70')
        }
        
        weights = {
            "indicator1": Decimal('0.3'),
            "indicator2": Decimal('0.4'),
            "indicator3": Decimal('0.3')
        }
        
        result = evaluator.calculate_weighted_score(indicator_values, weights)
        
        # 预期结果：(80*0.3 + 90*0.4 + 70*0.3) / 1.0 = 81.0
        assert result == Decimal('81.00'), "加权评分计算结果应该正确"


# 测试夹具
@pytest.fixture
async def db_session():
    """数据库会话夹具"""
    from src.database.connection import get_database_session
    
    async for session in get_database_session():
        yield session
        await session.rollback()  # 回滚测试数据
        break
