"""
设备配置管理模块完整测试用例

包含数据模型、服务层和API层的全面测试
遵循base-rules.md规范：测试独立性、断言清晰性、命名规范
"""

import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.models.equipment import (
    EquipmentConfig,
    EquipmentTypeEnum,
    EquipmentStatusEnum
)
from src.services.equipment import EquipmentService
from src.schemas.equipment import (
    EquipmentCreateSchema,
    EquipmentUpdateSchema
)


class TestEquipmentModel:
    """设备配置数据模型测试类"""
    
    def test_equipment_model_creation_with_valid_data_creates_instance(self):
        """测试使用有效数据创建设备配置模型实例"""
        # 准备测试数据
        equipment = EquipmentConfig(
            equipment_type=EquipmentTypeEnum.LOADING_VEHICLE,
            equipment_model="测试装载车-TEST001",
            quantity=2,
            max_load_capacity=20000,
            loading_speed=5000,
            unloading_speed=6000,
            operation_radius=100,
            power_consumption=80,
            unit_operation_duration=1.5,
            efficiency_factor=0.88,
            maintenance_interval=250,
            operational_status=EquipmentStatusEnum.AVAILABLE,
            maintenance_info={"last_maintenance": "2025-07-01"},
            remarks="测试用设备配置"
        )
        
        # 验证实例创建
        assert equipment.equipment_type == EquipmentTypeEnum.LOADING_VEHICLE
        assert equipment.equipment_model == "测试装载车-TEST001"
        assert equipment.quantity == 2
        assert equipment.max_load_capacity == 20000
        assert equipment.loading_speed == 5000
        assert equipment.unloading_speed == 6000
        assert equipment.efficiency_factor == 0.88
        assert equipment.operational_status == EquipmentStatusEnum.AVAILABLE
    
    def test_equipment_model_to_dict_returns_complete_dictionary(self):
        """测试设备配置模型转换为字典返回完整数据"""
        equipment = EquipmentConfig(
            equipment_type=EquipmentTypeEnum.HANDLING_EQUIPMENT,
            equipment_model="测试叉车-TEST002",
            quantity=3,
            max_load_capacity=3000,
            loading_speed=1500,
            unloading_speed=2000,
            operation_radius=50,
            unit_operation_duration=0.8,
            efficiency_factor=0.85,
            maintenance_interval=150,
            operational_status=EquipmentStatusEnum.AVAILABLE
        )
        
        equipment_dict = equipment.to_dict()
        
        # 验证字典包含所有必要字段
        assert "equipment_type" in equipment_dict
        assert "equipment_model" in equipment_dict
        assert "quantity" in equipment_dict
        assert "max_load_capacity" in equipment_dict
        assert "loading_speed" in equipment_dict
        assert "unloading_speed" in equipment_dict
        assert "efficiency_factor" in equipment_dict
        assert "operational_status" in equipment_dict
        
        # 验证字典值正确
        assert equipment_dict["equipment_model"] == "测试叉车-TEST002"
        assert equipment_dict["quantity"] == 3
        assert equipment_dict["max_load_capacity"] == 3000
    
    def test_equipment_model_string_representation_contains_type_and_model(self):
        """测试设备配置模型字符串表示包含类型和型号"""
        equipment = EquipmentConfig(
            equipment_type=EquipmentTypeEnum.SUPPORT_EQUIPMENT,
            equipment_model="测试牵引车-TEST003",
            quantity=1,
            max_load_capacity=50000,
            loading_speed=8000,
            unloading_speed=10000,
            operation_radius=200,
            unit_operation_duration=2.0,
            efficiency_factor=0.90,
            maintenance_interval=400,
            operational_status=EquipmentStatusEnum.AVAILABLE
        )
        
        str_repr = str(equipment)
        
        assert "SUPPORT_EQUIPMENT" in str_repr
        assert "测试牵引车-TEST003" in str_repr


class TestEquipmentService:
    """设备配置服务层测试类"""
    
    @pytest.fixture
    def sample_create_data(self) -> EquipmentCreateSchema:
        """创建示例设备配置数据fixture"""
        return EquipmentCreateSchema(
            equipment_type=EquipmentTypeEnum.LOADING_VEHICLE,
            equipment_model="服务测试装载车-SERVICE001",
            quantity=2,
            max_load_capacity=18000,
            loading_speed=4500,
            unloading_speed=5500,
            operation_radius=120,
            power_consumption=90,
            unit_operation_duration=1.3,
            efficiency_factor=0.87,
            maintenance_interval=280,
            operational_status=EquipmentStatusEnum.AVAILABLE,
            maintenance_info={"test": "data"},
            remarks="服务层测试用设备配置"
        )
    
    async def test_create_equipment_config_with_valid_data_returns_created_config(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: EquipmentCreateSchema
    ):
        """测试使用有效数据创建设备配置返回创建的配置"""
        service = EquipmentService(get_test_database_session)
        
        result = await service.create_equipment_config(sample_create_data)
        
        assert result is not None
        assert result.equipment_type == sample_create_data.equipment_type
        assert result.equipment_model == sample_create_data.equipment_model
        assert result.quantity == sample_create_data.quantity
        assert result.max_load_capacity == sample_create_data.max_load_capacity
        assert result.loading_speed == sample_create_data.loading_speed
        assert result.efficiency_factor == sample_create_data.efficiency_factor
        assert result.operational_status == sample_create_data.operational_status
        assert result.id is not None
        assert result.created_at is not None
    
    async def test_get_equipment_config_by_id_with_existing_id_returns_config(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: EquipmentCreateSchema
    ):
        """测试使用存在的ID获取设备配置返回配置信息"""
        service = EquipmentService(get_test_database_session)
        
        # 先创建设备配置
        created_config = await service.create_equipment_config(sample_create_data)
        config_id = created_config.id
        
        # 获取设备配置
        result = await service.get_equipment_config_by_id(config_id)
        
        assert result is not None
        assert result.id == config_id
        assert result.equipment_model == sample_create_data.equipment_model
    
    async def test_get_equipment_config_by_id_with_non_existent_id_returns_none(
        self,
        get_test_database_session: AsyncSession
    ):
        """测试使用不存在的ID获取设备配置返回None"""
        service = EquipmentService(get_test_database_session)
        
        result = await service.get_equipment_config_by_id("non-existent-id")
        
        assert result is None
    
    async def test_get_equipment_config_list_with_filters_returns_filtered_results(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: EquipmentCreateSchema
    ):
        """测试使用筛选条件获取设备配置列表返回筛选结果"""
        service = EquipmentService(get_test_database_session)
        
        # 创建测试设备配置
        await service.create_equipment_config(sample_create_data)
        
        # 获取筛选列表
        result = await service.get_equipment_config_list(
            page=1,
            page_size=10,
            equipment_type=EquipmentTypeEnum.LOADING_VEHICLE,
            status=EquipmentStatusEnum.AVAILABLE,
            search_keyword="服务测试"
        )
        
        assert result is not None
        assert result.total >= 1
        assert result.page == 1
        assert result.page_size == 10
        assert len(result.equipment_configs) >= 1
        
        # 验证筛选结果
        for config in result.equipment_configs:
            assert config.equipment_type == EquipmentTypeEnum.LOADING_VEHICLE
            assert config.operational_status == EquipmentStatusEnum.AVAILABLE
    
    async def test_update_equipment_config_with_valid_data_returns_updated_config(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: EquipmentCreateSchema
    ):
        """测试使用有效数据更新设备配置返回更新后的配置"""
        service = EquipmentService(get_test_database_session)
        
        # 创建设备配置
        created_config = await service.create_equipment_config(sample_create_data)
        config_id = created_config.id
        
        # 更新设备配置
        update_data = EquipmentUpdateSchema(
            equipment_model="更新后的服务测试装载车-SERVICE001-V2",
            quantity=3,
            efficiency_factor=0.92,
            operational_status=EquipmentStatusEnum.MAINTENANCE,
            remarks="已更新的服务层测试用设备配置"
        )
        
        result = await service.update_equipment_config(config_id, update_data)
        
        assert result is not None
        assert result.id == config_id
        assert result.equipment_model == "更新后的服务测试装载车-SERVICE001-V2"
        assert result.quantity == 3
        assert result.efficiency_factor == 0.92
        assert result.operational_status == EquipmentStatusEnum.MAINTENANCE
        assert result.updated_at > result.created_at
    
    async def test_delete_equipment_config_with_existing_id_returns_true(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: EquipmentCreateSchema
    ):
        """测试删除存在的设备配置返回True"""
        service = EquipmentService(get_test_database_session)
        
        # 创建设备配置
        created_config = await service.create_equipment_config(sample_create_data)
        config_id = created_config.id
        
        # 删除设备配置
        result = await service.delete_equipment_config(config_id)
        
        assert result is True
        
        # 验证设备配置已被删除
        deleted_config = await service.get_equipment_config_by_id(config_id)
        assert deleted_config is None
    
    async def test_validate_equipment_parameters_with_invalid_data_returns_errors(
        self,
        get_test_database_session: AsyncSession
    ):
        """测试验证无效设备配置参数返回错误列表"""
        service = EquipmentService(get_test_database_session)
        
        invalid_data = EquipmentCreateSchema(
            equipment_type=EquipmentTypeEnum.LOADING_VEHICLE,
            equipment_model="无效测试设备",
            quantity=1,
            max_load_capacity=500,    # 小载重
            loading_speed=15000,      # 过高的装载速度
            unloading_speed=18000,
            operation_radius=-10,     # 无效的负半径
            unit_operation_duration=1.0,
            efficiency_factor=1.5,    # 无效的效率系数（>1）
            maintenance_interval=10   # 过短的维护间隔
        )
        
        errors = await service.validate_equipment_parameters(invalid_data)
        
        assert len(errors) > 0
        assert any("小型设备的装载速度设置过高" in error for error in errors)
        assert any("效率系数必须在0-1之间" in error for error in errors)
        assert any("维护间隔不能少于24小时" in error for error in errors)
        assert any("作业半径必须大于0" in error for error in errors)
    
    async def test_validate_equipment_parameters_with_valid_data_returns_empty_errors(
        self,
        get_test_database_session: AsyncSession,
        sample_create_data: EquipmentCreateSchema
    ):
        """测试验证有效设备配置参数返回空错误列表"""
        service = EquipmentService(get_test_database_session)
        
        errors = await service.validate_equipment_parameters(sample_create_data)
        
        assert len(errors) == 0


class TestEquipmentAPI:
    """设备配置API接口测试类"""
    
    @pytest.fixture
    def sample_api_data(self) -> dict:
        """创建API测试数据fixture"""
        return {
            "equipment_type": "loading_vehicle",
            "equipment_model": "API测试装载车-API001",
            "quantity": 2,
            "max_load_capacity": 16000,
            "loading_speed": 4000,
            "unloading_speed": 5000,
            "operation_radius": 110,
            "power_consumption": 85,
            "unit_operation_duration": 1.4,
            "efficiency_factor": 0.86,
            "maintenance_interval": 260,
            "operational_status": "available",
            "maintenance_info": {"test": "api_data"},
            "remarks": "API接口测试用设备配置"
        }
    
    async def test_create_equipment_config_api_with_valid_data_returns_201_and_config(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试创建设备配置API使用有效数据返回201和配置信息"""
        response = await get_test_client.post(
            "/api/v1/equipment-configs/",
            json=sample_api_data
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["equipment_type"] == sample_api_data["equipment_type"]
        assert data["equipment_model"] == sample_api_data["equipment_model"]
        assert data["quantity"] == sample_api_data["quantity"]
        assert data["max_load_capacity"] == sample_api_data["max_load_capacity"]
        assert "id" in data
        assert "created_at" in data
    
    async def test_get_equipment_config_list_api_returns_200_and_paginated_list(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试获取设备配置列表API返回200和分页列表"""
        # 先创建一个设备配置
        await get_test_client.post("/api/v1/equipment-configs/", json=sample_api_data)
        
        # 获取设备配置列表
        response = await get_test_client.get("/api/v1/equipment-configs/")
        
        assert response.status_code == 200
        data = response.json()
        assert "equipment_configs" in data
        assert "total" in data
        assert "page" in data
        assert "page_size" in data
        assert "total_pages" in data
        assert isinstance(data["equipment_configs"], list)
    
    async def test_get_equipment_config_detail_api_with_valid_id_returns_200_and_config(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试获取设备配置详情API使用有效ID返回200和配置信息"""
        # 先创建设备配置
        create_response = await get_test_client.post(
            "/api/v1/equipment-configs/",
            json=sample_api_data
        )
        config_id = create_response.json()["id"]
        
        # 获取设备配置详情
        response = await get_test_client.get(f"/api/v1/equipment-configs/{config_id}")
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == config_id
        assert data["equipment_model"] == sample_api_data["equipment_model"]
    
    async def test_update_equipment_config_api_with_valid_data_returns_200_and_updated_config(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试更新设备配置API使用有效数据返回200和更新后的配置"""
        # 先创建设备配置
        create_response = await get_test_client.post(
            "/api/v1/equipment-configs/",
            json=sample_api_data
        )
        config_id = create_response.json()["id"]
        
        # 更新设备配置
        update_data = {
            "equipment_model": "更新后的API测试装载车-API001-V2",
            "quantity": 3,
            "efficiency_factor": 0.90
        }
        
        response = await get_test_client.put(
            f"/api/v1/equipment-configs/{config_id}",
            json=update_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["equipment_model"] == update_data["equipment_model"]
        assert data["quantity"] == update_data["quantity"]
        assert data["efficiency_factor"] == update_data["efficiency_factor"]
    
    async def test_delete_equipment_config_api_with_valid_id_returns_204(
        self,
        get_test_client: AsyncClient,
        sample_api_data: dict
    ):
        """测试删除设备配置API使用有效ID返回204"""
        # 先创建设备配置
        create_response = await get_test_client.post(
            "/api/v1/equipment-configs/",
            json=sample_api_data
        )
        config_id = create_response.json()["id"]
        
        # 删除设备配置
        response = await get_test_client.delete(f"/api/v1/equipment-configs/{config_id}")
        
        assert response.status_code == 204
    
    async def test_create_equipment_config_api_with_invalid_data_returns_400_with_errors(
        self,
        get_test_client: AsyncClient
    ):
        """测试创建设备配置API使用无效数据返回400和错误信息"""
        invalid_data = {
            "equipment_type": "loading_vehicle",
            "equipment_model": "无效API测试设备",
            "quantity": 1,
            "max_load_capacity": 500,     # 小载重
            "loading_speed": 15000,       # 过高速度
            "unloading_speed": 18000,
            "operation_radius": -10,      # 无效半径
            "unit_operation_duration": 1.0,
            "efficiency_factor": 1.5,     # 无效效率
            "maintenance_interval": 10    # 过短间隔
        }
        
        response = await get_test_client.post(
            "/api/v1/equipment-configs/",
            json=invalid_data
        )
        
        assert response.status_code == 400
        data = response.json()
        assert "errors" in data["detail"]
    
    async def test_get_equipment_config_detail_api_with_invalid_id_returns_404(
        self,
        get_test_client: AsyncClient
    ):
        """测试获取设备配置详情API使用无效ID返回404"""
        response = await get_test_client.get("/api/v1/equipment-configs/invalid-id")
        
        assert response.status_code == 404
        data = response.json()
        assert data["detail"] == "设备配置不存在"
