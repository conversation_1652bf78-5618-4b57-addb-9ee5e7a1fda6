"""
应用主入口模块

遵循base-rules.md规范：
- 文件长度不超过500行
- 函数复杂度控制
- 使用描述性命名
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from src.config.settings import settings
from src.database.connection import create_database_tables
from src.api.v1.router import v1_router
from src.api.middleware import (
    add_request_id_middleware,
    add_process_time_middleware,
    add_error_handling_middleware
)
from src.api.exceptions import register_exception_handlers


@asynccontextmanager
async def get_application_lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    应用生命周期管理
    
    Args:
        app: FastAPI应用实例
        
    Yields:
        None: 生命周期控制
    """
    # 启动时执行
    await create_database_tables()
    
    yield
    
    # 关闭时执行
    # 这里可以添加清理逻辑


def create_fastapi_application() -> FastAPI:
    """
    创建FastAPI应用实例
    
    Returns:
        FastAPI: 配置好的FastAPI应用实例
    """
    app = FastAPI(
        title="航空运输保障效能算法库",
        description="Aviation Transport Support Efficiency Algorithm Library",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc",
        openapi_url="/openapi.json",
        lifespan=get_application_lifespan,
    )
    
    # 配置CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 生产环境应该限制具体域名
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 添加自定义中间件
    app.middleware("http")(add_error_handling_middleware)
    app.middleware("http")(add_process_time_middleware)
    app.middleware("http")(add_request_id_middleware)

    # 注册异常处理器
    register_exception_handlers(app)

    # 注册API路由
    app.include_router(v1_router, prefix="/api")

    return app


# 创建应用实例
app = create_fastapi_application()


@app.get("/")
async def get_root_endpoint() -> dict[str, str]:
    """
    根路径端点
    
    Returns:
        dict: 应用基本信息
    """
    return {
        "message": "航空运输保障效能算法库",
        "version": "1.0.0",
        "status": "running"
    }


@app.get("/health")
async def get_health_check() -> dict[str, str]:
    """
    健康检查端点
    
    Returns:
        dict: 健康状态信息
    """
    return {
        "status": "healthy",
        "service": "xiaoneng-aviation-transport"
    }
