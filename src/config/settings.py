"""
应用配置模块

遵循base-rules.md规范：
- 统一考虑配置文件，代码中不能出现硬编码
- 所有配置通过环境变量或配置文件管理
"""

from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class DatabaseSettings(BaseSettings):
    """数据库配置"""
    
    host: str = Field(default="localhost", description="数据库主机地址")
    port: int = Field(default=5432, description="数据库端口")
    name: str = Field(default="xiaoneng_aviation", description="数据库名称")
    user: str = Field(default="xiaoneng_user", description="数据库用户名")
    password: str = Field(default="xiaoneng_password", description="数据库密码")
    
    @property
    def url(self) -> str:
        """获取数据库连接URL"""
        return f"postgresql://{self.user}:{self.password}@{self.host}:{self.port}/{self.name}"
    
    class Config:
        env_prefix = "DATABASE_"


class RedisSettings(BaseSettings):
    """Redis配置"""
    
    host: str = Field(default="localhost", description="Redis主机地址")
    port: int = Field(default=6379, description="Redis端口")
    db: int = Field(default=0, description="Redis数据库编号")
    password: Optional[str] = Field(default=None, description="Redis密码")
    
    @property
    def url(self) -> str:
        """获取Redis连接URL"""
        if self.password:
            return f"redis://:{self.password}@{self.host}:{self.port}/{self.db}"
        return f"redis://{self.host}:{self.port}/{self.db}"
    
    class Config:
        env_prefix = "REDIS_"


class RabbitMQSettings(BaseSettings):
    """RabbitMQ配置"""
    
    host: str = Field(default="localhost", description="RabbitMQ主机地址")
    port: int = Field(default=5672, description="RabbitMQ端口")
    user: str = Field(default="guest", description="RabbitMQ用户名")
    password: str = Field(default="guest", description="RabbitMQ密码")
    
    @property
    def url(self) -> str:
        """获取RabbitMQ连接URL"""
        return f"amqp://{self.user}:{self.password}@{self.host}:{self.port}/"
    
    class Config:
        env_prefix = "RABBITMQ_"


class APISettings(BaseSettings):
    """API配置"""
    
    host: str = Field(default="0.0.0.0", description="API服务主机地址")
    port: int = Field(default=8000, description="API服务端口")
    debug: bool = Field(default=False, description="调试模式")
    reload: bool = Field(default=False, description="自动重载")
    
    class Config:
        env_prefix = "API_"


class SecuritySettings(BaseSettings):
    """安全配置"""
    
    secret_key: str = Field(default="your-secret-key-here", description="密钥")
    algorithm: str = Field(default="HS256", description="加密算法")
    access_token_expire_minutes: int = Field(default=30, description="访问令牌过期时间（分钟）")
    
    class Config:
        env_prefix = "SECURITY_"


class AlgorithmSettings(BaseSettings):
    """算法配置"""
    
    max_calculation_time: int = Field(default=3600, description="最大计算时间（秒）")
    max_concurrent_calculations: int = Field(default=10, description="最大并发计算数")
    default_simulation_runs: int = Field(default=1000, description="默认仿真运行次数")
    
    class Config:
        env_prefix = "ALGORITHM_"


class LogSettings(BaseSettings):
    """日志配置"""
    
    level: str = Field(default="INFO", description="日志级别")
    format: str = Field(default="json", description="日志格式")
    
    class Config:
        env_prefix = "LOG_"


class Settings(BaseSettings):
    """应用总配置"""
    
    # 各模块配置
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    redis: RedisSettings = Field(default_factory=RedisSettings)
    rabbitmq: RabbitMQSettings = Field(default_factory=RabbitMQSettings)
    api: APISettings = Field(default_factory=APISettings)
    security: SecuritySettings = Field(default_factory=SecuritySettings)
    algorithm: AlgorithmSettings = Field(default_factory=AlgorithmSettings)
    log: LogSettings = Field(default_factory=LogSettings)
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()
