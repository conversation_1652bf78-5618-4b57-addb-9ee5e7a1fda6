"""
API依赖项

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 使用描述性的函数命名
- 模块职责单一性：只包含API依赖项相关功能
"""

from typing import Optional
from fastapi import Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session


class PaginationParams:
    """
    分页参数类
    
    提供标准化的分页参数处理
    """
    
    def __init__(
        self,
        page: int = Query(1, ge=1, description="页码"),
        page_size: int = Query(20, ge=1, le=100, description="每页数量")
    ):
        """
        初始化分页参数
        
        Args:
            page: 页码，从1开始
            page_size: 每页数量，最大100
        """
        self.page = page
        self.page_size = page_size
        self.offset = (page - 1) * page_size
        self.limit = page_size


class SearchParams:
    """
    搜索参数类
    
    提供标准化的搜索参数处理
    """
    
    def __init__(
        self,
        search: Optional[str] = Query(None, description="搜索关键词"),
        sort_by: Optional[str] = Query(None, description="排序字段"),
        sort_order: Optional[str] = Query("desc", regex="^(asc|desc)$", description="排序方向")
    ):
        """
        初始化搜索参数
        
        Args:
            search: 搜索关键词
            sort_by: 排序字段
            sort_order: 排序方向，asc或desc
        """
        self.search = search
        self.sort_by = sort_by
        self.sort_order = sort_order


async def get_current_user(
    # 这里可以添加认证逻辑，比如从JWT token中获取用户信息
    # token: str = Depends(oauth2_scheme)
) -> Optional[dict]:
    """
    获取当前用户信息
    
    Returns:
        Optional[dict]: 当前用户信息，未认证时返回None
    """
    # 暂时返回None，后续可以实现完整的用户认证逻辑
    return None


async def require_authentication(
    current_user: Optional[dict] = Depends(get_current_user)
) -> dict:
    """
    要求用户认证
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        dict: 认证用户信息
        
    Raises:
        HTTPException: 用户未认证时抛出401异常
    """
    if current_user is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="需要用户认证",
            headers={"WWW-Authenticate": "Bearer"}
        )
    
    return current_user


async def check_admin_permission(
    current_user: dict = Depends(require_authentication)
) -> dict:
    """
    检查管理员权限
    
    Args:
        current_user: 当前用户信息
        
    Returns:
        dict: 管理员用户信息
        
    Raises:
        HTTPException: 用户无管理员权限时抛出403异常
    """
    if not current_user.get("is_admin", False):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要管理员权限"
        )
    
    return current_user


def get_pagination_params() -> PaginationParams:
    """
    获取分页参数依赖项
    
    Returns:
        PaginationParams: 分页参数实例
    """
    return Depends(PaginationParams)


def get_search_params() -> SearchParams:
    """
    获取搜索参数依赖项
    
    Returns:
        SearchParams: 搜索参数实例
    """
    return Depends(SearchParams)


async def validate_resource_exists(
    resource_id: str,
    resource_service: any,
    resource_name: str = "资源"
) -> any:
    """
    验证资源是否存在
    
    Args:
        resource_id: 资源ID
        resource_service: 资源服务实例
        resource_name: 资源名称，用于错误消息
        
    Returns:
        any: 资源对象
        
    Raises:
        HTTPException: 资源不存在时抛出404异常
    """
    resource = await resource_service.get_by_id(resource_id)
    
    if resource is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"{resource_name}不存在"
        )
    
    return resource


class DatabaseTransactionManager:
    """
    数据库事务管理器
    
    提供事务管理的上下文管理器
    """
    
    def __init__(self, db_session: AsyncSession):
        """
        初始化事务管理器
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def __aenter__(self):
        """进入事务上下文"""
        return self.db_session
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """退出事务上下文"""
        if exc_type is not None:
            # 发生异常时回滚事务
            await self.db_session.rollback()
        else:
            # 正常情况下提交事务
            await self.db_session.commit()


async def get_transaction_manager(
    db_session: AsyncSession = Depends(get_database_session)
) -> DatabaseTransactionManager:
    """
    获取数据库事务管理器
    
    Args:
        db_session: 数据库会话
        
    Returns:
        DatabaseTransactionManager: 事务管理器实例
    """
    return DatabaseTransactionManager(db_session)
