"""
装卸载作业效能管理API路由

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含装卸载作业效能管理相关的API路由
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, status
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session
from src.services.loading_efficiency_service import (
    LoadingEfficiencyTaskService,
    LoadingEfficiencyCalculationService
)
from src.schemas.loading_efficiency import (
    LoadingEfficiencyCalculationRequestSchema,
    LoadingEfficiencyTaskResponseSchema,
    LoadingEfficiencyResultResponseSchema,
    ContributionValueResponseSchema,
    ExecutionReportResponseSchema,
    TaskProgressResponseSchema
)
from src.database.models.loading_efficiency import TaskStatusEnum

# 创建路由器
router = APIRouter(prefix="/loading-efficiency", tags=["装卸载作业效能管理"])


async def get_task_service(
    db_session: AsyncSession = Depends(get_database_session)
) -> LoadingEfficiencyTaskService:
    """
    获取装卸载作业效能任务服务实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        LoadingEfficiencyTaskService: 任务服务实例
    """
    return LoadingEfficiencyTaskService(db_session)


async def get_calculation_service(
    db_session: AsyncSession = Depends(get_database_session)
) -> LoadingEfficiencyCalculationService:
    """
    获取装卸载作业效能计算服务实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        LoadingEfficiencyCalculationService: 计算服务实例
    """
    return LoadingEfficiencyCalculationService(db_session)


async def execute_efficiency_calculation(
    task_id: str,
    task_service: LoadingEfficiencyTaskService,
    calculation_service: LoadingEfficiencyCalculationService
) -> None:
    """
    执行装卸载作业效能计算的后台任务
    
    Args:
        task_id: 任务ID
        task_service: 任务服务实例
        calculation_service: 计算服务实例
    """
    try:
        # 更新任务状态为运行中
        await task_service.update_task_status(task_id, TaskStatusEnum.RUNNING, 10)
        
        # 获取任务信息
        task = await task_service.get_task_by_id(task_id)
        if not task:
            raise ValueError(f"任务不存在: {task_id}")
        
        # 执行效能指标计算
        await task_service.update_task_status(task_id, TaskStatusEnum.RUNNING, 30)
        efficiency_results = await calculation_service.calculate_efficiency_indicators(task)
        
        # 执行贡献值计算
        await task_service.update_task_status(task_id, TaskStatusEnum.RUNNING, 60)
        contribution_values = await calculation_service.calculate_contribution_values(task)
        
        # 生成作业执行报告
        await task_service.update_task_status(task_id, TaskStatusEnum.RUNNING, 80)
        execution_report = await calculation_service.generate_execution_report(
            task, efficiency_results, contribution_values
        )
        
        # 更新任务结果
        await task_service.update_task_results(
            task_id, efficiency_results, contribution_values, execution_report
        )
        
        # 标记任务完成
        await task_service.update_task_status(task_id, TaskStatusEnum.COMPLETED, 100)
        
    except Exception as e:
        # 记录错误并更新任务状态
        error_message = str(e)
        error_details = {"error_type": type(e).__name__, "traceback": str(e)}
        await task_service.update_task_error(task_id, error_message, error_details)


@router.post(
    "/calculate",
    response_model=LoadingEfficiencyTaskResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="启动装卸载作业效能计算",
    description="创建并启动装卸载作业效能计算任务，支持三段式作业流程分析"
)
async def start_loading_efficiency_calculation(
    calculation_request: LoadingEfficiencyCalculationRequestSchema,
    background_tasks: BackgroundTasks,
    task_service: LoadingEfficiencyTaskService = Depends(get_task_service),
    calculation_service: LoadingEfficiencyCalculationService = Depends(get_calculation_service)
) -> LoadingEfficiencyTaskResponseSchema:
    """
    启动装卸载作业效能计算
    
    Args:
        calculation_request: 计算请求数据
        background_tasks: 后台任务管理器
        task_service: 任务服务
        calculation_service: 计算服务
        
    Returns:
        LoadingEfficiencyTaskResponseSchema: 创建的计算任务信息
        
    Raises:
        HTTPException: 当请求数据无效时返回400错误
    """
    try:
        # 构建任务数据
        task_data = {
            "task_name": calculation_request.task_name,
            "scenario_id": calculation_request.scenario_id,
            "loading_phases": calculation_request.loading_phases,
            "input_data": {
                "warehouse_loading_data": calculation_request.warehouse_loading_data,
                "ground_transport_data": calculation_request.ground_transport_data,
                "aircraft_loading_data": calculation_request.aircraft_loading_data,
                "equipment_data": calculation_request.equipment_data,
                "personnel_data": calculation_request.personnel_data
            },
            "calculation_parameters": calculation_request.calculation_options
        }
        
        # 创建计算任务
        task = await task_service.create_task(task_data)
        
        # 添加后台计算任务
        background_tasks.add_task(
            execute_efficiency_calculation,
            task.id,
            task_service,
            calculation_service
        )
        
        return LoadingEfficiencyTaskResponseSchema.model_validate(task)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动计算任务失败: {str(e)}"
        )


@router.get(
    "/tasks/{task_id}/progress",
    response_model=TaskProgressResponseSchema,
    summary="获取计算进度",
    description="获取装卸载作业效能计算任务的执行进度和状态信息"
)
async def get_calculation_progress(
    task_id: str,
    task_service: LoadingEfficiencyTaskService = Depends(get_task_service)
) -> TaskProgressResponseSchema:
    """
    获取计算进度
    
    Args:
        task_id: 任务ID
        task_service: 任务服务
        
    Returns:
        TaskProgressResponseSchema: 任务进度信息
        
    Raises:
        HTTPException: 当任务不存在时返回404错误
    """
    task = await task_service.get_task_by_id(task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="计算任务不存在"
        )
    
    # 估算完成时间（简化实现）
    estimated_completion_time = None
    if task.status == TaskStatusEnum.RUNNING and task.started_at and task.progress_percentage > 0:
        from datetime import datetime, timedelta
        start_time = datetime.fromisoformat(task.started_at)
        elapsed_time = datetime.now() - start_time
        if task.progress_percentage > 0:
            total_estimated_time = elapsed_time * (100 / task.progress_percentage)
            estimated_completion = start_time + total_estimated_time
            estimated_completion_time = estimated_completion.isoformat()
    
    # 确定当前执行阶段
    current_phase = None
    if task.status == TaskStatusEnum.RUNNING:
        if task.progress_percentage < 30:
            current_phase = "效能指标计算"
        elif task.progress_percentage < 60:
            current_phase = "贡献值计算"
        elif task.progress_percentage < 80:
            current_phase = "报告生成"
        else:
            current_phase = "结果整理"
    
    return TaskProgressResponseSchema(
        task_id=task.id,
        status=task.status,
        progress_percentage=task.progress_percentage,
        started_at=task.started_at,
        estimated_completion_time=estimated_completion_time,
        current_phase=current_phase,
        error_message=task.error_message
    )


@router.get(
    "/tasks/{task_id}/results",
    response_model=LoadingEfficiencyResultResponseSchema,
    summary="获取效能计算结果",
    description="获取装卸载作业效能计算的详细结果，包括各类指标的计算值"
)
async def get_efficiency_calculation_results(
    task_id: str,
    task_service: LoadingEfficiencyTaskService = Depends(get_task_service)
) -> LoadingEfficiencyResultResponseSchema:
    """
    获取效能计算结果
    
    Args:
        task_id: 任务ID
        task_service: 任务服务
        
    Returns:
        LoadingEfficiencyResultResponseSchema: 效能计算结果
        
    Raises:
        HTTPException: 当任务不存在或未完成时返回相应错误
    """
    task = await task_service.get_task_by_id(task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="计算任务不存在"
        )
    
    if task.status != TaskStatusEnum.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"任务尚未完成，当前状态: {task.status.value}"
        )
    
    efficiency_results = task.efficiency_results
    
    # 计算综合效能评分（简化实现）
    overall_score = 0.0
    indicator_count = 0
    
    for category_results in efficiency_results.values():
        if isinstance(category_results, dict):
            for value in category_results.values():
                if isinstance(value, (int, float)):
                    overall_score += float(value)
                    indicator_count += 1
    
    if indicator_count > 0:
        overall_score = overall_score / indicator_count
    
    # 构建阶段结果（简化实现）
    phase_results = {}
    for phase in task.loading_phases:
        phase_name = phase.get('phase_name', 'Unknown')
        phase_results[phase_name] = {
            "completion_rate": 100.0,
            "efficiency_score": overall_score * 0.9  # 简化计算
        }
    
    return LoadingEfficiencyResultResponseSchema(
        task_id=task.id,
        timeliness_indicators=efficiency_results.get('timeliness', {}),
        efficiency_indicators=efficiency_results.get('efficiency', {}),
        quality_indicators=efficiency_results.get('quality', {}),
        resource_config_indicators=efficiency_results.get('resource_config', {}),
        coordination_indicators=efficiency_results.get('coordination', {}),
        overall_score=round(overall_score, 2),
        phase_results=phase_results
    )


@router.get(
    "/tasks/{task_id}/contributions",
    response_model=ContributionValueResponseSchema,
    summary="获取贡献值计算结果",
    description="获取装卸载作业中各设备和人员的贡献值分析结果"
)
async def get_contribution_values(
    task_id: str,
    task_service: LoadingEfficiencyTaskService = Depends(get_task_service)
) -> ContributionValueResponseSchema:
    """
    获取贡献值计算结果
    
    Args:
        task_id: 任务ID
        task_service: 任务服务
        
    Returns:
        ContributionValueResponseSchema: 贡献值计算结果
        
    Raises:
        HTTPException: 当任务不存在或未完成时返回相应错误
    """
    task = await task_service.get_task_by_id(task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="计算任务不存在"
        )
    
    if task.status != TaskStatusEnum.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"任务尚未完成，当前状态: {task.status.value}"
        )
    
    contribution_values = task.contribution_values
    
    # 生成排名前列的贡献者
    equipment_contributions = contribution_values.get('equipment', {})
    personnel_contributions = contribution_values.get('personnel', {})
    
    # 设备贡献值排名
    top_equipment_contributors = []
    if equipment_contributions:
        sorted_equipment = sorted(
            equipment_contributions.items(),
            key=lambda x: x[1].get('contribution_score', 0),
            reverse=True
        )[:5]  # 取前5名
        
        for eq_id, data in sorted_equipment:
            top_equipment_contributors.append({
                "equipment_id": eq_id,
                "contribution_score": data.get('contribution_score', 0),
                "usage_time": data.get('usage_time', 0),
                "processed_volume": data.get('processed_volume', 0)
            })
    
    # 人员贡献值排名
    top_personnel_contributors = []
    if personnel_contributions:
        sorted_personnel = sorted(
            personnel_contributions.items(),
            key=lambda x: x[1].get('contribution_score', 0),
            reverse=True
        )[:5]  # 取前5名
        
        for person_id, data in sorted_personnel:
            top_personnel_contributors.append({
                "personnel_id": person_id,
                "contribution_score": data.get('contribution_score', 0),
                "work_time": data.get('work_time', 0),
                "completed_tasks": data.get('completed_tasks', 0)
            })
    
    return ContributionValueResponseSchema(
        task_id=task.id,
        equipment_contributions=equipment_contributions,
        personnel_contributions=personnel_contributions,
        top_equipment_contributors=top_equipment_contributors,
        top_personnel_contributors=top_personnel_contributors
    )


@router.get(
    "/tasks/{task_id}/report",
    response_model=ExecutionReportResponseSchema,
    summary="获取作业执行报告",
    description="获取装卸载作业的标准化执行报告，包括效能分析和改进建议"
)
async def get_execution_report(
    task_id: str,
    task_service: LoadingEfficiencyTaskService = Depends(get_task_service)
) -> ExecutionReportResponseSchema:
    """
    获取作业执行报告
    
    Args:
        task_id: 任务ID
        task_service: 任务服务
        
    Returns:
        ExecutionReportResponseSchema: 作业执行报告
        
    Raises:
        HTTPException: 当任务不存在或未完成时返回相应错误
    """
    task = await task_service.get_task_by_id(task_id)
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="计算任务不存在"
        )
    
    if task.status != TaskStatusEnum.COMPLETED:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"任务尚未完成，当前状态: {task.status.value}"
        )
    
    execution_report = task.execution_report
    
    return ExecutionReportResponseSchema(
        task_id=task.id,
        task_name=task.task_name,
        scenario_id=task.scenario_id,
        execution_summary=execution_report.get('task_info', {}),
        phase_analysis=execution_report.get('loading_phases', {}),
        efficiency_analysis=execution_report.get('efficiency_summary', {}),
        contribution_analysis=execution_report.get('contribution_analysis', {}),
        improvement_recommendations=execution_report.get('recommendations', []),
        generated_at=execution_report.get('generated_at', '')
    )
