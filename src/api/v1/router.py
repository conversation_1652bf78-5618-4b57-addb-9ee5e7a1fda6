"""
API v1路由汇总

遵循base-rules.md规范：
- 模块职责单一性：只负责路由汇总
- 使用描述性的函数命名
"""

from fastapi import APIRouter
from src.api.v1.scenarios import router as scenarios_router
from src.api.v1.equipment import router as equipment_router
from src.api.v1.aircraft import router as aircraft_router
from src.api.v1.configuration import router as configuration_router

# 创建v1版本的主路由器
v1_router = APIRouter(prefix="/v1")

# 注册各模块路由
v1_router.include_router(scenarios_router)
v1_router.include_router(equipment_router)
v1_router.include_router(aircraft_router)
v1_router.include_router(configuration_router)

# 导出路由器
__all__ = ["v1_router"]
