"""
配置方案管理API路由

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含配置方案管理相关的API路由
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session
from src.services.configuration import ConfigurationService
from src.schemas.configuration import (
    ConfigurationSchemeCreateSchema,
    ConfigurationSchemeUpdateSchema,
    ConfigurationSchemeResponseSchema,
    ConfigurationSchemeListResponseSchema,
    SchemeValidationResultSchema,
    SchemeVersionListResponseSchema
)
from src.database.models.configuration import SchemeTypeEnum, SchemeStatusEnum

# 创建路由器
router = APIRouter(prefix="/configuration-schemes", tags=["配置方案管理"])


async def get_configuration_service(
    db_session: AsyncSession = Depends(get_database_session)
) -> ConfigurationService:
    """
    获取配置方案服务实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        ConfigurationService: 配置方案服务实例
    """
    return ConfigurationService(db_session)


@router.post(
    "/",
    response_model=ConfigurationSchemeResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="创建配置方案",
    description="创建完整的配置方案，整合设备、飞机、人员等各类配置"
)
async def create_configuration_scheme(
    scheme_data: ConfigurationSchemeCreateSchema,
    configuration_service: ConfigurationService = Depends(get_configuration_service)
) -> ConfigurationSchemeResponseSchema:
    """
    创建配置方案接口
    
    Args:
        scheme_data: 配置方案创建数据
        configuration_service: 配置方案服务
        
    Returns:
        ConfigurationSchemeResponseSchema: 创建的配置方案信息
        
    Raises:
        HTTPException: 创建失败时抛出异常
    """
    try:
        # 创建配置方案
        configuration_scheme = await configuration_service.create_configuration_scheme(
            scheme_data
        )
        return configuration_scheme
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建配置方案失败: {str(e)}"
        )


@router.get(
    "/",
    response_model=ConfigurationSchemeListResponseSchema,
    summary="获取配置方案列表",
    description="查询配置方案列表，支持多种筛选条件"
)
async def get_configuration_scheme_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    scheme_type: Optional[SchemeTypeEnum] = Query(None, description="方案类型筛选"),
    status: Optional[SchemeStatusEnum] = Query(None, description="状态筛选"),
    category: Optional[str] = Query(None, description="分类筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    configuration_service: ConfigurationService = Depends(get_configuration_service)
) -> ConfigurationSchemeListResponseSchema:
    """
    获取配置方案列表接口
    
    Args:
        page: 页码
        page_size: 每页数量
        scheme_type: 方案类型筛选
        status: 状态筛选
        category: 分类筛选
        search: 搜索关键词
        configuration_service: 配置方案服务
        
    Returns:
        ConfigurationSchemeListResponseSchema: 配置方案列表响应
    """
    try:
        scheme_list = await configuration_service.get_configuration_scheme_list(
            page=page,
            page_size=page_size,
            scheme_type=scheme_type,
            status=status,
            category=category,
            search_keyword=search
        )
        return scheme_list
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配置方案列表失败: {str(e)}"
        )


@router.get(
    "/{scheme_id}",
    response_model=ConfigurationSchemeResponseSchema,
    summary="获取配置方案详情",
    description="根据配置方案ID获取方案的详细信息"
)
async def get_configuration_scheme_detail(
    scheme_id: str,
    configuration_service: ConfigurationService = Depends(get_configuration_service)
) -> ConfigurationSchemeResponseSchema:
    """
    获取配置方案详情接口
    
    Args:
        scheme_id: 配置方案ID
        configuration_service: 配置方案服务
        
    Returns:
        ConfigurationSchemeResponseSchema: 配置方案详细信息
        
    Raises:
        HTTPException: 配置方案不存在时抛出404异常
    """
    configuration_scheme = await configuration_service.get_configuration_scheme_by_id(
        scheme_id
    )
    
    if configuration_scheme is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置方案不存在"
        )
    
    return configuration_scheme


@router.put(
    "/{scheme_id}",
    response_model=ConfigurationSchemeResponseSchema,
    summary="更新配置方案",
    description="更新配置方案的配置信息"
)
async def update_configuration_scheme(
    scheme_id: str,
    update_data: ConfigurationSchemeUpdateSchema,
    configuration_service: ConfigurationService = Depends(get_configuration_service)
) -> ConfigurationSchemeResponseSchema:
    """
    更新配置方案接口
    
    Args:
        scheme_id: 配置方案ID
        update_data: 更新数据
        configuration_service: 配置方案服务
        
    Returns:
        ConfigurationSchemeResponseSchema: 更新后的配置方案信息
        
    Raises:
        HTTPException: 配置方案不存在或更新失败时抛出异常
    """
    try:
        # 更新配置方案
        configuration_scheme = await configuration_service.update_configuration_scheme(
            scheme_id, update_data
        )
        
        if configuration_scheme is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="配置方案不存在"
            )
        
        return configuration_scheme
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新配置方案失败: {str(e)}"
        )


@router.delete(
    "/{scheme_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除配置方案",
    description="删除指定的配置方案（软删除）"
)
async def delete_configuration_scheme(
    scheme_id: str,
    configuration_service: ConfigurationService = Depends(get_configuration_service)
) -> None:
    """
    删除配置方案接口
    
    Args:
        scheme_id: 配置方案ID
        configuration_service: 配置方案服务
        
    Raises:
        HTTPException: 配置方案不存在时抛出404异常
    """
    success = await configuration_service.delete_configuration_scheme(scheme_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="配置方案不存在"
        )


@router.post(
    "/{scheme_id}/validate",
    response_model=SchemeValidationResultSchema,
    summary="验证配置方案",
    description="验证配置方案的合理性和提供优化建议"
)
async def validate_configuration_scheme(
    scheme_id: str,
    configuration_service: ConfigurationService = Depends(get_configuration_service)
) -> SchemeValidationResultSchema:
    """
    验证配置方案接口
    
    Args:
        scheme_id: 配置方案ID
        configuration_service: 配置方案服务
        
    Returns:
        SchemeValidationResultSchema: 验证结果
    """
    try:
        validation_result = await configuration_service.validate_configuration_scheme(
            scheme_id
        )
        return validation_result
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"验证配置方案失败: {str(e)}"
        )


@router.get(
    "/{scheme_id}/versions",
    response_model=SchemeVersionListResponseSchema,
    summary="获取方案版本列表",
    description="获取配置方案的历史版本列表"
)
async def get_scheme_versions(
    scheme_id: str,
    configuration_service: ConfigurationService = Depends(get_configuration_service)
) -> SchemeVersionListResponseSchema:
    """
    获取方案版本列表接口
    
    Args:
        scheme_id: 配置方案ID
        configuration_service: 配置方案服务
        
    Returns:
        SchemeVersionListResponseSchema: 版本列表响应
    """
    try:
        version_list = await configuration_service.get_scheme_versions(scheme_id)
        return version_list
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取方案版本列表失败: {str(e)}"
        )
