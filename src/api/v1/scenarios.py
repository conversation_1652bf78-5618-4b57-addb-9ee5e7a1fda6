"""
场景管理API路由

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含场景管理相关的API路由
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session
from src.services.scenario import ScenarioService
from src.schemas.scenario import (
    ScenarioCreateSchema,
    ScenarioUpdateSchema,
    ScenarioResponseSchema,
    ScenarioListResponseSchema
)
from src.database.models.scenario import ScenarioStatusEnum

# 创建路由器
router = APIRouter(prefix="/scenarios", tags=["场景管理"])


async def get_scenario_service(
    db_session: AsyncSession = Depends(get_database_session)
) -> ScenarioService:
    """
    获取场景服务实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        ScenarioService: 场景服务实例
    """
    return ScenarioService(db_session)


@router.post(
    "/",
    response_model=ScenarioResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="创建场景",
    description="创建新的运输保障场景，定义完整的任务参数和环境条件"
)
async def create_scenario(
    scenario_data: ScenarioCreateSchema,
    scenario_service: ScenarioService = Depends(get_scenario_service)
) -> ScenarioResponseSchema:
    """
    创建场景接口
    
    Args:
        scenario_data: 场景创建数据
        scenario_service: 场景服务
        
    Returns:
        ScenarioResponseSchema: 创建的场景信息
        
    Raises:
        HTTPException: 创建失败时抛出异常
    """
    try:
        # 验证场景参数
        validation_errors = await scenario_service.validate_scenario_parameters(
            scenario_data.model_dump()
        )
        
        if validation_errors:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"message": "场景参数验证失败", "errors": validation_errors}
            )
        
        # 创建场景
        scenario = await scenario_service.create_scenario(scenario_data)
        return scenario
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建场景失败: {str(e)}"
        )


@router.get(
    "/",
    response_model=ScenarioListResponseSchema,
    summary="获取场景列表",
    description="分页查询场景列表，支持多种筛选条件"
)
async def get_scenario_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    scenario_type: Optional[str] = Query(None, description="场景类型筛选"),
    task_type: Optional[str] = Query(None, description="任务类型筛选"),
    status: Optional[ScenarioStatusEnum] = Query(None, description="状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    scenario_service: ScenarioService = Depends(get_scenario_service)
) -> ScenarioListResponseSchema:
    """
    获取场景列表接口
    
    Args:
        page: 页码
        page_size: 每页数量
        scenario_type: 场景类型筛选
        task_type: 任务类型筛选
        status: 状态筛选
        search: 搜索关键词
        scenario_service: 场景服务
        
    Returns:
        ScenarioListResponseSchema: 场景列表响应
    """
    try:
        scenario_list = await scenario_service.get_scenario_list(
            page=page,
            page_size=page_size,
            scenario_type=scenario_type,
            task_type=task_type,
            status=status,
            search_keyword=search
        )
        return scenario_list
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取场景列表失败: {str(e)}"
        )


@router.get(
    "/{scenario_id}",
    response_model=ScenarioResponseSchema,
    summary="获取场景详情",
    description="根据场景ID获取场景的详细信息"
)
async def get_scenario_detail(
    scenario_id: str,
    scenario_service: ScenarioService = Depends(get_scenario_service)
) -> ScenarioResponseSchema:
    """
    获取场景详情接口
    
    Args:
        scenario_id: 场景ID
        scenario_service: 场景服务
        
    Returns:
        ScenarioResponseSchema: 场景详细信息
        
    Raises:
        HTTPException: 场景不存在时抛出404异常
    """
    scenario = await scenario_service.get_scenario_by_id(scenario_id)
    
    if scenario is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="场景不存在"
        )
    
    return scenario


@router.put(
    "/{scenario_id}",
    response_model=ScenarioResponseSchema,
    summary="更新场景",
    description="更新场景的配置信息"
)
async def update_scenario(
    scenario_id: str,
    update_data: ScenarioUpdateSchema,
    scenario_service: ScenarioService = Depends(get_scenario_service)
) -> ScenarioResponseSchema:
    """
    更新场景接口
    
    Args:
        scenario_id: 场景ID
        update_data: 更新数据
        scenario_service: 场景服务
        
    Returns:
        ScenarioResponseSchema: 更新后的场景信息
        
    Raises:
        HTTPException: 场景不存在或更新失败时抛出异常
    """
    try:
        # 验证更新数据
        if update_data.model_dump(exclude_unset=True):
            validation_errors = await scenario_service.validate_scenario_parameters(
                update_data.model_dump(exclude_unset=True)
            )
            
            if validation_errors:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={"message": "场景参数验证失败", "errors": validation_errors}
                )
        
        # 更新场景
        scenario = await scenario_service.update_scenario(scenario_id, update_data)
        
        if scenario is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="场景不存在"
            )
        
        return scenario
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新场景失败: {str(e)}"
        )


@router.delete(
    "/{scenario_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除场景",
    description="删除指定的场景（软删除）"
)
async def delete_scenario(
    scenario_id: str,
    scenario_service: ScenarioService = Depends(get_scenario_service)
) -> None:
    """
    删除场景接口
    
    Args:
        scenario_id: 场景ID
        scenario_service: 场景服务
        
    Raises:
        HTTPException: 场景不存在时抛出404异常
    """
    success = await scenario_service.delete_scenario(scenario_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="场景不存在"
        )
