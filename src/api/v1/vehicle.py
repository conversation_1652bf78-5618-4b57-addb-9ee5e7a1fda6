"""
运输车辆配置管理API路由

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含运输车辆配置管理相关的API路由
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session
from src.services.vehicle import VehicleService
from src.schemas.vehicle import (
    VehicleCreateSchema,
    VehicleUpdateSchema,
    VehicleResponseSchema,
    VehicleListResponseSchema
)
from src.database.models.vehicle import VehicleTypeEnum, VehicleStatusEnum

# 创建路由器
router = APIRouter(prefix="/transport-vehicle-configs", tags=["运输车辆配置管理"])


async def get_vehicle_service(
    db_session: AsyncSession = Depends(get_database_session)
) -> VehicleService:
    """
    获取运输车辆配置服务实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        VehicleService: 运输车辆配置服务实例
    """
    return VehicleService(db_session)


@router.post(
    "/",
    response_model=VehicleResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="创建运输车辆配置",
    description="创建新的运输车辆配置，定义车辆的基本信息和性能参数"
)
async def create_vehicle_config(
    vehicle_data: VehicleCreateSchema,
    vehicle_service: VehicleService = Depends(get_vehicle_service)
) -> VehicleResponseSchema:
    """
    创建运输车辆配置接口
    
    Args:
        vehicle_data: 运输车辆配置创建数据
        vehicle_service: 运输车辆配置服务
        
    Returns:
        VehicleResponseSchema: 创建的运输车辆配置信息
        
    Raises:
        HTTPException: 创建失败时抛出异常
    """
    try:
        # 验证车辆配置参数
        validation_errors = await vehicle_service.validate_vehicle_parameters(vehicle_data)
        
        if validation_errors:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"message": "运输车辆配置参数验证失败", "errors": validation_errors}
            )
        
        # 创建运输车辆配置
        vehicle_config = await vehicle_service.create_vehicle_config(vehicle_data)
        return vehicle_config
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建运输车辆配置失败: {str(e)}"
        )


@router.get(
    "/",
    response_model=VehicleListResponseSchema,
    summary="获取运输车辆配置列表",
    description="分页查询运输车辆配置列表，支持多种筛选条件"
)
async def get_vehicle_config_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    vehicle_type: Optional[VehicleTypeEnum] = Query(None, description="车辆类型筛选"),
    status: Optional[VehicleStatusEnum] = Query(None, description="状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    vehicle_service: VehicleService = Depends(get_vehicle_service)
) -> VehicleListResponseSchema:
    """
    获取运输车辆配置列表接口
    
    Args:
        page: 页码
        page_size: 每页数量
        vehicle_type: 车辆类型筛选
        status: 状态筛选
        search: 搜索关键词
        vehicle_service: 运输车辆配置服务
        
    Returns:
        VehicleListResponseSchema: 运输车辆配置列表响应
    """
    try:
        vehicle_list = await vehicle_service.get_vehicle_config_list(
            page=page,
            page_size=page_size,
            vehicle_type=vehicle_type,
            status=status,
            search_keyword=search
        )
        return vehicle_list
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取运输车辆配置列表失败: {str(e)}"
        )


@router.get(
    "/{vehicle_id}",
    response_model=VehicleResponseSchema,
    summary="获取运输车辆配置详情",
    description="根据车辆配置ID获取车辆配置的详细信息"
)
async def get_vehicle_config_detail(
    vehicle_id: str,
    vehicle_service: VehicleService = Depends(get_vehicle_service)
) -> VehicleResponseSchema:
    """
    获取运输车辆配置详情接口
    
    Args:
        vehicle_id: 运输车辆配置ID
        vehicle_service: 运输车辆配置服务
        
    Returns:
        VehicleResponseSchema: 运输车辆配置详细信息
        
    Raises:
        HTTPException: 运输车辆配置不存在时抛出404异常
    """
    vehicle_config = await vehicle_service.get_vehicle_config_by_id(vehicle_id)
    
    if vehicle_config is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="运输车辆配置不存在"
        )
    
    return vehicle_config


@router.put(
    "/{vehicle_id}",
    response_model=VehicleResponseSchema,
    summary="更新运输车辆配置",
    description="更新运输车辆配置的信息"
)
async def update_vehicle_config(
    vehicle_id: str,
    update_data: VehicleUpdateSchema,
    vehicle_service: VehicleService = Depends(get_vehicle_service)
) -> VehicleResponseSchema:
    """
    更新运输车辆配置接口
    
    Args:
        vehicle_id: 运输车辆配置ID
        update_data: 更新数据
        vehicle_service: 运输车辆配置服务
        
    Returns:
        VehicleResponseSchema: 更新后的运输车辆配置信息
        
    Raises:
        HTTPException: 运输车辆配置不存在或更新失败时抛出异常
    """
    try:
        # 验证更新数据
        if update_data.model_dump(exclude_unset=True):
            validation_errors = await vehicle_service.validate_vehicle_parameters(update_data)
            
            if validation_errors:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail={"message": "运输车辆配置参数验证失败", "errors": validation_errors}
                )
        
        # 更新运输车辆配置
        vehicle_config = await vehicle_service.update_vehicle_config(vehicle_id, update_data)
        
        if vehicle_config is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="运输车辆配置不存在"
            )
        
        return vehicle_config
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新运输车辆配置失败: {str(e)}"
        )


@router.delete(
    "/{vehicle_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除运输车辆配置",
    description="删除指定的运输车辆配置"
)
async def delete_vehicle_config(
    vehicle_id: str,
    vehicle_service: VehicleService = Depends(get_vehicle_service)
) -> None:
    """
    删除运输车辆配置接口
    
    Args:
        vehicle_id: 运输车辆配置ID
        vehicle_service: 运输车辆配置服务
        
    Raises:
        HTTPException: 运输车辆配置不存在时抛出404异常
    """
    success = await vehicle_service.delete_vehicle_config(vehicle_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="运输车辆配置不存在"
        )
