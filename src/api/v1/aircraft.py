"""
飞机配置管理API路由

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含飞机配置管理相关的API路由
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session
from src.services.aircraft import AircraftService
from src.schemas.aircraft import (
    AircraftCreateSchema,
    AircraftUpdateSchema,
    AircraftResponseSchema,
    AircraftListResponseSchema
)
from src.database.models.aircraft import AircraftModelEnum, AircraftStatusEnum

# 创建路由器
router = APIRouter(prefix="/aircraft-configs", tags=["飞机配置管理"])


async def get_aircraft_service(
    db_session: AsyncSession = Depends(get_database_session)
) -> AircraftService:
    """
    获取飞机配置服务实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        AircraftService: 飞机配置服务实例
    """
    return AircraftService(db_session)


@router.post(
    "/",
    response_model=AircraftResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="创建飞机配置",
    description="创建新的飞机配置，定义飞机的基本信息和性能参数"
)
async def create_aircraft_config(
    aircraft_data: AircraftCreateSchema,
    aircraft_service: AircraftService = Depends(get_aircraft_service)
) -> AircraftResponseSchema:
    """
    创建飞机配置接口
    
    Args:
        aircraft_data: 飞机配置创建数据
        aircraft_service: 飞机配置服务
        
    Returns:
        AircraftResponseSchema: 创建的飞机配置信息
        
    Raises:
        HTTPException: 创建失败时抛出异常
    """
    try:
        # 验证飞机配置参数
        validation_errors = await aircraft_service.validate_aircraft_parameters(
            aircraft_data
        )
        
        if validation_errors:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"message": "飞机配置参数验证失败", "errors": validation_errors}
            )
        
        # 创建飞机配置
        aircraft_config = await aircraft_service.create_aircraft_config(aircraft_data)
        return aircraft_config
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建飞机配置失败: {str(e)}"
        )


@router.get(
    "/",
    response_model=AircraftListResponseSchema,
    summary="获取飞机配置列表",
    description="查询飞机配置列表，支持按机型和状态筛选"
)
async def get_aircraft_config_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    aircraft_model: Optional[AircraftModelEnum] = Query(None, description="飞机型号筛选"),
    status: Optional[AircraftStatusEnum] = Query(None, description="飞机状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    aircraft_service: AircraftService = Depends(get_aircraft_service)
) -> AircraftListResponseSchema:
    """
    获取飞机配置列表接口
    
    Args:
        page: 页码
        page_size: 每页数量
        aircraft_model: 飞机型号筛选
        status: 飞机状态筛选
        search: 搜索关键词
        aircraft_service: 飞机配置服务
        
    Returns:
        AircraftListResponseSchema: 飞机配置列表响应
    """
    try:
        aircraft_list = await aircraft_service.get_aircraft_config_list(
            page=page,
            page_size=page_size,
            aircraft_model=aircraft_model,
            status=status,
            search_keyword=search
        )
        return aircraft_list
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取飞机配置列表失败: {str(e)}"
        )


@router.get(
    "/{aircraft_id}",
    response_model=AircraftResponseSchema,
    summary="获取飞机配置详情",
    description="根据飞机配置ID获取飞机的详细信息"
)
async def get_aircraft_config_detail(
    aircraft_id: str,
    aircraft_service: AircraftService = Depends(get_aircraft_service)
) -> AircraftResponseSchema:
    """
    获取飞机配置详情接口
    
    Args:
        aircraft_id: 飞机配置ID
        aircraft_service: 飞机配置服务
        
    Returns:
        AircraftResponseSchema: 飞机配置详细信息
        
    Raises:
        HTTPException: 飞机配置不存在时抛出404异常
    """
    aircraft_config = await aircraft_service.get_aircraft_config_by_id(aircraft_id)
    
    if aircraft_config is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="飞机配置不存在"
        )
    
    return aircraft_config


@router.put(
    "/{aircraft_id}",
    response_model=AircraftResponseSchema,
    summary="更新飞机配置",
    description="更新飞机配置的参数信息"
)
async def update_aircraft_config(
    aircraft_id: str,
    update_data: AircraftUpdateSchema,
    aircraft_service: AircraftService = Depends(get_aircraft_service)
) -> AircraftResponseSchema:
    """
    更新飞机配置接口
    
    Args:
        aircraft_id: 飞机配置ID
        update_data: 更新数据
        aircraft_service: 飞机配置服务
        
    Returns:
        AircraftResponseSchema: 更新后的飞机配置信息
        
    Raises:
        HTTPException: 飞机配置不存在或更新失败时抛出异常
    """
    try:
        # 更新飞机配置
        aircraft_config = await aircraft_service.update_aircraft_config(
            aircraft_id, update_data
        )
        
        if aircraft_config is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="飞机配置不存在"
            )
        
        return aircraft_config
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新飞机配置失败: {str(e)}"
        )


@router.delete(
    "/{aircraft_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除飞机配置",
    description="删除指定的飞机配置"
)
async def delete_aircraft_config(
    aircraft_id: str,
    aircraft_service: AircraftService = Depends(get_aircraft_service)
) -> None:
    """
    删除飞机配置接口
    
    Args:
        aircraft_id: 飞机配置ID
        aircraft_service: 飞机配置服务
        
    Raises:
        HTTPException: 飞机配置不存在时抛出404异常
    """
    success = await aircraft_service.delete_aircraft_config(aircraft_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="飞机配置不存在"
        )
