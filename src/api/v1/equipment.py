"""
设备配置管理API路由

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含设备配置管理相关的API路由
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session
from src.services.equipment import EquipmentService
from src.schemas.equipment import (
    EquipmentCreateSchema,
    EquipmentUpdateSchema,
    EquipmentResponseSchema,
    EquipmentListResponseSchema
)
from src.database.models.equipment import EquipmentTypeEnum, EquipmentStatusEnum

# 创建路由器
router = APIRouter(prefix="/equipment-configs", tags=["设备配置管理"])


async def get_equipment_service(
    db_session: AsyncSession = Depends(get_database_session)
) -> EquipmentService:
    """
    获取设备配置服务实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        EquipmentService: 设备配置服务实例
    """
    return EquipmentService(db_session)


@router.post(
    "/",
    response_model=EquipmentResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="创建设备配置",
    description="创建新的设备配置，定义设备的基本信息和性能参数"
)
async def create_equipment_config(
    equipment_data: EquipmentCreateSchema,
    equipment_service: EquipmentService = Depends(get_equipment_service)
) -> EquipmentResponseSchema:
    """
    创建设备配置接口
    
    Args:
        equipment_data: 设备配置创建数据
        equipment_service: 设备配置服务
        
    Returns:
        EquipmentResponseSchema: 创建的设备配置信息
        
    Raises:
        HTTPException: 创建失败时抛出异常
    """
    try:
        # 验证设备配置参数
        validation_errors = await equipment_service.validate_equipment_parameters(
            equipment_data
        )
        
        if validation_errors:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail={"message": "设备配置参数验证失败", "errors": validation_errors}
            )
        
        # 创建设备配置
        equipment_config = await equipment_service.create_equipment_config(equipment_data)
        return equipment_config
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建设备配置失败: {str(e)}"
        )


@router.get(
    "/",
    response_model=EquipmentListResponseSchema,
    summary="获取设备配置列表",
    description="查询设备配置列表，支持按类型和状态筛选"
)
async def get_equipment_config_list(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    equipment_type: Optional[EquipmentTypeEnum] = Query(None, description="设备类型筛选"),
    status: Optional[EquipmentStatusEnum] = Query(None, description="运行状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    equipment_service: EquipmentService = Depends(get_equipment_service)
) -> EquipmentListResponseSchema:
    """
    获取设备配置列表接口
    
    Args:
        page: 页码
        page_size: 每页数量
        equipment_type: 设备类型筛选
        status: 运行状态筛选
        search: 搜索关键词
        equipment_service: 设备配置服务
        
    Returns:
        EquipmentListResponseSchema: 设备配置列表响应
    """
    try:
        equipment_list = await equipment_service.get_equipment_config_list(
            page=page,
            page_size=page_size,
            equipment_type=equipment_type,
            status=status,
            search_keyword=search
        )
        return equipment_list
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取设备配置列表失败: {str(e)}"
        )


@router.get(
    "/{equipment_id}",
    response_model=EquipmentResponseSchema,
    summary="获取设备配置详情",
    description="根据设备配置ID获取设备的详细信息"
)
async def get_equipment_config_detail(
    equipment_id: str,
    equipment_service: EquipmentService = Depends(get_equipment_service)
) -> EquipmentResponseSchema:
    """
    获取设备配置详情接口
    
    Args:
        equipment_id: 设备配置ID
        equipment_service: 设备配置服务
        
    Returns:
        EquipmentResponseSchema: 设备配置详细信息
        
    Raises:
        HTTPException: 设备配置不存在时抛出404异常
    """
    equipment_config = await equipment_service.get_equipment_config_by_id(equipment_id)
    
    if equipment_config is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="设备配置不存在"
        )
    
    return equipment_config


@router.put(
    "/{equipment_id}",
    response_model=EquipmentResponseSchema,
    summary="更新设备配置",
    description="更新设备配置的参数信息"
)
async def update_equipment_config(
    equipment_id: str,
    update_data: EquipmentUpdateSchema,
    equipment_service: EquipmentService = Depends(get_equipment_service)
) -> EquipmentResponseSchema:
    """
    更新设备配置接口
    
    Args:
        equipment_id: 设备配置ID
        update_data: 更新数据
        equipment_service: 设备配置服务
        
    Returns:
        EquipmentResponseSchema: 更新后的设备配置信息
        
    Raises:
        HTTPException: 设备配置不存在或更新失败时抛出异常
    """
    try:
        # 更新设备配置
        equipment_config = await equipment_service.update_equipment_config(
            equipment_id, update_data
        )
        
        if equipment_config is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="设备配置不存在"
            )
        
        return equipment_config
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新设备配置失败: {str(e)}"
        )


@router.delete(
    "/{equipment_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除设备配置",
    description="删除指定的设备配置"
)
async def delete_equipment_config(
    equipment_id: str,
    equipment_service: EquipmentService = Depends(get_equipment_service)
) -> None:
    """
    删除设备配置接口
    
    Args:
        equipment_id: 设备配置ID
        equipment_service: 设备配置服务
        
    Raises:
        HTTPException: 设备配置不存在时抛出404异常
    """
    success = await equipment_service.delete_equipment_config(equipment_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="设备配置不存在"
        )
