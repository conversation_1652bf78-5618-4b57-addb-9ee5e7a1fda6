"""
效能指标管理API路由

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含效能指标管理相关的API路由
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session
from src.services.efficiency import (
    EfficiencyIndicatorService,
    IndicatorCalculationService,
    IndicatorWeightConfigService
)
from src.services.efficiency_evaluator import EfficiencyEvaluator
from src.schemas.efficiency import (
    EfficiencyIndicatorCreateSchema,
    EfficiencyIndicatorUpdateSchema,
    EfficiencyIndicatorResponseSchema,
    IndicatorCalculationRequestSchema,
    IndicatorCalculationResultResponseSchema,
    IndicatorWeightConfigCreateSchema,
    IndicatorWeightConfigResponseSchema
)
from src.database.models.efficiency import IndicatorCategoryEnum

# 创建路由器
router = APIRouter(prefix="/efficiency", tags=["效能指标管理"])


async def get_indicator_service(
    db_session: AsyncSession = Depends(get_database_session)
) -> EfficiencyIndicatorService:
    """
    获取效能指标服务实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        EfficiencyIndicatorService: 效能指标服务实例
    """
    return EfficiencyIndicatorService(db_session)


async def get_calculation_service(
    db_session: AsyncSession = Depends(get_database_session)
) -> IndicatorCalculationService:
    """
    获取指标计算服务实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        IndicatorCalculationService: 指标计算服务实例
    """
    return IndicatorCalculationService(db_session)


async def get_weight_config_service(
    db_session: AsyncSession = Depends(get_database_session)
) -> IndicatorWeightConfigService:
    """
    获取权重配置服务实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        IndicatorWeightConfigService: 权重配置服务实例
    """
    return IndicatorWeightConfigService(db_session)


async def get_evaluator_service(
    db_session: AsyncSession = Depends(get_database_session)
) -> EfficiencyEvaluator:
    """
    获取效能评估器实例
    
    Args:
        db_session: 数据库会话
        
    Returns:
        EfficiencyEvaluator: 效能评估器实例
    """
    return EfficiencyEvaluator(db_session)


# 效能指标管理相关路由

@router.post(
    "/indicators",
    response_model=EfficiencyIndicatorResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="创建效能指标",
    description="创建新的效能指标定义，包括指标基本信息、计算公式和阈值设定"
)
async def create_indicator(
    indicator_data: EfficiencyIndicatorCreateSchema,
    service: EfficiencyIndicatorService = Depends(get_indicator_service)
) -> EfficiencyIndicatorResponseSchema:
    """
    创建效能指标
    
    Args:
        indicator_data: 指标创建数据
        service: 效能指标服务
        
    Returns:
        EfficiencyIndicatorResponseSchema: 创建的指标信息
        
    Raises:
        HTTPException: 当指标名称或编码已存在时返回400错误
    """
    try:
        return await service.create_indicator(indicator_data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get(
    "/indicators/{indicator_id}",
    response_model=EfficiencyIndicatorResponseSchema,
    summary="获取效能指标详情",
    description="根据指标ID获取效能指标的详细信息"
)
async def get_indicator(
    indicator_id: str,
    service: EfficiencyIndicatorService = Depends(get_indicator_service)
) -> EfficiencyIndicatorResponseSchema:
    """
    获取效能指标详情
    
    Args:
        indicator_id: 指标ID
        service: 效能指标服务
        
    Returns:
        EfficiencyIndicatorResponseSchema: 指标详细信息
        
    Raises:
        HTTPException: 当指标不存在时返回404错误
    """
    indicator = await service.get_indicator_by_id(indicator_id)
    if not indicator:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指标不存在"
        )
    return indicator


@router.get(
    "/indicators",
    response_model=List[EfficiencyIndicatorResponseSchema],
    summary="获取效能指标列表",
    description="获取所有激活的效能指标列表，支持按分类筛选"
)
async def get_indicators(
    category: Optional[IndicatorCategoryEnum] = Query(None, description="指标分类筛选"),
    service: EfficiencyIndicatorService = Depends(get_indicator_service)
) -> List[EfficiencyIndicatorResponseSchema]:
    """
    获取效能指标列表
    
    Args:
        category: 指标分类筛选条件
        service: 效能指标服务
        
    Returns:
        List[EfficiencyIndicatorResponseSchema]: 指标列表
    """
    if category:
        return await service.get_indicators_by_category(category)
    else:
        return await service.get_all_active_indicators()


@router.put(
    "/indicators/{indicator_id}",
    response_model=EfficiencyIndicatorResponseSchema,
    summary="更新效能指标",
    description="更新效能指标的信息，包括描述、阈值、权重等"
)
async def update_indicator(
    indicator_id: str,
    update_data: EfficiencyIndicatorUpdateSchema,
    service: EfficiencyIndicatorService = Depends(get_indicator_service)
) -> EfficiencyIndicatorResponseSchema:
    """
    更新效能指标
    
    Args:
        indicator_id: 指标ID
        update_data: 更新数据
        service: 效能指标服务
        
    Returns:
        EfficiencyIndicatorResponseSchema: 更新后的指标信息
        
    Raises:
        HTTPException: 当指标不存在时返回404错误
    """
    updated_indicator = await service.update_indicator(indicator_id, update_data)
    if not updated_indicator:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指标不存在"
        )
    return updated_indicator


@router.delete(
    "/indicators/{indicator_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除效能指标",
    description="删除效能指标（软删除，设置为已弃用状态）"
)
async def delete_indicator(
    indicator_id: str,
    service: EfficiencyIndicatorService = Depends(get_indicator_service)
) -> None:
    """
    删除效能指标
    
    Args:
        indicator_id: 指标ID
        service: 效能指标服务
        
    Raises:
        HTTPException: 当指标不存在时返回404错误
    """
    success = await service.delete_indicator(indicator_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="指标不存在"
        )


# 指标计算相关路由

@router.get(
    "/calculations/scenarios/{scenario_id}/results",
    response_model=List[IndicatorCalculationResultResponseSchema],
    summary="获取场景计算结果",
    description="获取指定场景的所有效能指标计算结果"
)
async def get_scenario_calculation_results(
    scenario_id: str,
    service: IndicatorCalculationService = Depends(get_calculation_service)
) -> List[IndicatorCalculationResultResponseSchema]:
    """
    获取场景计算结果
    
    Args:
        scenario_id: 场景ID
        service: 指标计算服务
        
    Returns:
        List[IndicatorCalculationResultResponseSchema]: 计算结果列表
    """
    return await service.get_calculation_results_by_scenario(scenario_id)


# 权重配置相关路由

@router.post(
    "/weight-configs",
    response_model=IndicatorWeightConfigResponseSchema,
    status_code=status.HTTP_201_CREATED,
    summary="创建权重配置",
    description="创建新的指标权重配置，用于不同场景类型的综合评估"
)
async def create_weight_config(
    config_data: IndicatorWeightConfigCreateSchema,
    service: IndicatorWeightConfigService = Depends(get_weight_config_service)
) -> IndicatorWeightConfigResponseSchema:
    """
    创建权重配置
    
    Args:
        config_data: 权重配置数据
        service: 权重配置服务
        
    Returns:
        IndicatorWeightConfigResponseSchema: 创建的权重配置信息
        
    Raises:
        HTTPException: 当权重总和不等于1时返回400错误
    """
    try:
        return await service.create_weight_config(config_data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get(
    "/weight-configs/default/{scenario_type}",
    response_model=IndicatorWeightConfigResponseSchema,
    summary="获取默认权重配置",
    description="获取指定场景类型的默认权重配置"
)
async def get_default_weight_config(
    scenario_type: str,
    service: IndicatorWeightConfigService = Depends(get_weight_config_service)
) -> IndicatorWeightConfigResponseSchema:
    """
    获取默认权重配置
    
    Args:
        scenario_type: 场景类型
        service: 权重配置服务
        
    Returns:
        IndicatorWeightConfigResponseSchema: 默认权重配置信息
        
    Raises:
        HTTPException: 当配置不存在时返回404错误
    """
    config = await service.get_default_weight_config(scenario_type)
    if not config:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"未找到场景类型 {scenario_type} 的默认权重配置"
        )
    return config


# 综合评估相关路由

@router.get(
    "/evaluations/scenarios/{scenario_id}/comprehensive-score",
    response_model=Dict[str, float],
    summary="计算综合效能评分",
    description="计算指定场景的综合效能评分，包括各分类评分和总评分"
)
async def calculate_comprehensive_score(
    scenario_id: str,
    scenario_type: str = Query(..., description="场景类型"),
    evaluator: EfficiencyEvaluator = Depends(get_evaluator_service)
) -> Dict[str, float]:
    """
    计算综合效能评分
    
    Args:
        scenario_id: 场景ID
        scenario_type: 场景类型
        evaluator: 效能评估器
        
    Returns:
        Dict[str, float]: 综合评分结果
        
    Raises:
        HTTPException: 当场景或权重配置不存在时返回404错误
    """
    try:
        scores = await evaluator.calculate_comprehensive_score(scenario_id, scenario_type)
        # 将Decimal转换为float以便JSON序列化
        return {key: float(value) for key, value in scores.items()}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
