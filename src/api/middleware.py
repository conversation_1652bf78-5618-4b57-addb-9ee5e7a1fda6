"""
API中间件

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 使用描述性的函数命名
- 模块职责单一性：只包含API中间件相关功能
"""

import time
import uuid
from typing import Callable
from fastapi import Request, Response
from fastapi.responses import JSONResponse
import structlog

# 配置结构化日志
logger = structlog.get_logger()


async def add_request_id_middleware(request: Request, call_next: Callable) -> Response:
    """
    添加请求ID中间件
    
    为每个请求生成唯一的请求ID，用于日志追踪
    
    Args:
        request: HTTP请求对象
        call_next: 下一个中间件或路由处理器
        
    Returns:
        Response: HTTP响应对象
    """
    # 生成请求ID
    request_id = str(uuid.uuid4())
    
    # 将请求ID添加到请求状态中
    request.state.request_id = request_id
    
    # 处理请求
    response = await call_next(request)
    
    # 将请求ID添加到响应头中
    response.headers["X-Request-ID"] = request_id
    
    return response


async def add_process_time_middleware(request: Request, call_next: Callable) -> Response:
    """
    添加处理时间中间件
    
    记录请求的处理时间，用于性能监控
    
    Args:
        request: HTTP请求对象
        call_next: 下一个中间件或路由处理器
        
    Returns:
        Response: HTTP响应对象
    """
    # 记录开始时间
    start_time = time.time()
    
    # 处理请求
    response = await call_next(request)
    
    # 计算处理时间
    process_time = time.time() - start_time
    
    # 将处理时间添加到响应头中
    response.headers["X-Process-Time"] = str(process_time)
    
    # 记录日志
    logger.info(
        "request_processed",
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        process_time=process_time,
        request_id=getattr(request.state, 'request_id', 'unknown')
    )
    
    return response


async def add_error_handling_middleware(request: Request, call_next: Callable) -> Response:
    """
    添加错误处理中间件
    
    统一处理未捕获的异常，返回标准化的错误响应
    
    Args:
        request: HTTP请求对象
        call_next: 下一个中间件或路由处理器
        
    Returns:
        Response: HTTP响应对象
    """
    try:
        response = await call_next(request)
        return response
    except Exception as e:
        # 记录错误日志
        logger.error(
            "unhandled_exception",
            error=str(e),
            method=request.method,
            url=str(request.url),
            request_id=getattr(request.state, 'request_id', 'unknown'),
            exc_info=True
        )
        
        # 返回标准化错误响应
        return JSONResponse(
            status_code=500,
            content={
                "code": 500,
                "message": "内部服务器错误",
                "detail": "服务器遇到了一个意外的错误，请稍后重试",
                "request_id": getattr(request.state, 'request_id', 'unknown')
            }
        )


def create_standard_response(
    data: any = None,
    message: str = "success",
    code: int = 200
) -> dict:
    """
    创建标准化响应格式
    
    Args:
        data: 响应数据
        message: 响应消息
        code: 响应代码
        
    Returns:
        dict: 标准化响应字典
    """
    response = {
        "code": code,
        "message": message,
        "timestamp": time.time()
    }
    
    if data is not None:
        response["data"] = data
    
    return response
