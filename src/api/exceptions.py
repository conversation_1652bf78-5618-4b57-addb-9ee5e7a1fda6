"""
API异常处理

遵循base-rules.md规范：
- 使用描述性的类和异常命名
- 类文档字符串说明类的用途和主要功能
- 模块职责单一性：只包含API异常处理相关功能
"""

from typing import Any, Dict, Optional
from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from starlette.exceptions import HTTPException as StarletteHTTPException
import structlog

# 配置结构化日志
logger = structlog.get_logger()


class BusinessLogicError(Exception):
    """
    业务逻辑错误基类
    
    用于表示业务逻辑层的错误
    """
    
    def __init__(
        self,
        message: str,
        error_code: str = "BUSINESS_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        """
        初始化业务逻辑错误
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 错误详情
        """
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ValidationError(BusinessLogicError):
    """
    数据验证错误
    
    用于表示数据验证失败的错误
    """
    
    def __init__(
        self,
        message: str,
        field_errors: Optional[Dict[str, str]] = None
    ):
        """
        初始化数据验证错误
        
        Args:
            message: 错误消息
            field_errors: 字段错误详情
        """
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            details={"field_errors": field_errors or {}}
        )


class ResourceNotFoundError(BusinessLogicError):
    """
    资源未找到错误
    
    用于表示请求的资源不存在
    """
    
    def __init__(self, resource_name: str, resource_id: str):
        """
        初始化资源未找到错误
        
        Args:
            resource_name: 资源名称
            resource_id: 资源ID
        """
        super().__init__(
            message=f"{resource_name}不存在",
            error_code="RESOURCE_NOT_FOUND",
            details={"resource_name": resource_name, "resource_id": resource_id}
        )


class PermissionDeniedError(BusinessLogicError):
    """
    权限拒绝错误
    
    用于表示用户没有执行操作的权限
    """
    
    def __init__(self, operation: str, resource: str = ""):
        """
        初始化权限拒绝错误
        
        Args:
            operation: 操作名称
            resource: 资源名称
        """
        message = f"没有权限执行操作: {operation}"
        if resource:
            message += f" (资源: {resource})"
        
        super().__init__(
            message=message,
            error_code="PERMISSION_DENIED",
            details={"operation": operation, "resource": resource}
        )


class ConfigurationError(BusinessLogicError):
    """
    配置错误
    
    用于表示配置相关的错误
    """
    
    def __init__(self, message: str, config_key: str = ""):
        """
        初始化配置错误
        
        Args:
            message: 错误消息
            config_key: 配置键名
        """
        super().__init__(
            message=message,
            error_code="CONFIGURATION_ERROR",
            details={"config_key": config_key}
        )


async def business_logic_exception_handler(
    request: Request,
    exc: BusinessLogicError
) -> JSONResponse:
    """
    业务逻辑异常处理器
    
    Args:
        request: HTTP请求对象
        exc: 业务逻辑异常
        
    Returns:
        JSONResponse: 标准化错误响应
    """
    # 根据错误类型确定HTTP状态码
    status_code_map = {
        "VALIDATION_ERROR": status.HTTP_400_BAD_REQUEST,
        "RESOURCE_NOT_FOUND": status.HTTP_404_NOT_FOUND,
        "PERMISSION_DENIED": status.HTTP_403_FORBIDDEN,
        "CONFIGURATION_ERROR": status.HTTP_500_INTERNAL_SERVER_ERROR,
    }
    
    status_code = status_code_map.get(exc.error_code, status.HTTP_400_BAD_REQUEST)
    
    # 记录日志
    logger.warning(
        "business_logic_error",
        error_code=exc.error_code,
        message=exc.message,
        details=exc.details,
        request_id=getattr(request.state, 'request_id', 'unknown')
    )
    
    # 返回标准化错误响应
    return JSONResponse(
        status_code=status_code,
        content={
            "code": status_code,
            "message": exc.message,
            "error_code": exc.error_code,
            "details": exc.details,
            "request_id": getattr(request.state, 'request_id', 'unknown')
        }
    )


async def validation_exception_handler(
    request: Request,
    exc: RequestValidationError
) -> JSONResponse:
    """
    请求验证异常处理器
    
    Args:
        request: HTTP请求对象
        exc: 请求验证异常
        
    Returns:
        JSONResponse: 标准化错误响应
    """
    # 提取验证错误详情
    field_errors = {}
    for error in exc.errors():
        field_path = ".".join(str(loc) for loc in error["loc"][1:])  # 跳过'body'
        field_errors[field_path] = error["msg"]
    
    # 记录日志
    logger.warning(
        "validation_error",
        errors=exc.errors(),
        request_id=getattr(request.state, 'request_id', 'unknown')
    )
    
    # 返回标准化错误响应
    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={
            "code": status.HTTP_422_UNPROCESSABLE_ENTITY,
            "message": "请求数据验证失败",
            "error_code": "VALIDATION_ERROR",
            "details": {"field_errors": field_errors},
            "request_id": getattr(request.state, 'request_id', 'unknown')
        }
    )


async def http_exception_handler(
    request: Request,
    exc: StarletteHTTPException
) -> JSONResponse:
    """
    HTTP异常处理器
    
    Args:
        request: HTTP请求对象
        exc: HTTP异常
        
    Returns:
        JSONResponse: 标准化错误响应
    """
    # 记录日志
    logger.warning(
        "http_exception",
        status_code=exc.status_code,
        detail=exc.detail,
        request_id=getattr(request.state, 'request_id', 'unknown')
    )
    
    # 返回标准化错误响应
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "code": exc.status_code,
            "message": exc.detail,
            "error_code": "HTTP_ERROR",
            "request_id": getattr(request.state, 'request_id', 'unknown')
        }
    )


def register_exception_handlers(app) -> None:
    """
    注册异常处理器
    
    Args:
        app: FastAPI应用实例
    """
    app.add_exception_handler(BusinessLogicError, business_logic_exception_handler)
    app.add_exception_handler(RequestValidationError, validation_exception_handler)
    app.add_exception_handler(StarletteHTTPException, http_exception_handler)
