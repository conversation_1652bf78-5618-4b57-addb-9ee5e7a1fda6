"""
效能指标数据传输对象

遵循base-rules.md规范：
- 使用描述性的类和属性命名
- 类文档字符串说明类的用途和主要功能
- 模块职责单一性：只包含效能指标相关的DTO
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from decimal import Decimal
from pydantic import BaseModel, Field, ConfigDict
from src.database.models.efficiency import (
    IndicatorCategoryEnum, 
    IndicatorStatusEnum,
    CalculationStatusEnum
)


class EfficiencyIndicatorCreateSchema(BaseModel):
    """创建效能指标的请求模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    indicator_name: str = Field(
        ...,
        min_length=1,
        max_length=200,
        description="指标名称"
    )
    
    indicator_code: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="指标编码"
    )
    
    category: IndicatorCategoryEnum = Field(
        ...,
        description="指标分类"
    )
    
    description: Optional[str] = Field(
        None,
        description="指标详细描述"
    )
    
    calculation_formula: Optional[str] = Field(
        None,
        description="计算公式说明"
    )
    
    unit: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="计量单位"
    )
    
    min_threshold: Optional[Decimal] = Field(
        None,
        description="最小阈值"
    )
    
    max_threshold: Optional[Decimal] = Field(
        None,
        description="最大阈值"
    )
    
    target_value: Optional[Decimal] = Field(
        None,
        description="目标值"
    )
    
    default_weight: Decimal = Field(
        default=Decimal('0.1000'),
        ge=0,
        le=1,
        description="默认权重"
    )
    
    priority_level: int = Field(
        default=1,
        ge=1,
        le=10,
        description="优先级等级"
    )
    
    created_by: Optional[str] = Field(
        None,
        max_length=100,
        description="创建者"
    )
    
    metadata_info: Dict[str, Any] = Field(
        default_factory=dict,
        description="元数据信息"
    )


class EfficiencyIndicatorUpdateSchema(BaseModel):
    """更新效能指标的请求模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    indicator_name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=200,
        description="指标名称"
    )
    
    description: Optional[str] = Field(
        None,
        description="指标详细描述"
    )
    
    calculation_formula: Optional[str] = Field(
        None,
        description="计算公式说明"
    )
    
    unit: Optional[str] = Field(
        None,
        min_length=1,
        max_length=50,
        description="计量单位"
    )
    
    min_threshold: Optional[Decimal] = Field(
        None,
        description="最小阈值"
    )
    
    max_threshold: Optional[Decimal] = Field(
        None,
        description="最大阈值"
    )
    
    target_value: Optional[Decimal] = Field(
        None,
        description="目标值"
    )
    
    default_weight: Optional[Decimal] = Field(
        None,
        ge=0,
        le=1,
        description="默认权重"
    )
    
    priority_level: Optional[int] = Field(
        None,
        ge=1,
        le=10,
        description="优先级等级"
    )
    
    status: Optional[IndicatorStatusEnum] = Field(
        None,
        description="指标状态"
    )
    
    metadata_info: Optional[Dict[str, Any]] = Field(
        None,
        description="元数据信息"
    )


class EfficiencyIndicatorResponseSchema(BaseModel):
    """效能指标响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(..., description="指标ID")
    indicator_name: str = Field(..., description="指标名称")
    indicator_code: str = Field(..., description="指标编码")
    category: IndicatorCategoryEnum = Field(..., description="指标分类")
    description: Optional[str] = Field(None, description="指标详细描述")
    calculation_formula: Optional[str] = Field(None, description="计算公式说明")
    unit: str = Field(..., description="计量单位")
    min_threshold: Optional[Decimal] = Field(None, description="最小阈值")
    max_threshold: Optional[Decimal] = Field(None, description="最大阈值")
    target_value: Optional[Decimal] = Field(None, description="目标值")
    default_weight: Decimal = Field(..., description="默认权重")
    priority_level: int = Field(..., description="优先级等级")
    status: IndicatorStatusEnum = Field(..., description="指标状态")
    created_by: Optional[str] = Field(None, description="创建者")
    metadata_info: Dict[str, Any] = Field(..., description="元数据信息")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class IndicatorCalculationRequestSchema(BaseModel):
    """指标计算请求模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    scenario_id: str = Field(
        ...,
        description="关联场景ID"
    )
    
    indicator_ids: List[str] = Field(
        ...,
        min_length=1,
        description="需要计算的指标ID列表"
    )
    
    calculation_method: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="使用的计算方法"
    )
    
    input_parameters: Dict[str, Any] = Field(
        default_factory=dict,
        description="输入参数"
    )


class IndicatorCalculationResultResponseSchema(BaseModel):
    """指标计算结果响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(..., description="计算结果ID")
    scenario_id: str = Field(..., description="关联场景ID")
    indicator_id: str = Field(..., description="关联指标ID")
    calculated_value: Decimal = Field(..., description="计算得出的数值")
    confidence_level: Optional[Decimal] = Field(None, description="置信度")
    calculation_method: str = Field(..., description="使用的计算方法")
    calculation_status: CalculationStatusEnum = Field(..., description="计算状态")
    input_parameters: Dict[str, Any] = Field(..., description="输入参数")
    calculation_metadata: Dict[str, Any] = Field(..., description="计算元数据")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class IndicatorWeightConfigCreateSchema(BaseModel):
    """创建指标权重配置的请求模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    config_name: str = Field(
        ...,
        min_length=1,
        max_length=200,
        description="配置名称"
    )
    
    scenario_type: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="适用的场景类型"
    )
    
    description: Optional[str] = Field(
        None,
        description="配置说明"
    )
    
    weight_settings: Dict[str, Decimal] = Field(
        ...,
        description="各指标的权重分配"
    )
    
    is_default: bool = Field(
        default=False,
        description="是否为默认配置"
    )
    
    created_by: Optional[str] = Field(
        None,
        max_length=100,
        description="创建者"
    )


class IndicatorWeightConfigResponseSchema(BaseModel):
    """指标权重配置响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(..., description="配置ID")
    config_name: str = Field(..., description="配置名称")
    scenario_type: str = Field(..., description="适用的场景类型")
    description: Optional[str] = Field(None, description="配置说明")
    weight_settings: Dict[str, Any] = Field(..., description="各指标的权重分配")
    is_default: bool = Field(..., description="是否为默认配置")
    is_active: bool = Field(..., description="是否激活")
    created_by: Optional[str] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
