"""
配置方案数据传输对象

遵循base-rules.md规范：
- 使用描述性的类和属性命名
- 类文档字符串说明类的用途和主要功能
- 模块职责单一性：只包含配置方案相关的DTO
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from src.database.models.configuration import SchemeTypeEnum, SchemeStatusEnum


class OperationalParametersSchema(BaseModel):
    """作业参数模式"""
    
    max_operation_duration: float = Field(
        gt=0,
        description="最大作业时长（小时）"
    )
    
    safety_margin: float = Field(
        ge=0,
        le=1,
        description="安全裕度（0-1）"
    )
    
    efficiency_target: float = Field(
        ge=0,
        le=1,
        description="效率目标（0-1）"
    )
    
    priority_level: str = Field(
        default="medium",
        description="优先级别"
    )


class ConfigurationSchemeCreateSchema(BaseModel):
    """创建配置方案请求模式"""
    
    scheme_name: str = Field(
        min_length=1,
        max_length=200,
        description="方案名称"
    )
    
    scheme_type: SchemeTypeEnum = Field(
        description="方案类型"
    )
    
    description: Optional[str] = Field(
        default=None,
        description="方案详细描述"
    )
    
    equipment_config_ids: List[str] = Field(
        default_factory=list,
        description="设备配置ID列表"
    )
    
    aircraft_config_ids: List[str] = Field(
        default_factory=list,
        description="飞机配置ID列表"
    )
    
    personnel_config_ids: List[str] = Field(
        default_factory=list,
        description="人员配置ID列表"
    )
    
    weather_condition_ids: List[str] = Field(
        default_factory=list,
        description="气象条件ID列表"
    )
    
    scenario_parameters: Dict[str, Any] = Field(
        default_factory=dict,
        description="场景参数"
    )
    
    operational_parameters: OperationalParametersSchema = Field(
        description="作业参数"
    )
    
    tags: List[str] = Field(
        default_factory=list,
        description="标签列表"
    )
    
    category: Optional[str] = Field(
        default=None,
        max_length=100,
        description="分类"
    )
    
    created_by: Optional[str] = Field(
        default=None,
        max_length=100,
        description="创建者"
    )


class ConfigurationSchemeUpdateSchema(BaseModel):
    """更新配置方案请求模式"""
    
    scheme_name: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=200,
        description="方案名称"
    )
    
    scheme_type: Optional[SchemeTypeEnum] = Field(
        default=None,
        description="方案类型"
    )
    
    description: Optional[str] = Field(
        default=None,
        description="方案详细描述"
    )
    
    equipment_config_ids: Optional[List[str]] = Field(
        default=None,
        description="设备配置ID列表"
    )
    
    aircraft_config_ids: Optional[List[str]] = Field(
        default=None,
        description="飞机配置ID列表"
    )
    
    personnel_config_ids: Optional[List[str]] = Field(
        default=None,
        description="人员配置ID列表"
    )
    
    weather_condition_ids: Optional[List[str]] = Field(
        default=None,
        description="气象条件ID列表"
    )
    
    scenario_parameters: Optional[Dict[str, Any]] = Field(
        default=None,
        description="场景参数"
    )
    
    operational_parameters: Optional[OperationalParametersSchema] = Field(
        default=None,
        description="作业参数"
    )
    
    tags: Optional[List[str]] = Field(
        default=None,
        description="标签列表"
    )
    
    category: Optional[str] = Field(
        default=None,
        max_length=100,
        description="分类"
    )
    
    status: Optional[SchemeStatusEnum] = Field(
        default=None,
        description="方案状态"
    )
    
    rating: Optional[float] = Field(
        default=None,
        ge=1,
        le=5,
        description="评分（1-5）"
    )


class ConfigurationSchemeResponseSchema(BaseModel):
    """配置方案响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(description="方案ID")
    scheme_name: str = Field(description="方案名称")
    scheme_type: SchemeTypeEnum = Field(description="方案类型")
    description: Optional[str] = Field(description="方案描述")
    version: str = Field(description="版本号")
    equipment_config_ids: List[str] = Field(description="设备配置ID列表")
    aircraft_config_ids: List[str] = Field(description="飞机配置ID列表")
    personnel_config_ids: List[str] = Field(description="人员配置ID列表")
    weather_condition_ids: List[str] = Field(description="气象条件ID列表")
    scenario_parameters: Dict[str, Any] = Field(description="场景参数")
    operational_parameters: Dict[str, Any] = Field(description="作业参数")
    tags: List[str] = Field(description="标签列表")
    category: Optional[str] = Field(description="分类")
    created_by: Optional[str] = Field(description="创建者")
    status: SchemeStatusEnum = Field(description="方案状态")
    usage_count: int = Field(description="使用次数")
    rating: Optional[float] = Field(description="评分")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")


class ConfigurationSchemeListResponseSchema(BaseModel):
    """配置方案列表响应模式"""
    
    schemes: List[ConfigurationSchemeResponseSchema] = Field(
        description="配置方案列表"
    )
    total: int = Field(
        description="总数量"
    )
    page: int = Field(
        description="当前页码"
    )
    page_size: int = Field(
        description="每页数量"
    )
    total_pages: int = Field(
        description="总页数"
    )


class SchemeValidationResultSchema(BaseModel):
    """方案验证结果模式"""
    
    overall_status: str = Field(
        description="总体状态"
    )
    
    validation_errors: List[str] = Field(
        description="验证错误列表"
    )
    
    validation_warnings: List[str] = Field(
        description="验证警告列表"
    )
    
    resource_compatibility: Dict[str, Any] = Field(
        description="资源兼容性分析"
    )
    
    optimization_suggestions: List[Dict[str, Any]] = Field(
        description="优化建议列表"
    )
    
    performance_prediction: Dict[str, Any] = Field(
        description="性能预测"
    )


class SchemeVersionResponseSchema(BaseModel):
    """方案版本响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(description="版本ID")
    scheme_id: str = Field(description="方案ID")
    version_number: str = Field(description="版本号")
    change_description: Optional[str] = Field(description="变更描述")
    configuration_snapshot: Dict[str, Any] = Field(description="配置数据快照")
    is_current: bool = Field(description="是否为当前版本")
    created_by: Optional[str] = Field(description="创建者")
    created_at: datetime = Field(description="创建时间")


class SchemeVersionListResponseSchema(BaseModel):
    """方案版本列表响应模式"""
    
    versions: List[SchemeVersionResponseSchema] = Field(
        description="版本列表"
    )
    total: int = Field(
        description="总数量"
    )
