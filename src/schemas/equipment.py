"""
设备配置数据传输对象

遵循base-rules.md规范：
- 使用描述性的类和属性命名
- 类文档字符串说明类的用途和主要功能
- 模块职责单一性：只包含设备配置相关的DTO
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from src.database.models.equipment import EquipmentTypeEnum, EquipmentStatusEnum, OperationStageEnum


class EquipmentCreateSchema(BaseModel):
    """创建设备配置请求模式"""
    
    equipment_type: EquipmentTypeEnum = Field(
        description="设备类型"
    )
    
    equipment_model: str = Field(
        min_length=1,
        max_length=100,
        description="设备型号"
    )
    
    quantity: int = Field(
        ge=1,
        description="设备数量"
    )
    
    max_load_capacity: float = Field(
        gt=0,
        description="最大载重能力（kg）"
    )
    
    loading_speed: float = Field(
        gt=0,
        description="装载速度（kg/h）"
    )
    
    unloading_speed: float = Field(
        gt=0,
        description="卸载速度（kg/h）"
    )
    
    operation_radius: float = Field(
        gt=0,
        description="作业半径（m）"
    )
    
    power_consumption: Optional[float] = Field(
        default=None,
        ge=0,
        description="功耗（kW）"
    )
    
    unit_operation_duration: float = Field(
        gt=0,
        description="单位作业时长（h）"
    )
    
    efficiency_factor: float = Field(
        ge=0,
        le=1,
        default=1.0,
        description="效率系数（0-1）"
    )
    
    maintenance_interval: int = Field(
        gt=0,
        description="维护间隔（小时）"
    )
    
    operational_status: EquipmentStatusEnum = Field(
        default=EquipmentStatusEnum.AVAILABLE,
        description="运行状态"
    )
    
    maintenance_info: Dict[str, Any] = Field(
        default_factory=dict,
        description="维护信息"
    )
    
    applicable_stages: List[OperationStageEnum] = Field(
        default_factory=list,
        description="适用的作业环节列表"
    )

    remarks: Optional[str] = Field(
        default=None,
        max_length=500,
        description="备注信息"
    )


class EquipmentUpdateSchema(BaseModel):
    """更新设备配置请求模式"""
    
    equipment_type: Optional[EquipmentTypeEnum] = Field(
        default=None,
        description="设备类型"
    )
    
    equipment_model: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=100,
        description="设备型号"
    )
    
    quantity: Optional[int] = Field(
        default=None,
        ge=1,
        description="设备数量"
    )
    
    max_load_capacity: Optional[float] = Field(
        default=None,
        gt=0,
        description="最大载重能力（kg）"
    )
    
    loading_speed: Optional[float] = Field(
        default=None,
        gt=0,
        description="装载速度（kg/h）"
    )
    
    unloading_speed: Optional[float] = Field(
        default=None,
        gt=0,
        description="卸载速度（kg/h）"
    )
    
    operation_radius: Optional[float] = Field(
        default=None,
        gt=0,
        description="作业半径（m）"
    )
    
    power_consumption: Optional[float] = Field(
        default=None,
        ge=0,
        description="功耗（kW）"
    )
    
    unit_operation_duration: Optional[float] = Field(
        default=None,
        gt=0,
        description="单位作业时长（h）"
    )
    
    efficiency_factor: Optional[float] = Field(
        default=None,
        ge=0,
        le=1,
        description="效率系数（0-1）"
    )
    
    maintenance_interval: Optional[int] = Field(
        default=None,
        gt=0,
        description="维护间隔（小时）"
    )
    
    operational_status: Optional[EquipmentStatusEnum] = Field(
        default=None,
        description="运行状态"
    )
    
    maintenance_info: Optional[Dict[str, Any]] = Field(
        default=None,
        description="维护信息"
    )

    applicable_stages: Optional[List[OperationStageEnum]] = Field(
        default=None,
        description="适用的作业环节列表"
    )

    remarks: Optional[str] = Field(
        default=None,
        max_length=500,
        description="备注信息"
    )


class EquipmentResponseSchema(BaseModel):
    """设备配置响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(description="设备配置ID")
    equipment_type: EquipmentTypeEnum = Field(description="设备类型")
    equipment_model: str = Field(description="设备型号")
    quantity: int = Field(description="设备数量")
    max_load_capacity: float = Field(description="最大载重能力（kg）")
    loading_speed: float = Field(description="装载速度（kg/h）")
    unloading_speed: float = Field(description="卸载速度（kg/h）")
    operation_radius: float = Field(description="作业半径（m）")
    power_consumption: Optional[float] = Field(description="功耗（kW）")
    unit_operation_duration: float = Field(description="单位作业时长（h）")
    efficiency_factor: float = Field(description="效率系数（0-1）")
    maintenance_interval: int = Field(description="维护间隔（小时）")
    operational_status: EquipmentStatusEnum = Field(description="运行状态")
    maintenance_info: Dict[str, Any] = Field(description="维护信息")
    applicable_stages: List[OperationStageEnum] = Field(description="适用的作业环节列表")
    remarks: Optional[str] = Field(description="备注信息")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")


class EquipmentListResponseSchema(BaseModel):
    """设备配置列表响应模式"""
    
    equipment_configs: List[EquipmentResponseSchema] = Field(
        description="设备配置列表"
    )
    total: int = Field(
        description="总数量"
    )
    page: int = Field(
        description="当前页码"
    )
    page_size: int = Field(
        description="每页数量"
    )
    total_pages: int = Field(
        description="总页数"
    )
