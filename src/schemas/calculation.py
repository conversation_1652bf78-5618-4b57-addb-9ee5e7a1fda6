"""
计算模型数据传输对象

遵循base-rules.md规范：
- 使用描述性的类和属性命名
- 类文档字符串说明类的用途和主要功能
- 模块职责单一性：只包含计算模型相关的DTO
"""

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from src.database.models.calculation import (
    ModelTypeEnum,
    ModelStatusEnum,
    TaskStatusEnum,
    TaskPriorityEnum
)


class CalculationModelCreateSchema(BaseModel):
    """创建计算模型的请求模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    model_name: str = Field(
        ...,
        min_length=1,
        max_length=200,
        description="模型名称"
    )
    
    model_code: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="模型编码"
    )
    
    model_type: ModelTypeEnum = Field(
        ...,
        description="模型类型"
    )
    
    description: Optional[str] = Field(
        None,
        description="模型详细描述"
    )
    
    algorithm_category: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="算法类别"
    )
    
    implementation_class: str = Field(
        ...,
        min_length=1,
        max_length=200,
        description="实现类路径"
    )
    
    input_parameters_schema: Dict[str, Any] = Field(
        default_factory=dict,
        description="输入参数定义"
    )
    
    output_parameters_schema: Dict[str, Any] = Field(
        default_factory=dict,
        description="输出参数定义"
    )
    
    default_config: Dict[str, Any] = Field(
        default_factory=dict,
        description="默认配置信息"
    )
    
    estimated_execution_time: Optional[int] = Field(
        None,
        ge=0,
        description="预估执行时间（秒）"
    )
    
    memory_requirement: Optional[int] = Field(
        None,
        ge=0,
        description="内存需求（MB）"
    )
    
    is_parallel_supported: bool = Field(
        default=False,
        description="是否支持并行计算"
    )
    
    created_by: Optional[str] = Field(
        None,
        max_length=100,
        description="创建者"
    )
    
    version: str = Field(
        default="1.0.0",
        max_length=20,
        description="模型版本"
    )
    
    metadata_info: Dict[str, Any] = Field(
        default_factory=dict,
        description="元数据信息"
    )


class CalculationModelUpdateSchema(BaseModel):
    """更新计算模型的请求模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    model_name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=200,
        description="模型名称"
    )
    
    description: Optional[str] = Field(
        None,
        description="模型详细描述"
    )
    
    algorithm_category: Optional[str] = Field(
        None,
        min_length=1,
        max_length=100,
        description="算法类别"
    )
    
    implementation_class: Optional[str] = Field(
        None,
        min_length=1,
        max_length=200,
        description="实现类路径"
    )
    
    input_parameters_schema: Optional[Dict[str, Any]] = Field(
        None,
        description="输入参数定义"
    )
    
    output_parameters_schema: Optional[Dict[str, Any]] = Field(
        None,
        description="输出参数定义"
    )
    
    default_config: Optional[Dict[str, Any]] = Field(
        None,
        description="默认配置信息"
    )
    
    estimated_execution_time: Optional[int] = Field(
        None,
        ge=0,
        description="预估执行时间（秒）"
    )
    
    memory_requirement: Optional[int] = Field(
        None,
        ge=0,
        description="内存需求（MB）"
    )
    
    status: Optional[ModelStatusEnum] = Field(
        None,
        description="模型状态"
    )
    
    is_parallel_supported: Optional[bool] = Field(
        None,
        description="是否支持并行计算"
    )
    
    version: Optional[str] = Field(
        None,
        max_length=20,
        description="模型版本"
    )
    
    metadata_info: Optional[Dict[str, Any]] = Field(
        None,
        description="元数据信息"
    )


class CalculationModelResponseSchema(BaseModel):
    """计算模型响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(..., description="模型ID")
    model_name: str = Field(..., description="模型名称")
    model_code: str = Field(..., description="模型编码")
    model_type: ModelTypeEnum = Field(..., description="模型类型")
    description: Optional[str] = Field(None, description="模型详细描述")
    algorithm_category: str = Field(..., description="算法类别")
    implementation_class: str = Field(..., description="实现类路径")
    input_parameters_schema: Dict[str, Any] = Field(..., description="输入参数定义")
    output_parameters_schema: Dict[str, Any] = Field(..., description="输出参数定义")
    default_config: Dict[str, Any] = Field(..., description="默认配置信息")
    estimated_execution_time: Optional[int] = Field(None, description="预估执行时间（秒）")
    memory_requirement: Optional[int] = Field(None, description="内存需求（MB）")
    status: ModelStatusEnum = Field(..., description="模型状态")
    is_parallel_supported: bool = Field(..., description="是否支持并行计算")
    created_by: Optional[str] = Field(None, description="创建者")
    version: str = Field(..., description="模型版本")
    metadata_info: Dict[str, Any] = Field(..., description="元数据信息")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class CalculationTaskCreateSchema(BaseModel):
    """创建计算任务的请求模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    task_name: str = Field(
        ...,
        min_length=1,
        max_length=200,
        description="任务名称"
    )
    
    task_description: Optional[str] = Field(
        None,
        description="任务描述"
    )
    
    scenario_id: str = Field(
        ...,
        description="关联场景ID"
    )
    
    model_id: str = Field(
        ...,
        description="关联模型ID"
    )
    
    input_data: Dict[str, Any] = Field(
        default_factory=dict,
        description="输入数据"
    )
    
    parameters_config: Dict[str, Any] = Field(
        default_factory=dict,
        description="参数配置"
    )
    
    priority: TaskPriorityEnum = Field(
        default=TaskPriorityEnum.NORMAL,
        description="任务优先级"
    )
    
    created_by: Optional[str] = Field(
        None,
        max_length=100,
        description="创建者"
    )


class CalculationTaskUpdateSchema(BaseModel):
    """更新计算任务的请求模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    task_name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=200,
        description="任务名称"
    )
    
    task_description: Optional[str] = Field(
        None,
        description="任务描述"
    )
    
    input_data: Optional[Dict[str, Any]] = Field(
        None,
        description="输入数据"
    )
    
    parameters_config: Optional[Dict[str, Any]] = Field(
        None,
        description="参数配置"
    )
    
    status: Optional[TaskStatusEnum] = Field(
        None,
        description="任务状态"
    )
    
    priority: Optional[TaskPriorityEnum] = Field(
        None,
        description="任务优先级"
    )
    
    execution_progress: Optional[int] = Field(
        None,
        ge=0,
        le=100,
        description="执行进度（百分比）"
    )
    
    error_message: Optional[str] = Field(
        None,
        description="错误信息"
    )
    
    error_details: Optional[Dict[str, Any]] = Field(
        None,
        description="错误详情"
    )
    
    result_data: Optional[Dict[str, Any]] = Field(
        None,
        description="结果数据"
    )


class CalculationTaskResponseSchema(BaseModel):
    """计算任务响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(..., description="任务ID")
    task_name: str = Field(..., description="任务名称")
    task_description: Optional[str] = Field(None, description="任务描述")
    scenario_id: str = Field(..., description="关联场景ID")
    model_id: str = Field(..., description="关联模型ID")
    input_data: Dict[str, Any] = Field(..., description="输入数据")
    parameters_config: Dict[str, Any] = Field(..., description="参数配置")
    status: TaskStatusEnum = Field(..., description="任务状态")
    priority: TaskPriorityEnum = Field(..., description="任务优先级")
    execution_progress: int = Field(..., description="执行进度（百分比）")
    started_at: Optional[str] = Field(None, description="开始执行时间")
    completed_at: Optional[str] = Field(None, description="完成时间")
    execution_duration: Optional[int] = Field(None, description="执行时长（秒）")
    error_message: Optional[str] = Field(None, description="错误信息")
    error_details: Dict[str, Any] = Field(..., description="错误详情")
    result_data: Dict[str, Any] = Field(..., description="结果数据")
    created_by: Optional[str] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
