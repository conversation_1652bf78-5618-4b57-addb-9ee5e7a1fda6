"""
运输车辆配置数据传输对象

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含运输车辆配置相关的Schema定义
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from src.database.models.vehicle import VehicleTypeEnum, VehicleStatusEnum


class VehicleCreateSchema(BaseModel):
    """
    运输车辆配置创建Schema
    
    用于创建新的运输车辆配置时的数据验证和传输
    """
    
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        use_enum_values=True
    )
    
    # 车辆基本信息
    vehicle_type: VehicleTypeEnum = Field(
        ...,
        description="车辆类型"
    )
    
    vehicle_model: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="车辆型号"
    )
    
    quantity: int = Field(
        1,
        ge=1,
        description="车辆数量"
    )
    
    # 载重参数
    max_payload: float = Field(
        ...,
        gt=0,
        description="最大载重（kg）"
    )
    
    cargo_volume: float = Field(
        ...,
        gt=0,
        description="货厢容积（m³）"
    )
    
    loading_constraints: Dict[str, Any] = Field(
        default_factory=dict,
        description="装载限制条件"
    )
    
    # 性能参数
    max_speed: float = Field(
        ...,
        gt=0,
        description="最大行驶速度（km/h）"
    )
    
    fuel_consumption: float = Field(
        ...,
        gt=0,
        description="燃油消耗（L/100km）"
    )
    
    maneuverability: float = Field(
        1.0,
        ge=0,
        le=1,
        description="机动性能系数（0-1）"
    )
    
    # 作业参数
    loading_time: float = Field(
        ...,
        gt=0,
        description="装载时间（分钟）"
    )
    
    unloading_time: float = Field(
        ...,
        gt=0,
        description="卸载时间（分钟）"
    )
    
    transfer_efficiency: float = Field(
        1.0,
        ge=0,
        le=1,
        description="转运效率系数（0-1）"
    )
    
    # 维护信息
    maintenance_cycle: int = Field(
        ...,
        gt=0,
        description="维护周期（小时）"
    )
    
    availability_rate: float = Field(
        0.95,
        ge=0,
        le=1,
        description="可用率（0-1）"
    )
    
    maintenance_info: Dict[str, Any] = Field(
        default_factory=dict,
        description="维护信息详情"
    )
    
    # 运行状态
    operational_status: VehicleStatusEnum = Field(
        VehicleStatusEnum.AVAILABLE,
        description="运行状态"
    )
    
    # 故障统计
    fault_statistics: Dict[str, Any] = Field(
        default_factory=dict,
        description="故障统计信息"
    )
    
    # 备注信息
    remarks: Optional[str] = Field(
        None,
        max_length=500,
        description="备注信息"
    )


class VehicleUpdateSchema(BaseModel):
    """
    运输车辆配置更新Schema
    
    用于更新运输车辆配置时的数据验证和传输
    """
    
    model_config = ConfigDict(
        str_strip_whitespace=True,
        validate_assignment=True,
        use_enum_values=True
    )
    
    # 车辆基本信息（可选更新）
    vehicle_model: Optional[str] = Field(
        None,
        min_length=1,
        max_length=100,
        description="车辆型号"
    )
    
    quantity: Optional[int] = Field(
        None,
        ge=1,
        description="车辆数量"
    )
    
    # 载重参数（可选更新）
    max_payload: Optional[float] = Field(
        None,
        gt=0,
        description="最大载重（kg）"
    )
    
    cargo_volume: Optional[float] = Field(
        None,
        gt=0,
        description="货厢容积（m³）"
    )
    
    loading_constraints: Optional[Dict[str, Any]] = Field(
        None,
        description="装载限制条件"
    )
    
    # 性能参数（可选更新）
    max_speed: Optional[float] = Field(
        None,
        gt=0,
        description="最大行驶速度（km/h）"
    )
    
    fuel_consumption: Optional[float] = Field(
        None,
        gt=0,
        description="燃油消耗（L/100km）"
    )
    
    maneuverability: Optional[float] = Field(
        None,
        ge=0,
        le=1,
        description="机动性能系数（0-1）"
    )
    
    # 作业参数（可选更新）
    loading_time: Optional[float] = Field(
        None,
        gt=0,
        description="装载时间（分钟）"
    )
    
    unloading_time: Optional[float] = Field(
        None,
        gt=0,
        description="卸载时间（分钟）"
    )
    
    transfer_efficiency: Optional[float] = Field(
        None,
        ge=0,
        le=1,
        description="转运效率系数（0-1）"
    )
    
    # 维护信息（可选更新）
    maintenance_cycle: Optional[int] = Field(
        None,
        gt=0,
        description="维护周期（小时）"
    )
    
    availability_rate: Optional[float] = Field(
        None,
        ge=0,
        le=1,
        description="可用率（0-1）"
    )
    
    maintenance_info: Optional[Dict[str, Any]] = Field(
        None,
        description="维护信息详情"
    )
    
    # 运行状态（可选更新）
    operational_status: Optional[VehicleStatusEnum] = Field(
        None,
        description="运行状态"
    )
    
    # 故障统计（可选更新）
    fault_statistics: Optional[Dict[str, Any]] = Field(
        None,
        description="故障统计信息"
    )
    
    # 备注信息（可选更新）
    remarks: Optional[str] = Field(
        None,
        max_length=500,
        description="备注信息"
    )


class VehicleResponseSchema(BaseModel):
    """
    运输车辆配置响应Schema
    
    用于返回运输车辆配置信息时的数据格式定义
    """
    
    model_config = ConfigDict(
        from_attributes=True,
        use_enum_values=True
    )
    
    # 基础字段
    id: str = Field(..., description="运输车辆配置唯一标识")
    vehicle_type: VehicleTypeEnum = Field(..., description="车辆类型")
    vehicle_model: str = Field(..., description="车辆型号")
    quantity: int = Field(..., description="车辆数量")
    
    # 载重参数
    max_payload: float = Field(..., description="最大载重（kg）")
    cargo_volume: float = Field(..., description="货厢容积（m³）")
    loading_constraints: Dict[str, Any] = Field(..., description="装载限制条件")
    
    # 性能参数
    max_speed: float = Field(..., description="最大行驶速度（km/h）")
    fuel_consumption: float = Field(..., description="燃油消耗（L/100km）")
    maneuverability: float = Field(..., description="机动性能系数（0-1）")
    
    # 作业参数
    loading_time: float = Field(..., description="装载时间（分钟）")
    unloading_time: float = Field(..., description="卸载时间（分钟）")
    transfer_efficiency: float = Field(..., description="转运效率系数（0-1）")
    
    # 维护信息
    maintenance_cycle: int = Field(..., description="维护周期（小时）")
    availability_rate: float = Field(..., description="可用率（0-1）")
    maintenance_info: Dict[str, Any] = Field(..., description="维护信息详情")
    
    # 运行状态
    operational_status: VehicleStatusEnum = Field(..., description="运行状态")
    fault_statistics: Dict[str, Any] = Field(..., description="故障统计信息")
    
    # 备注信息
    remarks: Optional[str] = Field(None, description="备注信息")
    
    # 时间戳
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class VehicleListResponseSchema(BaseModel):
    """
    运输车辆配置列表响应Schema
    
    用于返回运输车辆配置列表时的数据格式定义
    """
    
    model_config = ConfigDict(from_attributes=True)
    
    vehicle_configs: List[VehicleResponseSchema] = Field(
        ...,
        description="运输车辆配置列表"
    )
    
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页数量")
    total_pages: int = Field(..., description="总页数")
