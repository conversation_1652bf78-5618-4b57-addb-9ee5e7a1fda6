"""
飞机配置数据传输对象

遵循base-rules.md规范：
- 使用描述性的类和属性命名
- 类文档字符串说明类的用途和主要功能
- 模块职责单一性：只包含飞机配置相关的DTO
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from src.database.models.aircraft import AircraftModelEnum, AircraftStatusEnum


class CrewRequirementsSchema(BaseModel):
    """机组人员需求模式"""
    
    pilots: int = Field(
        ge=1,
        description="飞行员数量"
    )
    
    flight_engineers: int = Field(
        ge=0,
        description="飞行工程师数量"
    )
    
    loadmasters: int = Field(
        ge=0,
        description="装载员数量"
    )


class MaintenanceScheduleSchema(BaseModel):
    """维护计划模式"""
    
    routine_maintenance_hours: int = Field(
        gt=0,
        description="例行维护间隔（小时）"
    )
    
    major_maintenance_hours: int = Field(
        gt=0,
        description="大修间隔（小时）"
    )
    
    last_maintenance_date: Optional[str] = Field(
        default=None,
        description="上次维护日期"
    )


class AircraftCreateSchema(BaseModel):
    """创建飞机配置请求模式"""
    
    aircraft_model: AircraftModelEnum = Field(
        description="飞机型号"
    )
    
    quantity: int = Field(
        ge=1,
        description="飞机数量"
    )
    
    payload_capacity: float = Field(
        gt=0,
        description="载重能力（kg）"
    )
    
    operational_range: float = Field(
        gt=0,
        description="作战半径（km）"
    )
    
    cruise_speed: float = Field(
        gt=0,
        description="巡航速度（km/h）"
    )
    
    fuel_consumption: float = Field(
        gt=0,
        description="燃油消耗（L/h）"
    )
    
    loading_time: float = Field(
        gt=0,
        description="装载时间（分钟）"
    )
    
    unloading_time: float = Field(
        gt=0,
        description="卸载时间（分钟）"
    )
    
    ground_taxi_time: float = Field(
        gt=0,
        description="地面滑行时间（分钟）"
    )
    
    crew_requirements: CrewRequirementsSchema = Field(
        description="机组人员需求"
    )
    
    compatible_equipment: List[str] = Field(
        default_factory=list,
        description="兼容的装卸设备列表"
    )
    
    maintenance_schedule: MaintenanceScheduleSchema = Field(
        description="维护计划"
    )
    
    status: AircraftStatusEnum = Field(
        default=AircraftStatusEnum.AVAILABLE,
        description="飞机状态"
    )
    
    remarks: Optional[str] = Field(
        default=None,
        max_length=500,
        description="备注信息"
    )


class AircraftUpdateSchema(BaseModel):
    """更新飞机配置请求模式"""
    
    aircraft_model: Optional[AircraftModelEnum] = Field(
        default=None,
        description="飞机型号"
    )
    
    quantity: Optional[int] = Field(
        default=None,
        ge=1,
        description="飞机数量"
    )
    
    payload_capacity: Optional[float] = Field(
        default=None,
        gt=0,
        description="载重能力（kg）"
    )
    
    operational_range: Optional[float] = Field(
        default=None,
        gt=0,
        description="作战半径（km）"
    )
    
    cruise_speed: Optional[float] = Field(
        default=None,
        gt=0,
        description="巡航速度（km/h）"
    )
    
    fuel_consumption: Optional[float] = Field(
        default=None,
        gt=0,
        description="燃油消耗（L/h）"
    )
    
    loading_time: Optional[float] = Field(
        default=None,
        gt=0,
        description="装载时间（分钟）"
    )
    
    unloading_time: Optional[float] = Field(
        default=None,
        gt=0,
        description="卸载时间（分钟）"
    )
    
    ground_taxi_time: Optional[float] = Field(
        default=None,
        gt=0,
        description="地面滑行时间（分钟）"
    )
    
    crew_requirements: Optional[CrewRequirementsSchema] = Field(
        default=None,
        description="机组人员需求"
    )
    
    compatible_equipment: Optional[List[str]] = Field(
        default=None,
        description="兼容的装卸设备列表"
    )
    
    maintenance_schedule: Optional[MaintenanceScheduleSchema] = Field(
        default=None,
        description="维护计划"
    )
    
    status: Optional[AircraftStatusEnum] = Field(
        default=None,
        description="飞机状态"
    )
    
    remarks: Optional[str] = Field(
        default=None,
        max_length=500,
        description="备注信息"
    )


class AircraftResponseSchema(BaseModel):
    """飞机配置响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(description="飞机配置ID")
    aircraft_model: AircraftModelEnum = Field(description="飞机型号")
    quantity: int = Field(description="飞机数量")
    payload_capacity: float = Field(description="载重能力（kg）")
    operational_range: float = Field(description="作战半径（km）")
    cruise_speed: float = Field(description="巡航速度（km/h）")
    fuel_consumption: float = Field(description="燃油消耗（L/h）")
    loading_time: float = Field(description="装载时间（分钟）")
    unloading_time: float = Field(description="卸载时间（分钟）")
    ground_taxi_time: float = Field(description="地面滑行时间（分钟）")
    crew_requirements: Dict[str, Any] = Field(description="机组人员需求")
    compatible_equipment: List[str] = Field(description="兼容的装卸设备列表")
    maintenance_schedule: Dict[str, Any] = Field(description="维护计划")
    status: AircraftStatusEnum = Field(description="飞机状态")
    remarks: Optional[str] = Field(description="备注信息")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")


class AircraftListResponseSchema(BaseModel):
    """飞机配置列表响应模式"""
    
    aircraft_configs: List[AircraftResponseSchema] = Field(
        description="飞机配置列表"
    )
    total: int = Field(
        description="总数量"
    )
    page: int = Field(
        description="当前页码"
    )
    page_size: int = Field(
        description="每页数量"
    )
    total_pages: int = Field(
        description="总页数"
    )
