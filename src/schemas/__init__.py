"""
数据传输对象模块

遵循base-rules.md规范：
- 导入语句组织规则：标准库、第三方库、本地应用导入分组
- 模块职责单一性：只包含数据传输对象的导入
"""

# 本地应用导入
from src.schemas.efficiency import (
    EfficiencyIndicatorCreateSchema,
    EfficiencyIndicatorUpdateSchema,
    EfficiencyIndicatorResponseSchema,
    IndicatorCalculationRequestSchema,
    IndicatorCalculationResultResponseSchema,
    IndicatorWeightConfigCreateSchema,
    IndicatorWeightConfigResponseSchema
)
from src.schemas.calculation import (
    CalculationModelCreateSchema,
    CalculationModelUpdateSchema,
    CalculationModelResponseSchema,
    CalculationTaskCreateSchema,
    CalculationTaskUpdateSchema,
    CalculationTaskResponseSchema
)

__all__ = [
    # 效能指标相关Schema
    "EfficiencyIndicatorCreateSchema",
    "EfficiencyIndicatorUpdateSchema",
    "EfficiencyIndicatorResponseSchema",
    "IndicatorCalculationRequestSchema",
    "IndicatorCalculationResultResponseSchema",
    "IndicatorWeightConfigCreateSchema",
    "IndicatorWeightConfigResponseSchema",

    # 计算模型相关Schema
    "CalculationModelCreateSchema",
    "CalculationModelUpdateSchema",
    "CalculationModelResponseSchema",
    "CalculationTaskCreateSchema",
    "CalculationTaskUpdateSchema",
    "CalculationTaskResponseSchema",
]
