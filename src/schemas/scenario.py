"""
场景数据传输对象

遵循base-rules.md规范：
- 使用描述性的类和属性命名
- 类文档字符串说明类的用途和主要功能
- 模块职责单一性：只包含场景相关的DTO
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from src.database.models.scenario import TaskTypeEnum, ScenarioStatusEnum, ThreatLevelEnum


class EnvironmentConditionsSchema(BaseModel):
    """环境条件参数模式"""
    
    # 气象条件
    weather: Dict[str, Any] = Field(
        default_factory=dict,
        description="气象条件参数"
    )
    
    # 地形特征
    terrain: str = Field(
        default="plain",
        description="地形类型"
    )
    
    # 空域管制
    airspace_control: Dict[str, Any] = Field(
        default_factory=dict,
        description="空域管制信息"
    )
    
    # 电磁环境
    electromagnetic_environment: Dict[str, Any] = Field(
        default_factory=dict,
        description="电磁环境参数"
    )
    
    # 机场条件
    airport_condition: Dict[str, Any] = Field(
        default_factory=dict,
        description="机场条件参数"
    )


class ResourceConstraintsSchema(BaseModel):
    """资源约束配置模式"""
    
    # 运输力量限制
    transport_capacity: Dict[str, Any] = Field(
        default_factory=dict,
        description="运输力量限制"
    )
    
    # 保障资源限制
    support_resources: Dict[str, Any] = Field(
        default_factory=dict,
        description="保障资源限制"
    )
    
    # 时间窗口限制
    time_window: Dict[str, Any] = Field(
        default_factory=dict,
        description="时间窗口限制"
    )


class MissionRequirementsSchema(BaseModel):
    """任务需求定义模式"""
    
    # 运输量需求
    cargo_weight: float = Field(
        gt=0,
        description="货物重量（kg）"
    )
    
    personnel_count: Optional[int] = Field(
        default=None,
        ge=0,
        description="人员数量"
    )
    
    # 起讫点
    origin: str = Field(
        description="出发地"
    )
    
    destination: str = Field(
        description="目的地"
    )
    
    # 时效性要求
    time_window: Dict[str, Any] = Field(
        default_factory=dict,
        description="时间窗口要求"
    )
    
    # 优先级
    priority: str = Field(
        default="medium",
        description="任务优先级"
    )
    
    # 安全性要求
    safety_requirements: Dict[str, Any] = Field(
        default_factory=dict,
        description="安全性要求"
    )


class ThreatFactorsSchema(BaseModel):
    """威胁因素建模模式"""
    
    # 敌方威胁
    enemy_threats: Dict[str, Any] = Field(
        default_factory=dict,
        description="敌方威胁因素"
    )
    
    # 威胁概率
    threat_probabilities: Dict[str, float] = Field(
        default_factory=dict,
        description="各类威胁发生概率"
    )
    
    # 应对措施
    countermeasures: List[str] = Field(
        default_factory=list,
        description="应对措施列表"
    )


class ScenarioCreateSchema(BaseModel):
    """创建场景请求模式"""
    
    scenario_name: str = Field(
        min_length=1,
        max_length=200,
        description="场景名称"
    )
    
    scenario_type: str = Field(
        min_length=1,
        max_length=100,
        description="场景类型"
    )
    
    description: Optional[str] = Field(
        default=None,
        description="场景详细描述"
    )
    
    task_type: TaskTypeEnum = Field(
        description="任务类型"
    )
    
    environment_conditions: EnvironmentConditionsSchema = Field(
        default_factory=EnvironmentConditionsSchema,
        description="环境条件参数"
    )
    
    resource_constraints: ResourceConstraintsSchema = Field(
        default_factory=ResourceConstraintsSchema,
        description="资源约束配置"
    )
    
    mission_requirements: MissionRequirementsSchema = Field(
        description="任务需求定义"
    )
    
    threat_factors: ThreatFactorsSchema = Field(
        default_factory=ThreatFactorsSchema,
        description="威胁因素建模"
    )
    
    threat_level: ThreatLevelEnum = Field(
        default=ThreatLevelEnum.LOW,
        description="威胁等级"
    )
    
    created_by: Optional[str] = Field(
        default=None,
        max_length=100,
        description="创建者"
    )


class ScenarioUpdateSchema(BaseModel):
    """更新场景请求模式"""
    
    scenario_name: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=200,
        description="场景名称"
    )
    
    scenario_type: Optional[str] = Field(
        default=None,
        min_length=1,
        max_length=100,
        description="场景类型"
    )
    
    description: Optional[str] = Field(
        default=None,
        description="场景详细描述"
    )
    
    task_type: Optional[TaskTypeEnum] = Field(
        default=None,
        description="任务类型"
    )
    
    environment_conditions: Optional[EnvironmentConditionsSchema] = Field(
        default=None,
        description="环境条件参数"
    )
    
    resource_constraints: Optional[ResourceConstraintsSchema] = Field(
        default=None,
        description="资源约束配置"
    )
    
    mission_requirements: Optional[MissionRequirementsSchema] = Field(
        default=None,
        description="任务需求定义"
    )
    
    threat_factors: Optional[ThreatFactorsSchema] = Field(
        default=None,
        description="威胁因素建模"
    )
    
    threat_level: Optional[ThreatLevelEnum] = Field(
        default=None,
        description="威胁等级"
    )
    
    status: Optional[ScenarioStatusEnum] = Field(
        default=None,
        description="场景状态"
    )


class ScenarioResponseSchema(BaseModel):
    """场景响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(description="场景ID")
    scenario_name: str = Field(description="场景名称")
    scenario_type: str = Field(description="场景类型")
    description: Optional[str] = Field(description="场景描述")
    task_type: TaskTypeEnum = Field(description="任务类型")
    threat_level: ThreatLevelEnum = Field(description="威胁等级")
    status: ScenarioStatusEnum = Field(description="场景状态")
    created_by: Optional[str] = Field(description="创建者")
    created_at: datetime = Field(description="创建时间")
    updated_at: datetime = Field(description="更新时间")
    
    # 复杂字段在需要时单独获取
    environment_conditions: Optional[Dict[str, Any]] = Field(
        default=None,
        description="环境条件参数"
    )
    resource_constraints: Optional[Dict[str, Any]] = Field(
        default=None,
        description="资源约束配置"
    )
    mission_requirements: Optional[Dict[str, Any]] = Field(
        default=None,
        description="任务需求定义"
    )
    threat_factors: Optional[Dict[str, Any]] = Field(
        default=None,
        description="威胁因素建模"
    )


class ScenarioListResponseSchema(BaseModel):
    """场景列表响应模式"""
    
    scenarios: List[ScenarioResponseSchema] = Field(
        description="场景列表"
    )
    total: int = Field(
        description="总数量"
    )
    page: int = Field(
        description="当前页码"
    )
    page_size: int = Field(
        description="每页数量"
    )
    total_pages: int = Field(
        description="总页数"
    )
