"""
装卸载作业效能数据传输对象

遵循base-rules.md规范：
- 使用描述性的类和属性命名
- 类文档字符串说明类的用途和主要功能
- 模块职责单一性：只包含装卸载作业效能相关的DTO
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
from pydantic import BaseModel, Field, ConfigDict
from src.database.models.loading_efficiency import (
    LoadingPhaseEnum,
    TaskStatusEnum,
    TaskPriorityEnum
)


class LoadingEfficiencyTaskCreateSchema(BaseModel):
    """创建装卸载作业效能计算任务的请求模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    task_name: str = Field(
        ...,
        min_length=1,
        max_length=200,
        description="任务名称"
    )
    
    task_description: Optional[str] = Field(
        None,
        description="任务描述"
    )
    
    scenario_id: str = Field(
        ...,
        description="关联场景ID"
    )
    
    loading_phases: List[Dict[str, Any]] = Field(
        ...,
        description="装卸载作业阶段列表"
    )
    
    input_data: Dict[str, Any] = Field(
        ...,
        description="输入数据"
    )
    
    calculation_parameters: Dict[str, Any] = Field(
        default_factory=dict,
        description="计算参数配置"
    )
    
    priority: TaskPriorityEnum = Field(
        default=TaskPriorityEnum.NORMAL,
        description="任务优先级"
    )
    
    created_by: Optional[str] = Field(
        None,
        max_length=100,
        description="创建者"
    )


class LoadingEfficiencyTaskUpdateSchema(BaseModel):
    """更新装卸载作业效能计算任务的请求模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    task_name: Optional[str] = Field(
        None,
        min_length=1,
        max_length=200,
        description="任务名称"
    )
    
    task_description: Optional[str] = Field(
        None,
        description="任务描述"
    )
    
    loading_phases: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="装卸载作业阶段列表"
    )
    
    input_data: Optional[Dict[str, Any]] = Field(
        None,
        description="输入数据"
    )
    
    calculation_parameters: Optional[Dict[str, Any]] = Field(
        None,
        description="计算参数配置"
    )
    
    status: Optional[TaskStatusEnum] = Field(
        None,
        description="任务状态"
    )
    
    priority: Optional[TaskPriorityEnum] = Field(
        None,
        description="任务优先级"
    )
    
    progress_percentage: Optional[int] = Field(
        None,
        ge=0,
        le=100,
        description="执行进度（百分比）"
    )


class LoadingEfficiencyTaskResponseSchema(BaseModel):
    """装卸载作业效能计算任务响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    id: str = Field(..., description="任务ID")
    task_name: str = Field(..., description="任务名称")
    task_description: Optional[str] = Field(None, description="任务描述")
    scenario_id: str = Field(..., description="关联场景ID")
    loading_phases: List[Dict[str, Any]] = Field(..., description="装卸载作业阶段列表")
    input_data: Dict[str, Any] = Field(..., description="输入数据")
    calculation_parameters: Dict[str, Any] = Field(..., description="计算参数配置")
    status: TaskStatusEnum = Field(..., description="任务状态")
    priority: TaskPriorityEnum = Field(..., description="任务优先级")
    progress_percentage: int = Field(..., description="执行进度（百分比）")
    started_at: Optional[str] = Field(None, description="开始执行时间")
    completed_at: Optional[str] = Field(None, description="完成时间")
    execution_duration: Optional[int] = Field(None, description="执行时长（秒）")
    efficiency_results: Dict[str, Any] = Field(..., description="效能计算结果")
    contribution_values: Dict[str, Any] = Field(..., description="贡献值计算结果")
    execution_report: Dict[str, Any] = Field(..., description="作业执行报告")
    error_message: Optional[str] = Field(None, description="错误信息")
    error_details: Dict[str, Any] = Field(..., description="错误详情")
    created_by: Optional[str] = Field(None, description="创建者")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")


class LoadingEfficiencyCalculationRequestSchema(BaseModel):
    """装卸载作业效能计算请求模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    scenario_id: str = Field(
        ...,
        description="场景ID"
    )
    
    task_name: str = Field(
        ...,
        min_length=1,
        max_length=200,
        description="计算任务名称"
    )
    
    loading_phases: List[Dict[str, Any]] = Field(
        ...,
        description="装卸载作业阶段配置"
    )
    
    warehouse_loading_data: Dict[str, Any] = Field(
        ...,
        description="仓库装载阶段数据"
    )
    
    ground_transport_data: Dict[str, Any] = Field(
        ...,
        description="地面运输阶段数据"
    )
    
    aircraft_loading_data: Dict[str, Any] = Field(
        ...,
        description="飞机装载阶段数据"
    )
    
    equipment_data: Dict[str, Any] = Field(
        default_factory=dict,
        description="设备相关数据"
    )
    
    personnel_data: Dict[str, Any] = Field(
        default_factory=dict,
        description="人员相关数据"
    )
    
    calculation_options: Dict[str, Any] = Field(
        default_factory=dict,
        description="计算选项配置"
    )


class LoadingEfficiencyResultResponseSchema(BaseModel):
    """装卸载作业效能计算结果响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    task_id: str = Field(..., description="任务ID")
    
    timeliness_indicators: Dict[str, float] = Field(
        ...,
        description="时效性指标结果"
    )
    
    efficiency_indicators: Dict[str, float] = Field(
        ...,
        description="效率指标结果"
    )
    
    quality_indicators: Dict[str, float] = Field(
        ...,
        description="质量指标结果"
    )
    
    resource_config_indicators: Dict[str, float] = Field(
        ...,
        description="资源配置指标结果"
    )
    
    coordination_indicators: Dict[str, float] = Field(
        ...,
        description="协调性指标结果"
    )
    
    overall_score: float = Field(
        ...,
        description="综合效能评分"
    )
    
    phase_results: Dict[str, Dict[str, float]] = Field(
        ...,
        description="各阶段计算结果"
    )


class ContributionValueResponseSchema(BaseModel):
    """贡献值计算结果响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    task_id: str = Field(..., description="任务ID")
    
    equipment_contributions: Dict[str, Dict[str, float]] = Field(
        ...,
        description="设备贡献值"
    )
    
    personnel_contributions: Dict[str, Dict[str, float]] = Field(
        ...,
        description="人员贡献值"
    )
    
    top_equipment_contributors: List[Dict[str, Any]] = Field(
        ...,
        description="设备贡献值排名前列"
    )
    
    top_personnel_contributors: List[Dict[str, Any]] = Field(
        ...,
        description="人员贡献值排名前列"
    )


class ExecutionReportResponseSchema(BaseModel):
    """作业执行报告响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    task_id: str = Field(..., description="任务ID")
    task_name: str = Field(..., description="任务名称")
    scenario_id: str = Field(..., description="场景ID")
    
    execution_summary: Dict[str, Any] = Field(
        ...,
        description="执行摘要"
    )
    
    phase_analysis: Dict[str, Dict[str, Any]] = Field(
        ...,
        description="阶段分析"
    )
    
    efficiency_analysis: Dict[str, Dict[str, Any]] = Field(
        ...,
        description="效能分析"
    )
    
    contribution_analysis: Dict[str, Dict[str, Any]] = Field(
        ...,
        description="贡献值分析"
    )
    
    improvement_recommendations: List[str] = Field(
        ...,
        description="改进建议"
    )
    
    generated_at: str = Field(..., description="报告生成时间")


class TaskProgressResponseSchema(BaseModel):
    """任务进度响应模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    task_id: str = Field(..., description="任务ID")
    status: TaskStatusEnum = Field(..., description="任务状态")
    progress_percentage: int = Field(..., description="执行进度（百分比）")
    started_at: Optional[str] = Field(None, description="开始执行时间")
    estimated_completion_time: Optional[str] = Field(None, description="预计完成时间")
    current_phase: Optional[str] = Field(None, description="当前执行阶段")
    error_message: Optional[str] = Field(None, description="错误信息")


class LoadingPhaseConfigSchema(BaseModel):
    """装卸载阶段配置模式"""
    
    model_config = ConfigDict(from_attributes=True)
    
    phase_type: LoadingPhaseEnum = Field(
        ...,
        description="阶段类型"
    )
    
    phase_name: str = Field(
        ...,
        min_length=1,
        max_length=100,
        description="阶段名称"
    )
    
    sequence_order: int = Field(
        ...,
        ge=1,
        description="执行顺序"
    )
    
    input_parameters: Dict[str, Any] = Field(
        default_factory=dict,
        description="输入参数"
    )
    
    expected_duration: Optional[float] = Field(
        None,
        ge=0,
        description="预期持续时间（小时）"
    )
    
    resource_requirements: Dict[str, Any] = Field(
        default_factory=dict,
        description="资源需求"
    )
