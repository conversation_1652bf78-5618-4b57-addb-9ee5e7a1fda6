"""
数据库连接管理模块

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数和变量命名
"""

from typing import AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy.orm import DeclarativeBase
from src.config.settings import settings


class DatabaseBase(DeclarativeBase):
    """数据库模型基类"""
    pass


# 创建异步数据库引擎
async_engine = create_async_engine(
    settings.database.url.replace("postgresql://", "postgresql+asyncpg://"),
    echo=settings.api.debug,
    pool_size=20,
    max_overflow=30,
    pool_pre_ping=True,
    pool_recycle=3600,
)

# 创建异步会话工厂
AsyncSessionLocal = async_sessionmaker(
    bind=async_engine,
    class_=AsyncSession,
    expire_on_commit=False,
    autoflush=True,
    autocommit=False,
)


async def get_database_session() -> AsyncGenerator[AsyncSession, None]:
    """
    获取数据库会话
    
    Yields:
        AsyncSession: 异步数据库会话
        
    Raises:
        Exception: 数据库连接异常
    """
    async with AsyncSessionLocal() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()


async def create_database_tables() -> None:
    """
    创建数据库表
    
    Raises:
        Exception: 表创建异常
    """
    async with async_engine.begin() as connection:
        await connection.run_sync(DatabaseBase.metadata.create_all)


async def drop_database_tables() -> None:
    """
    删除数据库表
    
    Raises:
        Exception: 表删除异常
    """
    async with async_engine.begin() as connection:
        await connection.run_sync(DatabaseBase.metadata.drop_all)
