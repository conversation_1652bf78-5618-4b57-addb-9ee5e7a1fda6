"""
飞机配置数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含飞机配置相关的数据模型
"""

from typing import Optional
from sqlalchemy import String, Integer, Float, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column
from enum import Enum
from src.database.models.base import BaseModel


class AircraftModelEnum(str, Enum):
    """飞机型号枚举"""
    Y_20 = "Y-20"  # 运-20
    Y_31 = "Y-31"  # 运-31
    Y_8 = "Y-8"    # 运-8
    Y_7 = "Y-7"    # 运-7


class AircraftStatusEnum(str, Enum):
    """飞机状态枚举"""
    AVAILABLE = "available"  # 可用
    MAINTENANCE = "maintenance"  # 维修中
    MISSION = "mission"  # 执行任务中
    GROUNDED = "grounded"  # 停飞


class AircraftConfig(BaseModel):
    """
    飞机配置数据模型
    
    存储飞机的详细配置信息，包括：
    - 飞机基本信息（机型、数量）
    - 性能参数（载重、航程、速度等）
    - 作业参数（装卸时间、机组需求）
    - 维护计划和燃油消耗数据
    """
    
    __tablename__ = "aircraft_configs"
    
    # 飞机基本信息
    aircraft_model: Mapped[AircraftModelEnum] = mapped_column(
        SQLEnum(AircraftModelEnum),
        nullable=False,
        comment="飞机型号"
    )
    
    quantity: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=1,
        comment="飞机数量"
    )
    
    # 性能参数
    payload_capacity: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="载重能力（kg）"
    )
    
    operational_range: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="作战半径（km）"
    )
    
    cruise_speed: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="巡航速度（km/h）"
    )
    
    fuel_consumption: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="燃油消耗（L/h）"
    )
    
    # 作业参数
    loading_time: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="装载时间（分钟）"
    )
    
    unloading_time: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="卸载时间（分钟）"
    )
    
    ground_taxi_time: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="地面滑行时间（分钟）"
    )
    
    # 机组人员需求
    crew_requirements: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="机组人员需求"
    )
    
    # 兼容设备列表
    compatible_equipment: Mapped[list] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        comment="兼容的装卸设备列表"
    )
    
    # 维护计划
    maintenance_schedule: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="维护计划"
    )
    
    # 飞机状态
    status: Mapped[AircraftStatusEnum] = mapped_column(
        SQLEnum(AircraftStatusEnum),
        nullable=False,
        default=AircraftStatusEnum.AVAILABLE,
        comment="飞机状态"
    )
    
    # 备注信息
    remarks: Mapped[Optional[str]] = mapped_column(
        String(500),
        comment="备注信息"
    )
    
    def __repr__(self) -> str:
        """返回飞机配置的字符串表示"""
        return f"<AircraftConfig(model={self.aircraft_model}, quantity={self.quantity})>"
