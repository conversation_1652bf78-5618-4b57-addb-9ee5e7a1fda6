"""
气象条件数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含气象条件相关的数据模型
"""

from typing import Optional
from sqlalchemy import String, Float, Integer, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column
from enum import Enum
from src.database.models.base import BaseModel


class WeatherTypeEnum(str, Enum):
    """天气类型枚举"""
    SUNNY = "sunny"  # 晴天
    RAINY = "rainy"  # 雨天
    SNOWY = "snowy"  # 雪天
    FOGGY = "foggy"  # 雾天
    WINDY = "windy"  # 大风
    CLOUDY = "cloudy"  # 阴天


class SeverityLevelEnum(str, Enum):
    """严重程度枚举"""
    LIGHT = "light"  # 轻微
    MODERATE = "moderate"  # 中等
    SEVERE = "severe"  # 严重
    EXTREME = "extreme"  # 极端


class WeatherCondition(BaseModel):
    """
    气象条件数据模型
    
    存储气象条件的详细信息，包括：
    - 天气类型和基本参数
    - 具体气象参数（能见度、风速、降水量等）
    - 对作业效率的影响系数
    - 持续时间和严重程度等级
    """
    
    __tablename__ = "weather_conditions"
    
    # 天气基本信息
    weather_type: Mapped[WeatherTypeEnum] = mapped_column(
        SQLEnum(WeatherTypeEnum),
        nullable=False,
        comment="天气类型"
    )
    
    # 气象参数
    visibility: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="能见度（米）"
    )
    
    wind_speed: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="风速（米/秒）"
    )
    
    precipitation: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=0.0,
        comment="降水量（毫米/小时）"
    )
    
    temperature: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="温度（摄氏度）"
    )
    
    humidity: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="湿度（百分比）"
    )
    
    # 影响系数
    loading_efficiency_factor: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=1.0,
        comment="装载效率影响系数"
    )
    
    safety_factor: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=1.0,
        comment="安全系数"
    )
    
    equipment_performance_factor: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=1.0,
        comment="设备性能影响系数"
    )
    
    personnel_efficiency_factor: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=1.0,
        comment="人员效率影响系数"
    )
    
    # 持续时间和严重程度
    duration_hours: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="持续时间（小时）"
    )
    
    severity_level: Mapped[SeverityLevelEnum] = mapped_column(
        SQLEnum(SeverityLevelEnum),
        nullable=False,
        default=SeverityLevelEnum.MODERATE,
        comment="严重程度等级"
    )
    
    # 额外影响因子（使用JSONB存储复杂影响因子）
    additional_impact_factors: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="额外影响因子"
    )
    
    # 备注信息
    remarks: Mapped[Optional[str]] = mapped_column(
        String(500),
        comment="备注信息"
    )
    
    def __repr__(self) -> str:
        """返回气象条件的字符串表示"""
        return f"<WeatherCondition(type={self.weather_type}, severity={self.severity_level})>"
    
    def calculate_overall_impact_factor(self) -> float:
        """
        计算综合影响系数
        
        Returns:
            float: 综合影响系数
        """
        factors = [
            self.loading_efficiency_factor,
            self.safety_factor,
            self.equipment_performance_factor,
            self.personnel_efficiency_factor
        ]
        return sum(factors) / len(factors)
