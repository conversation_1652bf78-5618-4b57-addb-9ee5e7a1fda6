"""
运输车辆配置数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含运输车辆配置相关的数据模型
"""

from typing import Optional
from sqlalchemy import String, Integer, Float, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column
from enum import Enum
from src.database.models.base import BaseModel


class VehicleTypeEnum(str, Enum):
    """运输车辆类型枚举"""
    FLATBED_TRAILER = "flatbed_trailer"  # 平板拖车
    CONTAINER_TRAILER = "container_trailer"  # 集装箱拖车
    CARGO_TRUCK = "cargo_truck"  # 货物运输车
    TRACTOR_UNIT = "tractor_unit"  # 牵引车头


class VehicleStatusEnum(str, Enum):
    """运输车辆状态枚举"""
    AVAILABLE = "available"  # 可用
    IN_USE = "in_use"  # 使用中
    MAINTENANCE = "maintenance"  # 维修中
    FAULT = "fault"  # 故障
    RETIRED = "retired"  # 报废


class VehicleConfig(BaseModel):
    """
    运输车辆配置数据模型
    
    存储机场地面运输车辆的详细配置信息，包括：
    - 车辆基本信息（类型、型号、数量）
    - 载重参数和性能指标
    - 作业参数和维护信息
    """
    
    __tablename__ = "vehicle_configs"
    
    # 车辆基本信息
    vehicle_type: Mapped[VehicleTypeEnum] = mapped_column(
        SQLEnum(VehicleTypeEnum),
        nullable=False,
        comment="车辆类型"
    )
    
    vehicle_model: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="车辆型号"
    )
    
    quantity: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=1,
        comment="车辆数量"
    )
    
    # 载重参数
    max_payload: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="最大载重（kg）"
    )
    
    cargo_volume: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="货厢容积（m³）"
    )
    
    loading_constraints: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="装载限制条件"
    )
    
    # 性能参数
    max_speed: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="最大行驶速度（km/h）"
    )
    
    fuel_consumption: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="燃油消耗（L/100km）"
    )
    
    maneuverability: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=1.0,
        comment="机动性能系数（0-1）"
    )
    
    # 作业参数
    loading_time: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="装载时间（分钟）"
    )
    
    unloading_time: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="卸载时间（分钟）"
    )
    
    transfer_efficiency: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=1.0,
        comment="转运效率系数（0-1）"
    )
    
    # 维护信息
    maintenance_cycle: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        comment="维护周期（小时）"
    )
    
    availability_rate: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=0.95,
        comment="可用率（0-1）"
    )
    
    maintenance_info: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="维护信息详情"
    )
    
    # 运行状态
    operational_status: Mapped[VehicleStatusEnum] = mapped_column(
        SQLEnum(VehicleStatusEnum),
        nullable=False,
        default=VehicleStatusEnum.AVAILABLE,
        comment="运行状态"
    )
    
    # 故障统计
    fault_statistics: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="故障统计信息"
    )
    
    # 备注信息
    remarks: Mapped[Optional[str]] = mapped_column(
        String(500),
        comment="备注信息"
    )
    
    def __repr__(self) -> str:
        """返回运输车辆配置的字符串表示"""
        return f"<VehicleConfig(type={self.vehicle_type}, model={self.vehicle_model})>"
