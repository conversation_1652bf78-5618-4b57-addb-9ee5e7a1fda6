"""
场景数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含场景相关的数据模型
"""

from typing import Optional
from sqlalchemy import String, Text, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship
from enum import Enum
from src.database.models.base import BaseModel


class TaskTypeEnum(str, Enum):
    """任务类型枚举"""
    PERSONNEL_TRANSPORT = "personnel_transport"  # 人员投送
    EQUIPMENT_TRANSPORT = "equipment_transport"  # 装备运输
    MATERIAL_SUPPLY = "material_supply"  # 物资补给
    MEDICAL_EVACUATION = "medical_evacuation"  # 医疗后送
    SPECIAL_TRANSPORT = "special_transport"  # 特种运输


class ScenarioStatusEnum(str, Enum):
    """场景状态枚举"""
    DRAFT = "draft"  # 草稿
    ACTIVE = "active"  # 激活
    ARCHIVED = "archived"  # 归档
    DELETED = "deleted"  # 已删除


class ThreatLevelEnum(str, Enum):
    """威胁等级枚举"""
    LOW = "low"  # 低威胁
    MEDIUM = "medium"  # 中等威胁
    HIGH = "high"  # 高威胁


class Scenario(BaseModel):
    """
    场景数据模型
    
    存储运输保障场景的完整信息，包括：
    - 场景基本信息（名称、类型、描述）
    - 任务类型和环境条件参数
    - 资源约束和任务需求配置
    - 威胁因素和创建者信息
    """
    
    __tablename__ = "scenarios"
    
    # 场景基本信息
    scenario_name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        comment="场景名称"
    )
    
    scenario_type: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="场景类型"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="场景详细描述"
    )
    
    # 任务类型
    task_type: Mapped[TaskTypeEnum] = mapped_column(
        SQLEnum(TaskTypeEnum),
        nullable=False,
        comment="任务类型"
    )
    
    # 环境条件参数（使用JSONB存储复杂结构）
    environment_conditions: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="环境条件参数"
    )
    
    # 资源约束配置
    resource_constraints: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="资源约束配置"
    )
    
    # 任务需求定义
    mission_requirements: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="任务需求定义"
    )
    
    # 威胁因素建模
    threat_factors: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="威胁因素建模"
    )
    
    # 威胁等级
    threat_level: Mapped[ThreatLevelEnum] = mapped_column(
        SQLEnum(ThreatLevelEnum),
        nullable=False,
        default=ThreatLevelEnum.LOW,
        comment="威胁等级"
    )
    
    # 创建者信息
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )
    
    # 场景状态
    status: Mapped[ScenarioStatusEnum] = mapped_column(
        SQLEnum(ScenarioStatusEnum),
        nullable=False,
        default=ScenarioStatusEnum.DRAFT,
        comment="场景状态"
    )

    # 关联关系
    calculation_results = relationship("IndicatorCalculationResult", back_populates="scenario")
    calculation_tasks = relationship("CalculationTask", back_populates="scenario")

    def __repr__(self) -> str:
        """返回场景的字符串表示"""
        return f"<Scenario(name={self.scenario_name}, type={self.scenario_type})>"
