"""
设备配置数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含设备配置相关的数据模型
"""

from typing import Optional
from sqlalchemy import String, Integer, Float, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column
from enum import Enum
from src.database.models.base import BaseModel


class EquipmentTypeEnum(str, Enum):
    """设备类型枚举 - 专注机场地面装卸载设备"""
    # 仓库内作业设备
    WAREHOUSE_FORKLIFT = "warehouse_forklift"  # 叉车
    WAREHOUSE_CONVEYOR = "warehouse_conveyor"  # 传送带
    WAREHOUSE_PLATFORM = "warehouse_platform"  # 装卸平台
    WAREHOUSE_STACKER = "warehouse_stacker"  # 堆垛机
    WAREHOUSE_SORTER = "warehouse_sorter"  # 分拣设备

    # 运输车辆设备
    TRANSPORT_FLATBED = "transport_flatbed"  # 平板拖车
    TRANSPORT_CONTAINER = "transport_container"  # 集装箱拖车
    TRANSPORT_CARGO = "transport_cargo"  # 货物运输车
    TRANSPORT_TRACTOR = "transport_tractor"  # 牵引车

    # 停机坪作业设备
    APRON_LIFT_PLATFORM = "apron_lift_platform"  # 升降平台
    APRON_HYDRAULIC_LIFT = "apron_hydraulic_lift"  # 液压升降台
    APRON_BOARDING_BRIDGE = "apron_boarding_bridge"  # 登机桥
    APRON_CARGO_LOADER = "apron_cargo_loader"  # 货舱装卸设备

    # 辅助设备
    AUXILIARY_PUSHER = "auxiliary_pusher"  # 推拉设备
    AUXILIARY_FIXTURE = "auxiliary_fixture"  # 固定装置
    AUXILIARY_SAFETY = "auxiliary_safety"  # 安全设备
    AUXILIARY_COMMUNICATION = "auxiliary_communication"  # 通信设备

    # 保留原有类型以兼容现有数据
    TOWING_VEHICLE = "towing_vehicle"  # 拖车设备
    LIFTING_EQUIPMENT = "lifting_equipment"  # 升降设备
    LOADING_EQUIPMENT = "loading_equipment"  # 装卸设备
    AUXILIARY_EQUIPMENT = "auxiliary_equipment"  # 辅助设备
    LOADING_VEHICLE = "loading_vehicle"  # 运输车辆
    HANDLING_EQUIPMENT = "handling_equipment"  # 装卸设备
    SUPPORT_EQUIPMENT = "support_equipment"  # 支援设备


class EquipmentStatusEnum(str, Enum):
    """设备状态枚举"""
    AVAILABLE = "available"  # 可用
    MAINTENANCE = "maintenance"  # 维修中
    FAULT = "fault"  # 故障
    RETIRED = "retired"  # 报废


class OperationStageEnum(str, Enum):
    """作业环节枚举 - 标识设备适用的作业环节"""
    WAREHOUSE = "warehouse"  # 仓库环节：机场仓库内货物装卸载
    TRANSPORT = "transport"  # 运输环节：仓库与停机坪之间的运输
    APRON = "apron"  # 停机坪环节：停机坪上的飞机装卸载
    ALL_STAGES = "all_stages"  # 全环节：适用于所有作业环节


class EquipmentConfig(BaseModel):
    """
    设备配置数据模型
    
    存储装卸载设备的详细配置信息，包括：
    - 设备基本信息（类型、型号、数量）
    - 设备规格参数和性能指标
    - 运行状态和维护信息
    """
    
    __tablename__ = "equipment_configs"
    
    # 设备基本信息
    equipment_type: Mapped[EquipmentTypeEnum] = mapped_column(
        SQLEnum(EquipmentTypeEnum),
        nullable=False,
        comment="设备类型"
    )
    
    equipment_model: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="设备型号"
    )
    
    quantity: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=1,
        comment="设备数量"
    )
    
    # 设备规格参数
    max_load_capacity: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="最大载重能力（kg）"
    )
    
    loading_speed: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="装载速度（kg/h）"
    )
    
    unloading_speed: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="卸载速度（kg/h）"
    )
    
    operation_radius: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="作业半径（m）"
    )
    
    power_consumption: Mapped[Optional[float]] = mapped_column(
        Float,
        comment="功耗（kW）"
    )
    
    # 性能参数
    unit_operation_duration: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        comment="单位作业时长（h）"
    )
    
    efficiency_factor: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=1.0,
        comment="效率系数（0-1）"
    )
    
    maintenance_interval: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        comment="维护间隔（小时）"
    )
    
    # 运行状态
    operational_status: Mapped[EquipmentStatusEnum] = mapped_column(
        SQLEnum(EquipmentStatusEnum),
        nullable=False,
        default=EquipmentStatusEnum.AVAILABLE,
        comment="运行状态"
    )
    
    # 维护信息（使用JSONB存储复杂维护记录）
    maintenance_info: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="维护信息"
    )
    
    # 环节适用性（新增字段）
    applicable_stages: Mapped[list] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        comment="适用的作业环节列表"
    )

    # 备注信息
    remarks: Mapped[Optional[str]] = mapped_column(
        String(500),
        comment="备注信息"
    )
    
    def __repr__(self) -> str:
        """返回设备配置的字符串表示"""
        return f"<EquipmentConfig(type={self.equipment_type}, model={self.equipment_model})>"
