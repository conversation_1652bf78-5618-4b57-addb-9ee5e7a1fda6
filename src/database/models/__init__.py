"""
数据模型模块

统一导入所有数据模型，方便其他模块使用
遵循base-rules.md规范：导入语句按标准库、第三方库、本地应用导入的顺序组织
"""

# 本地应用导入
from src.database.models.base import BaseModel
from src.database.models.scenario import Scenario, TaskTypeEnum, ScenarioStatusEnum, ThreatLevelEnum
from src.database.models.equipment import EquipmentConfig, EquipmentTypeEnum, EquipmentStatusEnum
from src.database.models.aircraft import AircraftConfig, AircraftModelEnum, AircraftStatusEnum
from src.database.models.personnel import PersonnelConfig, PersonnelTypeEnum, SkillLevelEnum
from src.database.models.weather import WeatherCondition, WeatherTypeEnum, SeverityLevelEnum
from src.database.models.configuration import ConfigurationScheme, SchemeVersion, SchemeTypeEnum, SchemeStatusEnum

# 导出所有模型和枚举
__all__ = [
    # 基础模型
    "BaseModel",

    # 场景模型
    "Scenario",
    "TaskTypeEnum",
    "ScenarioStatusEnum",
    "ThreatLevelEnum",

    # 设备模型
    "EquipmentConfig",
    "EquipmentTypeEnum",
    "EquipmentStatusEnum",

    # 飞机模型
    "AircraftConfig",
    "AircraftModelEnum",
    "AircraftStatusEnum",

    # 人员模型
    "PersonnelConfig",
    "PersonnelTypeEnum",
    "SkillLevelEnum",

    # 气象模型
    "WeatherCondition",
    "WeatherTypeEnum",
    "SeverityLevelEnum",

    # 配置方案模型
    "ConfigurationScheme",
    "SchemeVersion",
    "SchemeTypeEnum",
    "SchemeStatusEnum",
]
