"""
效能指标数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含效能指标相关的数据模型
"""

from decimal import Decimal
from typing import Optional
from enum import Enum
from sqlalchemy import String, Text, Numeric, Boolean, ForeignKey, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship
from src.database.models.base import BaseModel


class IndicatorCategoryEnum(str, Enum):
    """效能指标分类枚举"""
    TIMELINESS = "timeliness"  # 时效性指标
    CAPABILITY = "capability"  # 能力指标
    ECONOMY = "economy"  # 经济性指标
    ROBUSTNESS = "robustness"  # 鲁棒性指标
    SAFETY = "safety"  # 安全性指标


class IndicatorStatusEnum(str, Enum):
    """指标状态枚举"""
    ACTIVE = "active"  # 激活
    INACTIVE = "inactive"  # 未激活
    DEPRECATED = "deprecated"  # 已弃用


class CalculationStatusEnum(str, Enum):
    """计算状态枚举"""
    PENDING = "pending"  # 待处理
    RUNNING = "running"  # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败


class EfficiencyIndicator(BaseModel):
    """
    效能指标基础信息模型
    
    存储效能指标的基础定义信息，包括：
    - 指标基本信息（名称、分类、描述）
    - 计算公式和计量单位
    - 阈值设定和权重配置
    - 激活状态和创建信息
    """
    
    __tablename__ = "efficiency_indicators"
    
    # 指标基本信息
    indicator_name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        unique=True,
        comment="指标名称"
    )
    
    indicator_code: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        unique=True,
        comment="指标编码"
    )
    
    category: Mapped[IndicatorCategoryEnum] = mapped_column(
        SQLEnum(IndicatorCategoryEnum),
        nullable=False,
        comment="指标分类"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="指标详细描述"
    )
    
    # 计算相关信息
    calculation_formula: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="计算公式说明"
    )
    
    unit: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        comment="计量单位"
    )
    
    # 阈值设定
    min_threshold: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=15, scale=6),
        comment="最小阈值"
    )
    
    max_threshold: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=15, scale=6),
        comment="最大阈值"
    )
    
    target_value: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=15, scale=6),
        comment="目标值"
    )
    
    # 权重和优先级
    default_weight: Mapped[Decimal] = mapped_column(
        Numeric(precision=5, scale=4),
        nullable=False,
        default=Decimal('0.1000'),
        comment="默认权重"
    )
    
    priority_level: Mapped[int] = mapped_column(
        nullable=False,
        default=1,
        comment="优先级等级"
    )
    
    # 状态信息
    status: Mapped[IndicatorStatusEnum] = mapped_column(
        SQLEnum(IndicatorStatusEnum),
        nullable=False,
        default=IndicatorStatusEnum.ACTIVE,
        comment="指标状态"
    )
    
    # 创建者信息
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )
    
    # 元数据信息
    metadata_info: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="元数据信息"
    )

    # 关联关系
    calculation_results = relationship("IndicatorCalculationResult", back_populates="indicator")

    def __repr__(self) -> str:
        """返回效能指标的字符串表示"""
        return f"<EfficiencyIndicator(name={self.indicator_name}, category={self.category})>"


class IndicatorCalculationResult(BaseModel):
    """
    指标计算结果模型
    
    存储效能指标的计算结果，包括：
    - 计算结果基本信息
    - 关联的场景和指标信息
    - 计算数值和置信度
    - 计算方法和输入参数
    """
    
    __tablename__ = "indicator_calculation_results"
    
    # 关联信息
    scenario_id: Mapped[str] = mapped_column(
        String(36),
        ForeignKey("scenarios.id"),
        nullable=False,
        comment="关联场景ID"
    )
    
    indicator_id: Mapped[str] = mapped_column(
        String(36),
        ForeignKey("efficiency_indicators.id"),
        nullable=False,
        comment="关联指标ID"
    )
    
    # 计算结果
    calculated_value: Mapped[Decimal] = mapped_column(
        Numeric(precision=15, scale=6),
        nullable=False,
        comment="计算得出的数值"
    )
    
    confidence_level: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=5, scale=4),
        comment="置信度"
    )
    
    # 计算信息
    calculation_method: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="使用的计算方法"
    )
    
    calculation_status: Mapped[CalculationStatusEnum] = mapped_column(
        SQLEnum(CalculationStatusEnum),
        nullable=False,
        default=CalculationStatusEnum.PENDING,
        comment="计算状态"
    )
    
    # 输入参数和元数据
    input_parameters: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="输入参数"
    )
    
    calculation_metadata: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="计算元数据"
    )
    
    # 错误信息
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="错误信息"
    )
    
    # 关联关系
    scenario = relationship("Scenario", back_populates="calculation_results")
    indicator = relationship("EfficiencyIndicator", back_populates="calculation_results")
    
    def __repr__(self) -> str:
        """返回计算结果的字符串表示"""
        return f"<IndicatorCalculationResult(scenario_id={self.scenario_id}, value={self.calculated_value})>"


class IndicatorWeightConfig(BaseModel):
    """
    指标权重配置模型
    
    存储不同场景类型下的指标权重配置，包括：
    - 权重配置基本信息
    - 适用的场景类型
    - 各指标的权重分配
    - 配置说明和状态
    """
    
    __tablename__ = "indicator_weight_configs"
    
    # 配置基本信息
    config_name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        comment="配置名称"
    )
    
    scenario_type: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="适用的场景类型"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="配置说明"
    )
    
    # 权重配置
    weight_settings: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="各指标的权重分配"
    )
    
    # 状态信息
    is_default: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否为默认配置"
    )
    
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        comment="是否激活"
    )
    
    # 创建者信息
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )
    
    def __repr__(self) -> str:
        """返回权重配置的字符串表示"""
        return f"<IndicatorWeightConfig(name={self.config_name}, scenario_type={self.scenario_type})>"
