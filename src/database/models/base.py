"""
数据模型基类

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
"""

from datetime import datetime
from typing import Any
from uuid import uuid4
from sqlalchemy import DateTime, String, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column
from src.database.connection import DatabaseBase


class BaseModel(DatabaseBase):
    """
    数据模型基类
    
    提供所有数据模型的通用字段和方法：
    - 主键ID（UUID类型）
    - 创建时间和更新时间
    - 通用的字符串表示方法
    """
    
    __abstract__ = True
    
    # 主键ID，使用UUID确保全局唯一性
    id: Mapped[str] = mapped_column(
        UUID(as_uuid=False),
        primary_key=True,
        default=lambda: str(uuid4()),
        comment="主键ID"
    )
    
    # 创建时间，自动设置为当前时间
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        comment="创建时间"
    )
    
    # 更新时间，每次更新时自动更新
    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        comment="更新时间"
    )
    
    def __repr__(self) -> str:
        """
        返回对象的字符串表示
        
        Returns:
            str: 对象的字符串表示
        """
        return f"<{self.__class__.__name__}(id={self.id})>"
    
    def to_dict(self) -> dict[str, Any]:
        """
        将模型转换为字典
        
        Returns:
            dict: 模型的字典表示
        """
        return {
            column.name: getattr(self, column.name)
            for column in self.__table__.columns
        }
