"""
计算模型数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含计算模型相关的数据模型
"""

from typing import Optional
from enum import Enum
from sqlalchemy import String, Text, Boolean, ForeignKey, Enum as SQLEnum, Integer
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship
from src.database.models.base import BaseModel


class ModelTypeEnum(str, Enum):
    """计算模型类型枚举"""
    ANALYTICAL = "analytical"  # 解析模型
    SIMULATION = "simulation"  # 仿真模型
    OPTIMIZATION = "optimization"  # 优化模型
    DATA_DRIVEN = "data_driven"  # 数据驱动模型


class ModelStatusEnum(str, Enum):
    """模型状态枚举"""
    ACTIVE = "active"  # 激活
    INACTIVE = "inactive"  # 未激活
    DEPRECATED = "deprecated"  # 已弃用
    UNDER_DEVELOPMENT = "under_development"  # 开发中


class TaskStatusEnum(str, Enum):
    """任务状态枚举"""
    PENDING = "pending"  # 待处理
    RUNNING = "running"  # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    CANCELLED = "cancelled"  # 已取消


class TaskPriorityEnum(str, Enum):
    """任务优先级枚举"""
    LOW = "low"  # 低优先级
    NORMAL = "normal"  # 普通优先级
    HIGH = "high"  # 高优先级
    URGENT = "urgent"  # 紧急优先级


class CalculationModel(BaseModel):
    """
    计算模型信息模型
    
    存储计算模型的基础定义信息，包括：
    - 模型基本信息（名称、类型、描述）
    - 算法类别和实现信息
    - 输入输出参数定义
    - 配置信息和状态
    """
    
    __tablename__ = "calculation_models"
    
    # 模型基本信息
    model_name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        unique=True,
        comment="模型名称"
    )
    
    model_code: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        unique=True,
        comment="模型编码"
    )
    
    model_type: Mapped[ModelTypeEnum] = mapped_column(
        SQLEnum(ModelTypeEnum),
        nullable=False,
        comment="模型类型"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="模型详细描述"
    )
    
    # 算法信息
    algorithm_category: Mapped[str] = mapped_column(
        String(100),
        nullable=False,
        comment="算法类别"
    )
    
    implementation_class: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        comment="实现类路径"
    )
    
    # 参数定义
    input_parameters_schema: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="输入参数定义"
    )
    
    output_parameters_schema: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="输出参数定义"
    )
    
    # 配置信息
    default_config: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="默认配置信息"
    )
    
    # 性能信息
    estimated_execution_time: Mapped[Optional[int]] = mapped_column(
        Integer,
        comment="预估执行时间（秒）"
    )
    
    memory_requirement: Mapped[Optional[int]] = mapped_column(
        Integer,
        comment="内存需求（MB）"
    )
    
    # 状态信息
    status: Mapped[ModelStatusEnum] = mapped_column(
        SQLEnum(ModelStatusEnum),
        nullable=False,
        default=ModelStatusEnum.ACTIVE,
        comment="模型状态"
    )
    
    is_parallel_supported: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=False,
        comment="是否支持并行计算"
    )
    
    # 创建者信息
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )
    
    # 版本信息
    version: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        default="1.0.0",
        comment="模型版本"
    )
    
    # 元数据信息
    metadata_info: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="元数据信息"
    )
    
    # 关联关系
    calculation_tasks = relationship("CalculationTask", back_populates="model")
    
    def __repr__(self) -> str:
        """返回计算模型的字符串表示"""
        return f"<CalculationModel(name={self.model_name}, type={self.model_type})>"


class CalculationTask(BaseModel):
    """
    计算任务管理模型
    
    存储计算任务的管理信息，包括：
    - 任务基本信息和关联关系
    - 输入数据和参数配置
    - 任务状态和执行信息
    - 错误信息和结果数据
    """
    
    __tablename__ = "calculation_tasks"
    
    # 任务基本信息
    task_name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        comment="任务名称"
    )
    
    task_description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="任务描述"
    )
    
    # 关联信息
    scenario_id: Mapped[str] = mapped_column(
        String(36),
        ForeignKey("scenarios.id"),
        nullable=False,
        comment="关联场景ID"
    )
    
    model_id: Mapped[str] = mapped_column(
        String(36),
        ForeignKey("calculation_models.id"),
        nullable=False,
        comment="关联模型ID"
    )
    
    # 输入数据和参数
    input_data: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="输入数据"
    )
    
    parameters_config: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="参数配置"
    )
    
    # 任务状态和优先级
    status: Mapped[TaskStatusEnum] = mapped_column(
        SQLEnum(TaskStatusEnum),
        nullable=False,
        default=TaskStatusEnum.PENDING,
        comment="任务状态"
    )
    
    priority: Mapped[TaskPriorityEnum] = mapped_column(
        SQLEnum(TaskPriorityEnum),
        nullable=False,
        default=TaskPriorityEnum.NORMAL,
        comment="任务优先级"
    )
    
    # 执行信息
    execution_progress: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="执行进度（百分比）"
    )
    
    started_at: Mapped[Optional[str]] = mapped_column(
        String(50),
        comment="开始执行时间"
    )
    
    completed_at: Mapped[Optional[str]] = mapped_column(
        String(50),
        comment="完成时间"
    )
    
    execution_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        comment="执行时长（秒）"
    )
    
    # 错误信息
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="错误信息"
    )
    
    error_details: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="错误详情"
    )
    
    # 结果数据
    result_data: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="结果数据"
    )
    
    # 创建者信息
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )
    
    # 关联关系
    scenario = relationship("Scenario", back_populates="calculation_tasks")
    model = relationship("CalculationModel", back_populates="calculation_tasks")
    
    def __repr__(self) -> str:
        """返回计算任务的字符串表示"""
        return f"<CalculationTask(name={self.task_name}, status={self.status})>"
