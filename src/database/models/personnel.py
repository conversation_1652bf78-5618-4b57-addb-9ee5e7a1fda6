"""
人员配置数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含人员配置相关的数据模型
"""

from typing import Optional
from sqlalchemy import String, Integer, Float, Enum as SQLEnum
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column
from enum import Enum
from src.database.models.base import BaseModel


class PersonnelTypeEnum(str, Enum):
    """人员类型枚举"""
    EQUIPMENT_OPERATOR = "equipment_operator"  # 设备操作员
    GROUND_CREW = "ground_crew"  # 地勤人员
    COMMAND_COORDINATOR = "command_coordinator"  # 指挥协调员
    SAFETY_SUPERVISOR = "safety_supervisor"  # 安全监督员


class SkillLevelEnum(str, Enum):
    """技能等级枚举"""
    JUNIOR = "junior"  # 初级
    INTERMEDIATE = "intermediate"  # 中级
    SENIOR = "senior"  # 高级
    EXPERT = "expert"  # 专家级


class PersonnelConfig(BaseModel):
    """
    人员配置数据模型
    
    存储作业人员的配置信息，包括：
    - 人员基本信息（类型、数量）
    - 技能等级和效率参数
    - 班次安排和设备分配关系
    - 认证要求和培训记录
    """
    
    __tablename__ = "personnel_configs"
    
    # 人员基本信息
    personnel_type: Mapped[PersonnelTypeEnum] = mapped_column(
        SQLEnum(PersonnelTypeEnum),
        nullable=False,
        comment="人员类型"
    )
    
    quantity: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=1,
        comment="人员数量"
    )
    
    # 技能等级
    skill_level: Mapped[SkillLevelEnum] = mapped_column(
        SQLEnum(SkillLevelEnum),
        nullable=False,
        default=SkillLevelEnum.INTERMEDIATE,
        comment="技能等级"
    )
    
    # 效率参数
    base_efficiency: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=1.0,
        comment="基础效率系数"
    )
    
    skill_bonus: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=0.0,
        comment="技能加成系数"
    )
    
    experience_bonus: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=0.0,
        comment="经验加成系数"
    )
    
    # 班次安排
    work_hours_per_shift: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=8.0,
        comment="每班工作时长（小时）"
    )
    
    rest_hours_between_shifts: Mapped[float] = mapped_column(
        Float,
        nullable=False,
        default=8.0,
        comment="班次间休息时间（小时）"
    )
    
    shift_rotation_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        default="continuous",
        comment="轮班制度类型"
    )
    
    # 设备分配关系
    equipment_assignments: Mapped[list] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        comment="设备分配关系"
    )
    
    # 认证要求
    certification_requirements: Mapped[list] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        comment="认证要求"
    )
    
    # 培训记录
    training_records: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="培训记录"
    )
    
    # 备注信息
    remarks: Mapped[Optional[str]] = mapped_column(
        String(500),
        comment="备注信息"
    )
    
    def __repr__(self) -> str:
        """返回人员配置的字符串表示"""
        return f"<PersonnelConfig(type={self.personnel_type}, quantity={self.quantity})>"
    
    def calculate_total_efficiency(self) -> float:
        """
        计算总效率系数
        
        Returns:
            float: 总效率系数
        """
        return self.base_efficiency + self.skill_bonus + self.experience_bonus
