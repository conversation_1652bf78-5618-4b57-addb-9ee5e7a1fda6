"""
配置方案数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含配置方案相关的数据模型
"""

from typing import Optional
from sqlalchemy import String, Text, Integer, Float, Enum as SQLEnum, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB, ARRAY
from sqlalchemy.orm import Mapped, mapped_column, relationship
from enum import Enum
from src.database.models.base import BaseModel


class SchemeTypeEnum(str, Enum):
    """方案类型枚举"""
    HEAVY_EQUIPMENT_TRANSPORT = "heavy_equipment_transport"  # 重型装备运输
    PERSONNEL_TRANSPORT = "personnel_transport"  # 人员运输
    MATERIAL_SUPPLY = "material_supply"  # 物资补给
    EMERGENCY_EVACUATION = "emergency_evacuation"  # 应急撤离
    MIXED_TRANSPORT = "mixed_transport"  # 混合运输


class SchemeStatusEnum(str, Enum):
    """方案状态枚举"""
    DRAFT = "draft"  # 草稿
    VALIDATED = "validated"  # 已验证
    APPROVED = "approved"  # 已批准
    ACTIVE = "active"  # 激活
    ARCHIVED = "archived"  # 归档


class ConfigurationScheme(BaseModel):
    """
    配置方案数据模型
    
    存储完整的配置方案信息，包括：
    - 方案基本信息（名称、类型、描述）
    - 设备、飞机、人员配置关联
    - 场景参数和作业参数
    - 标签分类和状态管理
    """
    
    __tablename__ = "configuration_schemes"
    
    # 方案基本信息
    scheme_name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        comment="方案名称"
    )
    
    scheme_type: Mapped[SchemeTypeEnum] = mapped_column(
        SQLEnum(SchemeTypeEnum),
        nullable=False,
        comment="方案类型"
    )
    
    description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="方案详细描述"
    )
    
    # 版本信息
    version: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        default="1.0.0",
        comment="版本号"
    )
    
    # 配置关联（存储ID列表）
    equipment_config_ids: Mapped[list] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        comment="设备配置ID列表"
    )
    
    aircraft_config_ids: Mapped[list] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        comment="飞机配置ID列表"
    )
    
    personnel_config_ids: Mapped[list] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        comment="人员配置ID列表"
    )
    
    weather_condition_ids: Mapped[list] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        comment="气象条件ID列表"
    )
    
    # 场景参数
    scenario_parameters: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="场景参数"
    )
    
    # 作业参数
    operational_parameters: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="作业参数"
    )
    
    # 标签和分类
    tags: Mapped[list] = mapped_column(
        ARRAY(String),
        nullable=False,
        default=list,
        comment="标签列表"
    )
    
    category: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="分类"
    )
    
    # 创建者信息
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )
    
    # 方案状态
    status: Mapped[SchemeStatusEnum] = mapped_column(
        SQLEnum(SchemeStatusEnum),
        nullable=False,
        default=SchemeStatusEnum.DRAFT,
        comment="方案状态"
    )
    
    # 使用统计
    usage_count: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="使用次数"
    )
    
    # 评分信息
    rating: Mapped[Optional[float]] = mapped_column(
        Float,
        comment="评分（1-5）"
    )
    
    def __repr__(self) -> str:
        """返回配置方案的字符串表示"""
        return f"<ConfigurationScheme(name={self.scheme_name}, type={self.scheme_type})>"


class SchemeVersion(BaseModel):
    """
    方案版本数据模型
    
    存储配置方案的版本控制信息，包括：
    - 版本基本信息
    - 完整的配置数据快照
    - 变更描述和创建信息
    """
    
    __tablename__ = "scheme_versions"
    
    # 关联的方案ID
    scheme_id: Mapped[str] = mapped_column(
        String,
        ForeignKey("configuration_schemes.id"),
        nullable=False,
        comment="关联的方案ID"
    )
    
    # 版本信息
    version_number: Mapped[str] = mapped_column(
        String(20),
        nullable=False,
        comment="版本号"
    )
    
    change_description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="变更描述"
    )
    
    # 完整配置数据快照
    configuration_snapshot: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        comment="配置数据快照"
    )
    
    # 是否为当前版本
    is_current: Mapped[bool] = mapped_column(
        nullable=False,
        default=False,
        comment="是否为当前版本"
    )
    
    # 创建者信息
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )
    
    def __repr__(self) -> str:
        """返回方案版本的字符串表示"""
        return f"<SchemeVersion(scheme_id={self.scheme_id}, version={self.version_number})>"
