"""
装卸载作业效能数据模型

遵循base-rules.md规范：
- 类文档字符串规则：说明类的用途、主要功能
- 使用描述性的类和属性命名
- 模块职责单一性：只包含装卸载作业效能相关的数据模型
"""

from decimal import Decimal
from typing import Optional
from enum import Enum
from sqlalchemy import String, Text, Numeric, Boolean, ForeignKey, Enum as SQLEnum, Integer
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship
from src.database.models.base import BaseModel


class LoadingPhaseEnum(str, Enum):
    """装卸载作业阶段枚举"""
    WAREHOUSE_LOADING = "warehouse_loading"  # 仓库装载
    GROUND_TRANSPORT = "ground_transport"  # 地面运输
    AIRCRAFT_LOADING = "aircraft_loading"  # 飞机装载


class TaskStatusEnum(str, Enum):
    """装卸载作业任务状态枚举"""
    PENDING = "pending"  # 待处理
    RUNNING = "running"  # 运行中
    COMPLETED = "completed"  # 已完成
    FAILED = "failed"  # 失败
    CANCELLED = "cancelled"  # 已取消


class TaskPriorityEnum(str, Enum):
    """任务优先级枚举"""
    LOW = "low"  # 低优先级
    NORMAL = "normal"  # 普通优先级
    HIGH = "high"  # 高优先级
    URGENT = "urgent"  # 紧急优先级


class LoadingEfficiencyTask(BaseModel):
    """
    装卸载作业效能计算任务模型
    
    存储装卸载作业效能计算任务的管理信息，包括：
    - 任务基本信息和关联关系
    - 作业阶段和输入数据
    - 任务状态和执行信息
    - 计算结果和报告数据
    """
    
    __tablename__ = "loading_efficiency_tasks"
    
    # 任务基本信息
    task_name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        comment="任务名称"
    )
    
    task_description: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="任务描述"
    )
    
    # 关联信息
    scenario_id: Mapped[str] = mapped_column(
        String(36),
        ForeignKey("scenarios.id"),
        nullable=False,
        comment="关联场景ID"
    )
    
    # 作业阶段信息
    loading_phases: Mapped[list] = mapped_column(
        JSONB,
        nullable=False,
        default=list,
        comment="装卸载作业阶段列表"
    )
    
    # 输入数据和参数
    input_data: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="输入数据"
    )
    
    calculation_parameters: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="计算参数配置"
    )
    
    # 任务状态和优先级
    status: Mapped[TaskStatusEnum] = mapped_column(
        SQLEnum(TaskStatusEnum),
        nullable=False,
        default=TaskStatusEnum.PENDING,
        comment="任务状态"
    )
    
    priority: Mapped[TaskPriorityEnum] = mapped_column(
        SQLEnum(TaskPriorityEnum),
        nullable=False,
        default=TaskPriorityEnum.NORMAL,
        comment="任务优先级"
    )
    
    # 执行信息
    progress_percentage: Mapped[int] = mapped_column(
        Integer,
        nullable=False,
        default=0,
        comment="执行进度（百分比）"
    )
    
    started_at: Mapped[Optional[str]] = mapped_column(
        String(50),
        comment="开始执行时间"
    )
    
    completed_at: Mapped[Optional[str]] = mapped_column(
        String(50),
        comment="完成时间"
    )
    
    execution_duration: Mapped[Optional[int]] = mapped_column(
        Integer,
        comment="执行时长（秒）"
    )
    
    # 计算结果数据
    efficiency_results: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="效能计算结果"
    )
    
    contribution_values: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="贡献值计算结果"
    )
    
    execution_report: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="作业执行报告"
    )
    
    # 错误信息
    error_message: Mapped[Optional[str]] = mapped_column(
        Text,
        comment="错误信息"
    )
    
    error_details: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="错误详情"
    )
    
    # 创建者信息
    created_by: Mapped[Optional[str]] = mapped_column(
        String(100),
        comment="创建者"
    )
    
    # 关联关系
    scenario = relationship("Scenario", back_populates="loading_efficiency_tasks")
    
    def __repr__(self) -> str:
        """返回装卸载作业任务的字符串表示"""
        return f"<LoadingEfficiencyTask(name={self.task_name}, status={self.status})>"


class LoadingEfficiencyResult(BaseModel):
    """
    装卸载作业效能计算结果模型
    
    存储具体的效能指标计算结果，包括：
    - 指标计算结果基本信息
    - 关联的任务和指标信息
    - 计算数值和置信度
    - 阶段性结果和贡献值
    """
    
    __tablename__ = "loading_efficiency_results"
    
    # 关联信息
    task_id: Mapped[str] = mapped_column(
        String(36),
        ForeignKey("loading_efficiency_tasks.id"),
        nullable=False,
        comment="关联任务ID"
    )
    
    indicator_code: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        comment="指标编码"
    )
    
    indicator_name: Mapped[str] = mapped_column(
        String(200),
        nullable=False,
        comment="指标名称"
    )
    
    # 计算结果
    calculated_value: Mapped[Decimal] = mapped_column(
        Numeric(precision=15, scale=6),
        nullable=False,
        comment="计算得出的数值"
    )
    
    confidence_level: Mapped[Optional[Decimal]] = mapped_column(
        Numeric(precision=5, scale=4),
        comment="置信度"
    )
    
    # 阶段性结果
    phase_results: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="各阶段的计算结果"
    )
    
    # 贡献值信息
    equipment_contributions: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="设备贡献值"
    )
    
    personnel_contributions: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="人员贡献值"
    )
    
    # 计算元数据
    calculation_metadata: Mapped[dict] = mapped_column(
        JSONB,
        nullable=False,
        default=dict,
        comment="计算元数据"
    )
    
    # 关联关系
    task = relationship("LoadingEfficiencyTask", back_populates="results")
    
    def __repr__(self) -> str:
        """返回效能计算结果的字符串表示"""
        return f"<LoadingEfficiencyResult(indicator={self.indicator_code}, value={self.calculated_value})>"


# 为LoadingEfficiencyTask添加反向关系
LoadingEfficiencyTask.results = relationship("LoadingEfficiencyResult", back_populates="task")
