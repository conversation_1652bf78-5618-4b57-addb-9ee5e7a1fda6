"""
效能综合评估服务

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含效能综合评估相关的逻辑
"""

from typing import Dict, List, Optional, Tuple
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from src.database.models.efficiency import (
    EfficiencyIndicator,
    IndicatorCalculationResult,
    IndicatorWeightConfig,
    IndicatorCategoryEnum
)
from src.services.efficiency import IndicatorWeightConfigService


class EfficiencyEvaluator:
    """
    效能综合评估器
    
    提供效能指标的综合评估功能：
    - 基于权重的综合评分计算
    - 多指标综合评估
    - 评估结果分析和排名
    """
    
    def __init__(self, db_session: AsyncSession) -> None:
        """
        初始化效能评估器
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
        self.weight_service = IndicatorWeightConfigService(db_session)
    
    async def calculate_comprehensive_score(self, scenario_id: str, scenario_type: str) -> Dict[str, Decimal]:
        """
        计算场景的综合效能评分
        
        Args:
            scenario_id: 场景ID
            scenario_type: 场景类型
            
        Returns:
            Dict[str, Decimal]: 包含各分类评分和总评分的字典
        """
        # 获取权重配置
        weight_config = await self.weight_service.get_default_weight_config(scenario_type)
        if not weight_config:
            raise ValueError(f"未找到场景类型 {scenario_type} 的权重配置")
        
        # 获取计算结果
        calculation_results = await self._get_calculation_results_by_scenario(scenario_id)
        if not calculation_results:
            raise ValueError(f"场景 {scenario_id} 没有计算结果")
        
        # 按分类组织结果
        results_by_category = await self._organize_results_by_category(calculation_results)
        
        # 计算各分类评分
        category_scores = {}
        for category, results in results_by_category.items():
            category_score = self._calculate_category_score(results, weight_config.weight_settings)
            category_scores[category.value] = category_score
        
        # 计算总评分
        total_score = self._calculate_total_score(category_scores, weight_config.weight_settings)
        category_scores["total"] = total_score
        
        return category_scores
    
    async def compare_scenarios(self, scenario_ids: List[str], scenario_type: str) -> List[Dict[str, any]]:
        """
        比较多个场景的效能评分
        
        Args:
            scenario_ids: 场景ID列表
            scenario_type: 场景类型
            
        Returns:
            List[Dict[str, any]]: 按评分排序的场景比较结果
        """
        comparison_results = []
        
        for scenario_id in scenario_ids:
            try:
                scores = await self.calculate_comprehensive_score(scenario_id, scenario_type)
                comparison_results.append({
                    "scenario_id": scenario_id,
                    "scores": scores,
                    "total_score": scores.get("total", Decimal('0'))
                })
            except ValueError as e:
                # 记录错误但继续处理其他场景
                comparison_results.append({
                    "scenario_id": scenario_id,
                    "error": str(e),
                    "total_score": Decimal('0')
                })
        
        # 按总评分降序排序
        comparison_results.sort(key=lambda x: x["total_score"], reverse=True)
        
        return comparison_results
    
    async def analyze_performance_by_category(self, scenario_id: str) -> Dict[str, Dict[str, Decimal]]:
        """
        按分类分析场景的性能表现
        
        Args:
            scenario_id: 场景ID
            
        Returns:
            Dict[str, Dict[str, Decimal]]: 各分类的详细性能分析
        """
        calculation_results = await self._get_calculation_results_by_scenario(scenario_id)
        results_by_category = await self._organize_results_by_category(calculation_results)
        
        analysis = {}
        for category, results in results_by_category.items():
            category_analysis = {
                "indicator_count": Decimal(str(len(results))),
                "average_score": self._calculate_average_score(results),
                "max_score": self._get_max_score(results),
                "min_score": self._get_min_score(results),
                "score_variance": self._calculate_score_variance(results)
            }
            analysis[category.value] = category_analysis
        
        return analysis
    
    def calculate_weighted_score(self, indicator_values: Dict[str, Decimal], 
                                weights: Dict[str, Decimal]) -> Decimal:
        """
        计算加权评分
        
        Args:
            indicator_values: 指标值字典
            weights: 权重字典
            
        Returns:
            Decimal: 加权评分
        """
        weighted_sum = Decimal('0')
        total_weight = Decimal('0')
        
        for indicator_code, value in indicator_values.items():
            weight = weights.get(indicator_code, Decimal('0'))
            if weight > 0:
                # 对指标值进行标准化处理（假设所有指标都是越大越好）
                normalized_value = self._normalize_indicator_value(value, indicator_code)
                weighted_sum += normalized_value * weight
                total_weight += weight
        
        if total_weight == 0:
            return Decimal('0')
        
        return (weighted_sum / total_weight).quantize(Decimal('0.01'))
    
    async def _get_calculation_results_by_scenario(self, scenario_id: str) -> List[IndicatorCalculationResult]:
        """获取场景的所有计算结果"""
        stmt = select(IndicatorCalculationResult).where(
            IndicatorCalculationResult.scenario_id == scenario_id
        )
        result = await self.db_session.execute(stmt)
        return result.scalars().all()
    
    async def _organize_results_by_category(self, results: List[IndicatorCalculationResult]) -> Dict[IndicatorCategoryEnum, List[IndicatorCalculationResult]]:
        """按分类组织计算结果"""
        # 获取所有相关的指标信息
        indicator_ids = [result.indicator_id for result in results]
        stmt = select(EfficiencyIndicator).where(EfficiencyIndicator.id.in_(indicator_ids))
        indicator_result = await self.db_session.execute(stmt)
        indicators = {ind.id: ind for ind in indicator_result.scalars().all()}
        
        # 按分类组织结果
        results_by_category = {}
        for result in results:
            indicator = indicators.get(result.indicator_id)
            if indicator:
                category = indicator.category
                if category not in results_by_category:
                    results_by_category[category] = []
                results_by_category[category].append(result)
        
        return results_by_category
    
    def _calculate_category_score(self, results: List[IndicatorCalculationResult], 
                                 weight_settings: Dict[str, any]) -> Decimal:
        """计算分类评分"""
        if not results:
            return Decimal('0')
        
        total_weighted_score = Decimal('0')
        total_weight = Decimal('0')
        
        for result in results:
            # 从权重设置中获取权重（这里简化处理，实际应该根据指标编码获取）
            weight = Decimal('1.0') / len(results)  # 平均权重
            normalized_value = self._normalize_indicator_value(result.calculated_value, "default")
            
            total_weighted_score += normalized_value * weight
            total_weight += weight
        
        if total_weight == 0:
            return Decimal('0')
        
        return (total_weighted_score / total_weight).quantize(Decimal('0.01'))
    
    def _calculate_total_score(self, category_scores: Dict[str, Decimal], 
                              weight_settings: Dict[str, any]) -> Decimal:
        """计算总评分"""
        # 分类权重（简化处理）
        category_weights = {
            "timeliness": Decimal('0.25'),
            "capability": Decimal('0.25'),
            "economy": Decimal('0.20'),
            "robustness": Decimal('0.15'),
            "safety": Decimal('0.15')
        }
        
        total_weighted_score = Decimal('0')
        total_weight = Decimal('0')
        
        for category, score in category_scores.items():
            if category != "total":
                weight = category_weights.get(category, Decimal('0'))
                total_weighted_score += score * weight
                total_weight += weight
        
        if total_weight == 0:
            return Decimal('0')
        
        return (total_weighted_score / total_weight).quantize(Decimal('0.01'))
    
    def _normalize_indicator_value(self, value: Decimal, indicator_code: str) -> Decimal:
        """
        标准化指标值到0-100范围
        
        Args:
            value: 原始指标值
            indicator_code: 指标编码
            
        Returns:
            Decimal: 标准化后的值
        """
        # 这里简化处理，实际应该根据指标类型和历史数据进行标准化
        if value < 0:
            return Decimal('0')
        elif value > 100:
            return Decimal('100')
        else:
            return value.quantize(Decimal('0.01'))
    
    def _calculate_average_score(self, results: List[IndicatorCalculationResult]) -> Decimal:
        """计算平均分"""
        if not results:
            return Decimal('0')
        
        total = sum(result.calculated_value for result in results)
        return (total / len(results)).quantize(Decimal('0.01'))
    
    def _get_max_score(self, results: List[IndicatorCalculationResult]) -> Decimal:
        """获取最高分"""
        if not results:
            return Decimal('0')
        
        return max(result.calculated_value for result in results)
    
    def _get_min_score(self, results: List[IndicatorCalculationResult]) -> Decimal:
        """获取最低分"""
        if not results:
            return Decimal('0')
        
        return min(result.calculated_value for result in results)
    
    def _calculate_score_variance(self, results: List[IndicatorCalculationResult]) -> Decimal:
        """计算分数方差"""
        if len(results) < 2:
            return Decimal('0')
        
        values = [result.calculated_value for result in results]
        mean = sum(values) / len(values)
        variance = sum((value - mean) ** 2 for value in values) / len(values)
        
        return variance.quantize(Decimal('0.01'))
