"""
效能指标计算器

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含效能指标计算相关的逻辑
"""

from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, timedelta
import math
from src.database.models.efficiency import IndicatorCategoryEnum


class TimelinessCalculator:
    """
    时效性指标计算器
    
    实现时效性相关指标的计算：
    - 任务完成时间
    - 平均响应时间
    - 准时交付率
    - 单位时间运输量
    """
    
    @staticmethod
    def calculate_task_completion_time(start_time: datetime, end_time: datetime) -> Decimal:
        """
        计算任务完成时间
        
        Args:
            start_time: 任务开始时间
            end_time: 任务结束时间
            
        Returns:
            Decimal: 任务完成时间（小时）
        """
        if end_time <= start_time:
            raise ValueError("结束时间必须晚于开始时间")
        
        duration = end_time - start_time
        hours = Decimal(str(duration.total_seconds() / 3600))
        return hours.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_average_response_time(response_times: List[float]) -> Decimal:
        """
        计算平均响应时间
        
        Args:
            response_times: 响应时间列表（小时）
            
        Returns:
            Decimal: 平均响应时间（小时）
        """
        if not response_times:
            return Decimal('0')
        
        average = sum(response_times) / len(response_times)
        return Decimal(str(average)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_on_time_delivery_rate(total_tasks: int, on_time_tasks: int) -> Decimal:
        """
        计算准时交付率
        
        Args:
            total_tasks: 总任务数
            on_time_tasks: 准时完成的任务数
            
        Returns:
            Decimal: 准时交付率（百分比）
        """
        if total_tasks <= 0:
            return Decimal('0')
        
        rate = (on_time_tasks / total_tasks) * 100
        return Decimal(str(rate)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_transport_volume_per_hour(total_volume: Decimal, duration_hours: Decimal) -> Decimal:
        """
        计算单位时间运输量
        
        Args:
            total_volume: 总运输量（吨或人）
            duration_hours: 持续时间（小时）
            
        Returns:
            Decimal: 单位时间运输量（吨/小时或人/小时）
        """
        if duration_hours <= 0:
            return Decimal('0')
        
        volume_per_hour = total_volume / duration_hours
        return volume_per_hour.quantize(Decimal('0.01'))


class CapabilityCalculator:
    """
    能力指标计算器
    
    实现能力相关指标的计算：
    - 最大持续运输能力
    - 任务达成率
    - 可用架次率
    - 装备利用率
    """
    
    @staticmethod
    def calculate_max_sustained_capacity(peak_capacity: Decimal, sustainability_factor: Decimal) -> Decimal:
        """
        计算最大持续运输能力
        
        Args:
            peak_capacity: 峰值运输能力
            sustainability_factor: 可持续性系数（0-1）
            
        Returns:
            Decimal: 最大持续运输能力
        """
        if not (0 <= sustainability_factor <= 1):
            raise ValueError("可持续性系数必须在0-1之间")
        
        sustained_capacity = peak_capacity * sustainability_factor
        return sustained_capacity.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_mission_success_rate(total_missions: int, successful_missions: int) -> Decimal:
        """
        计算任务达成率
        
        Args:
            total_missions: 总任务数
            successful_missions: 成功完成的任务数
            
        Returns:
            Decimal: 任务达成率（百分比）
        """
        if total_missions <= 0:
            return Decimal('0')
        
        success_rate = (successful_missions / total_missions) * 100
        return Decimal(str(success_rate)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_available_sortie_rate(total_sorties: int, available_sorties: int) -> Decimal:
        """
        计算可用架次率
        
        Args:
            total_sorties: 总架次数
            available_sorties: 可用架次数
            
        Returns:
            Decimal: 可用架次率（百分比）
        """
        if total_sorties <= 0:
            return Decimal('0')
        
        availability_rate = (available_sorties / total_sorties) * 100
        return Decimal(str(availability_rate)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_equipment_utilization_rate(actual_usage_hours: Decimal, available_hours: Decimal) -> Decimal:
        """
        计算装备利用率
        
        Args:
            actual_usage_hours: 实际使用时间（小时）
            available_hours: 可用时间（小时）
            
        Returns:
            Decimal: 装备利用率（百分比）
        """
        if available_hours <= 0:
            return Decimal('0')
        
        utilization_rate = (actual_usage_hours / available_hours) * 100
        return utilization_rate.quantize(Decimal('0.01'))


class EconomyCalculator:
    """
    经济性指标计算器
    
    实现经济性相关指标的计算：
    - 吨公里成本
    - 资源利用率
    - 冗余度
    - 燃油效率
    """
    
    @staticmethod
    def calculate_ton_km_cost(total_cost: Decimal, transport_volume: Decimal, distance: Decimal) -> Decimal:
        """
        计算吨公里成本
        
        Args:
            total_cost: 总成本
            transport_volume: 运输量（吨）
            distance: 运输距离（公里）
            
        Returns:
            Decimal: 吨公里成本
        """
        ton_km = transport_volume * distance
        if ton_km <= 0:
            return Decimal('0')
        
        cost_per_ton_km = total_cost / ton_km
        return cost_per_ton_km.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_resource_utilization_rate(used_resources: Decimal, total_resources: Decimal) -> Decimal:
        """
        计算资源利用率
        
        Args:
            used_resources: 已使用资源
            total_resources: 总资源
            
        Returns:
            Decimal: 资源利用率（百分比）
        """
        if total_resources <= 0:
            return Decimal('0')
        
        utilization_rate = (used_resources / total_resources) * 100
        return utilization_rate.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_redundancy_ratio(reserved_resources: Decimal, required_resources: Decimal) -> Decimal:
        """
        计算冗余度
        
        Args:
            reserved_resources: 预留资源
            required_resources: 所需资源
            
        Returns:
            Decimal: 冗余度（百分比）
        """
        if required_resources <= 0:
            return Decimal('0')
        
        redundancy = (reserved_resources / required_resources) * 100
        return redundancy.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_fuel_efficiency(transport_volume: Decimal, fuel_consumption: Decimal) -> Decimal:
        """
        计算燃油效率
        
        Args:
            transport_volume: 运输量（吨）
            fuel_consumption: 燃油消耗（升）
            
        Returns:
            Decimal: 燃油效率（吨/升）
        """
        if fuel_consumption <= 0:
            return Decimal('0')
        
        efficiency = transport_volume / fuel_consumption
        return efficiency.quantize(Decimal('0.01'))


class RobustnessCalculator:
    """
    鲁棒性指标计算器
    
    实现鲁棒性相关指标的计算：
    - 场景适应度
    - 抗毁伤能力
    - 恢复时间
    - 风险抵抗力
    """
    
    @staticmethod
    def calculate_scenario_adaptability(performance_scores: List[Decimal]) -> Decimal:
        """
        计算场景适应度
        
        Args:
            performance_scores: 不同场景下的性能评分列表
            
        Returns:
            Decimal: 场景适应度（标准差的倒数）
        """
        if not performance_scores or len(performance_scores) < 2:
            return Decimal('0')
        
        # 计算平均值
        mean = sum(performance_scores) / len(performance_scores)
        
        # 计算标准差
        variance = sum((score - mean) ** 2 for score in performance_scores) / len(performance_scores)
        std_dev = Decimal(str(math.sqrt(float(variance))))
        
        # 适应度为标准差的倒数（标准差越小，适应度越高）
        if std_dev == 0:
            return Decimal('100')  # 完全一致的性能
        
        adaptability = Decimal('1') / std_dev
        return adaptability.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_damage_resistance(operational_capacity_after_damage: Decimal, 
                                  original_capacity: Decimal) -> Decimal:
        """
        计算抗毁伤能力
        
        Args:
            operational_capacity_after_damage: 受损后的运行能力
            original_capacity: 原始运行能力
            
        Returns:
            Decimal: 抗毁伤能力（百分比）
        """
        if original_capacity <= 0:
            return Decimal('0')
        
        resistance = (operational_capacity_after_damage / original_capacity) * 100
        return resistance.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_recovery_time(failure_time: datetime, recovery_time: datetime) -> Decimal:
        """
        计算恢复时间
        
        Args:
            failure_time: 故障发生时间
            recovery_time: 恢复正常时间
            
        Returns:
            Decimal: 恢复时间（小时）
        """
        if recovery_time <= failure_time:
            raise ValueError("恢复时间必须晚于故障时间")
        
        duration = recovery_time - failure_time
        hours = Decimal(str(duration.total_seconds() / 3600))
        return hours.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_risk_resistance(risk_mitigation_score: Decimal, risk_exposure_score: Decimal) -> Decimal:
        """
        计算风险抵抗力
        
        Args:
            risk_mitigation_score: 风险缓解评分
            risk_exposure_score: 风险暴露评分
            
        Returns:
            Decimal: 风险抵抗力
        """
        if risk_exposure_score <= 0:
            return Decimal('100')  # 无风险暴露
        
        resistance = (risk_mitigation_score / risk_exposure_score) * 100
        return resistance.quantize(Decimal('0.01'))


class SafetyCalculator:
    """
    安全性指标计算器
    
    实现安全性相关指标的计算：
    - 任务风险概率
    - 损失率
    - 安全裕度
    - 应急响应能力
    """
    
    @staticmethod
    def calculate_mission_risk_probability(risk_events: int, total_missions: int) -> Decimal:
        """
        计算任务风险概率
        
        Args:
            risk_events: 风险事件数量
            total_missions: 总任务数
            
        Returns:
            Decimal: 任务风险概率（百分比）
        """
        if total_missions <= 0:
            return Decimal('0')
        
        probability = (risk_events / total_missions) * 100
        return Decimal(str(probability)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_loss_rate(lost_volume: Decimal, total_volume: Decimal) -> Decimal:
        """
        计算损失率
        
        Args:
            lost_volume: 损失量
            total_volume: 总量
            
        Returns:
            Decimal: 损失率（百分比）
        """
        if total_volume <= 0:
            return Decimal('0')
        
        loss_rate = (lost_volume / total_volume) * 100
        return loss_rate.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_safety_margin(safety_measures_score: Decimal, minimum_required_score: Decimal) -> Decimal:
        """
        计算安全裕度
        
        Args:
            safety_measures_score: 安全措施评分
            minimum_required_score: 最低要求评分
            
        Returns:
            Decimal: 安全裕度（百分比）
        """
        if minimum_required_score <= 0:
            return Decimal('100')
        
        margin = ((safety_measures_score - minimum_required_score) / minimum_required_score) * 100
        return margin.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_emergency_response_capability(response_time: Decimal, standard_time: Decimal) -> Decimal:
        """
        计算应急响应能力
        
        Args:
            response_time: 实际响应时间（分钟）
            standard_time: 标准响应时间（分钟）
            
        Returns:
            Decimal: 应急响应能力评分（标准时间/实际时间 * 100）
        """
        if response_time <= 0:
            return Decimal('0')
        
        capability = (standard_time / response_time) * 100
        return capability.quantize(Decimal('0.01'))
