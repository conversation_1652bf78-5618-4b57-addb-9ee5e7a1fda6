"""
装卸载作业效能管理服务

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含装卸载作业效能管理相关的业务逻辑
"""

from typing import Optional, List, Dict, Any
from decimal import Decimal
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
from src.database.models.loading_efficiency import (
    LoadingEfficiencyTask,
    LoadingEfficiencyResult,
    LoadingPhaseEnum,
    TaskStatusEnum,
    TaskPriorityEnum
)
from src.database.models.efficiency import IndicatorCategoryEnum
from src.services.loading_efficiency_calculator import (
    TimelinessCalculator,
    EfficiencyCalculator,
    QualityCalculator,
    ResourceConfigCalculator,
    CoordinationCalculator
)


class LoadingEfficiencyTaskService:
    """
    装卸载作业效能任务管理服务类
    
    提供装卸载作业效能计算任务的管理功能：
    - 创建、查询、更新、删除效能计算任务
    - 任务状态管理和进度跟踪
    - 计算结果存储和查询
    """
    
    def __init__(self, db_session: AsyncSession) -> None:
        """
        初始化装卸载作业效能任务服务
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def create_task(self, task_data: Dict[str, Any]) -> LoadingEfficiencyTask:
        """
        创建装卸载作业效能计算任务
        
        Args:
            task_data: 任务创建数据
            
        Returns:
            LoadingEfficiencyTask: 创建的任务实例
            
        Raises:
            ValueError: 当任务数据无效时
        """
        # 验证必要字段
        required_fields = ['task_name', 'scenario_id', 'loading_phases', 'input_data']
        for field in required_fields:
            if field not in task_data:
                raise ValueError(f"缺少必要字段: {field}")
        
        # 验证装卸载阶段数据
        loading_phases = task_data.get('loading_phases', [])
        if not loading_phases:
            raise ValueError("装卸载阶段数据不能为空")
        
        # 创建新任务
        new_task = LoadingEfficiencyTask(
            task_name=task_data['task_name'],
            task_description=task_data.get('task_description'),
            scenario_id=task_data['scenario_id'],
            loading_phases=loading_phases,
            input_data=task_data['input_data'],
            calculation_parameters=task_data.get('calculation_parameters', {}),
            priority=task_data.get('priority', TaskPriorityEnum.NORMAL),
            created_by=task_data.get('created_by')
        )
        
        self.db_session.add(new_task)
        await self.db_session.commit()
        await self.db_session.refresh(new_task)
        
        return new_task
    
    async def get_task_by_id(self, task_id: str) -> Optional[LoadingEfficiencyTask]:
        """
        根据ID获取装卸载作业效能任务
        
        Args:
            task_id: 任务ID
            
        Returns:
            Optional[LoadingEfficiencyTask]: 任务实例，不存在时返回None
        """
        stmt = select(LoadingEfficiencyTask).where(LoadingEfficiencyTask.id == task_id)
        result = await self.db_session.execute(stmt)
        return result.scalar_one_or_none()
    
    async def get_tasks_by_scenario(self, scenario_id: str) -> List[LoadingEfficiencyTask]:
        """
        根据场景ID获取装卸载作业效能任务列表
        
        Args:
            scenario_id: 场景ID
            
        Returns:
            List[LoadingEfficiencyTask]: 任务列表
        """
        stmt = select(LoadingEfficiencyTask).where(
            LoadingEfficiencyTask.scenario_id == scenario_id
        ).order_by(LoadingEfficiencyTask.created_at.desc())
        
        result = await self.db_session.execute(stmt)
        return result.scalars().all()
    
    async def update_task_status(self, task_id: str, status: TaskStatusEnum, 
                               progress_percentage: Optional[int] = None) -> Optional[LoadingEfficiencyTask]:
        """
        更新任务状态和进度
        
        Args:
            task_id: 任务ID
            status: 新状态
            progress_percentage: 进度百分比
            
        Returns:
            Optional[LoadingEfficiencyTask]: 更新后的任务实例
        """
        stmt = select(LoadingEfficiencyTask).where(LoadingEfficiencyTask.id == task_id)
        result = await self.db_session.execute(stmt)
        task = result.scalar_one_or_none()
        
        if not task:
            return None
        
        task.status = status
        if progress_percentage is not None:
            task.progress_percentage = progress_percentage
        
        # 更新时间戳
        current_time = datetime.now().isoformat()
        if status == TaskStatusEnum.RUNNING and not task.started_at:
            task.started_at = current_time
        elif status == TaskStatusEnum.COMPLETED:
            task.completed_at = current_time
            task.progress_percentage = 100
            
            # 计算执行时长
            if task.started_at:
                start_time = datetime.fromisoformat(task.started_at)
                end_time = datetime.fromisoformat(current_time)
                duration = (end_time - start_time).total_seconds()
                task.execution_duration = int(duration)
        
        await self.db_session.commit()
        await self.db_session.refresh(task)
        
        return task
    
    async def update_task_results(self, task_id: str, efficiency_results: Dict[str, Any],
                                contribution_values: Dict[str, Any], 
                                execution_report: Dict[str, Any]) -> Optional[LoadingEfficiencyTask]:
        """
        更新任务的计算结果
        
        Args:
            task_id: 任务ID
            efficiency_results: 效能计算结果
            contribution_values: 贡献值计算结果
            execution_report: 作业执行报告
            
        Returns:
            Optional[LoadingEfficiencyTask]: 更新后的任务实例
        """
        stmt = select(LoadingEfficiencyTask).where(LoadingEfficiencyTask.id == task_id)
        result = await self.db_session.execute(stmt)
        task = result.scalar_one_or_none()
        
        if not task:
            return None
        
        task.efficiency_results = efficiency_results
        task.contribution_values = contribution_values
        task.execution_report = execution_report
        
        await self.db_session.commit()
        await self.db_session.refresh(task)
        
        return task
    
    async def update_task_error(self, task_id: str, error_message: str, 
                              error_details: Dict[str, Any]) -> Optional[LoadingEfficiencyTask]:
        """
        更新任务错误信息
        
        Args:
            task_id: 任务ID
            error_message: 错误消息
            error_details: 错误详情
            
        Returns:
            Optional[LoadingEfficiencyTask]: 更新后的任务实例
        """
        stmt = select(LoadingEfficiencyTask).where(LoadingEfficiencyTask.id == task_id)
        result = await self.db_session.execute(stmt)
        task = result.scalar_one_or_none()
        
        if not task:
            return None
        
        task.status = TaskStatusEnum.FAILED
        task.error_message = error_message
        task.error_details = error_details
        
        await self.db_session.commit()
        await self.db_session.refresh(task)
        
        return task


class LoadingEfficiencyCalculationService:
    """
    装卸载作业效能计算服务类
    
    提供装卸载作业效能指标的计算功能：
    - 执行各类效能指标计算
    - 生成贡献值分析
    - 创建作业执行报告
    """
    
    def __init__(self, db_session: AsyncSession) -> None:
        """
        初始化装卸载作业效能计算服务
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def calculate_efficiency_indicators(self, task: LoadingEfficiencyTask) -> Dict[str, Any]:
        """
        计算装卸载作业效能指标
        
        Args:
            task: 装卸载作业任务
            
        Returns:
            Dict[str, Any]: 效能指标计算结果
        """
        input_data = task.input_data
        results = {}
        
        # 时效性指标计算
        timeliness_results = await self._calculate_timeliness_indicators(input_data)
        results['timeliness'] = timeliness_results
        
        # 效率指标计算
        efficiency_results = await self._calculate_efficiency_indicators(input_data)
        results['efficiency'] = efficiency_results
        
        # 质量指标计算
        quality_results = await self._calculate_quality_indicators(input_data)
        results['quality'] = quality_results
        
        # 资源配置指标计算
        resource_config_results = await self._calculate_resource_config_indicators(input_data)
        results['resource_config'] = resource_config_results
        
        # 协调性指标计算
        coordination_results = await self._calculate_coordination_indicators(input_data)
        results['coordination'] = coordination_results
        
        return results
    
    async def calculate_contribution_values(self, task: LoadingEfficiencyTask) -> Dict[str, Any]:
        """
        计算贡献值
        
        Args:
            task: 装卸载作业任务
            
        Returns:
            Dict[str, Any]: 贡献值计算结果
        """
        input_data = task.input_data
        contribution_values = {}
        
        # 设备贡献值计算
        equipment_contributions = await self._calculate_equipment_contributions(input_data)
        contribution_values['equipment'] = equipment_contributions
        
        # 人员贡献值计算
        personnel_contributions = await self._calculate_personnel_contributions(input_data)
        contribution_values['personnel'] = personnel_contributions
        
        return contribution_values
    
    async def generate_execution_report(self, task: LoadingEfficiencyTask, 
                                      efficiency_results: Dict[str, Any],
                                      contribution_values: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成作业执行报告
        
        Args:
            task: 装卸载作业任务
            efficiency_results: 效能计算结果
            contribution_values: 贡献值计算结果
            
        Returns:
            Dict[str, Any]: 作业执行报告
        """
        report = {
            'task_info': {
                'task_id': task.id,
                'task_name': task.task_name,
                'scenario_id': task.scenario_id,
                'execution_time': task.execution_duration,
                'completion_time': task.completed_at
            },
            'loading_phases': task.loading_phases,
            'efficiency_summary': self._generate_efficiency_summary(efficiency_results),
            'contribution_analysis': self._generate_contribution_analysis(contribution_values),
            'recommendations': self._generate_recommendations(efficiency_results, contribution_values),
            'generated_at': datetime.now().isoformat()
        }
        
        return report
    
    async def _calculate_timeliness_indicators(self, input_data: Dict[str, Any]) -> Dict[str, Decimal]:
        """计算时效性指标"""
        results = {}
        
        # 作业完成时间
        if 'start_time' in input_data and 'end_time' in input_data:
            start_time = datetime.fromisoformat(input_data['start_time'])
            end_time = datetime.fromisoformat(input_data['end_time'])
            completion_time = TimelinessCalculator.calculate_operation_completion_time(start_time, end_time)
            results['operation_completion_time'] = completion_time
        
        # 平均响应时间
        if 'response_times' in input_data:
            avg_response_time = TimelinessCalculator.calculate_average_response_time(input_data['response_times'])
            results['average_response_time'] = avg_response_time
        
        # 准时完成率
        if 'total_operations' in input_data and 'on_time_operations' in input_data:
            on_time_rate = TimelinessCalculator.calculate_on_time_completion_rate(
                input_data['total_operations'], input_data['on_time_operations']
            )
            results['on_time_completion_rate'] = on_time_rate
        
        # 单位时间处理量
        if 'total_volume' in input_data and 'duration_hours' in input_data:
            processing_volume = TimelinessCalculator.calculate_processing_volume_per_hour(
                Decimal(str(input_data['total_volume'])), Decimal(str(input_data['duration_hours']))
            )
            results['processing_volume_per_hour'] = processing_volume
        
        return results
    
    async def _calculate_efficiency_indicators(self, input_data: Dict[str, Any]) -> Dict[str, Decimal]:
        """计算效率指标"""
        results = {}
        
        # 设备利用率
        if 'equipment_usage_hours' in input_data and 'equipment_available_hours' in input_data:
            equipment_utilization = EfficiencyCalculator.calculate_equipment_utilization_rate(
                Decimal(str(input_data['equipment_usage_hours'])),
                Decimal(str(input_data['equipment_available_hours']))
            )
            results['equipment_utilization_rate'] = equipment_utilization
        
        # 人员利用率
        if 'personnel_work_hours' in input_data and 'personnel_scheduled_hours' in input_data:
            personnel_utilization = EfficiencyCalculator.calculate_personnel_utilization_rate(
                Decimal(str(input_data['personnel_work_hours'])),
                Decimal(str(input_data['personnel_scheduled_hours']))
            )
            results['personnel_utilization_rate'] = personnel_utilization
        
        # 作业成功率
        if 'total_operations' in input_data and 'successful_operations' in input_data:
            success_rate = EfficiencyCalculator.calculate_operation_success_rate(
                input_data['total_operations'], input_data['successful_operations']
            )
            results['operation_success_rate'] = success_rate
        
        # 设备作业效率
        if 'processed_volume' in input_data and 'operation_time' in input_data:
            operation_efficiency = EfficiencyCalculator.calculate_equipment_operation_efficiency(
                Decimal(str(input_data['processed_volume'])),
                Decimal(str(input_data['operation_time']))
            )
            results['equipment_operation_efficiency'] = operation_efficiency
        
        return results
    
    async def _calculate_quality_indicators(self, input_data: Dict[str, Any]) -> Dict[str, Decimal]:
        """计算质量指标"""
        results = {}
        
        # 货物完好率
        if 'total_cargo' in input_data and 'intact_cargo' in input_data:
            integrity_rate = QualityCalculator.calculate_cargo_integrity_rate(
                input_data['total_cargo'], input_data['intact_cargo']
            )
            results['cargo_integrity_rate'] = integrity_rate
        
        # 作业精度
        if 'target_positions' in input_data and 'accurate_positions' in input_data:
            accuracy = QualityCalculator.calculate_operation_accuracy(
                input_data['target_positions'], input_data['accurate_positions']
            )
            results['operation_accuracy'] = accuracy
        
        # 安全事故率
        if 'total_operations' in input_data and 'safety_incidents' in input_data:
            incident_rate = QualityCalculator.calculate_safety_incident_rate(
                input_data['total_operations'], input_data['safety_incidents']
            )
            results['safety_incident_rate'] = incident_rate
        
        # 返工率
        if 'total_operations' in input_data and 'rework_operations' in input_data:
            rework_rate = QualityCalculator.calculate_rework_rate(
                input_data['total_operations'], input_data['rework_operations']
            )
            results['rework_rate'] = rework_rate
        
        return results
    
    async def _calculate_resource_config_indicators(self, input_data: Dict[str, Any]) -> Dict[str, Decimal]:
        """计算资源配置指标"""
        results = {}
        
        # 设备配置合理性
        if 'required_equipment' in input_data and 'actual_equipment' in input_data:
            equipment_rationality = ResourceConfigCalculator.calculate_equipment_config_rationality(
                input_data['required_equipment'], input_data['actual_equipment']
            )
            results['equipment_config_rationality'] = equipment_rationality
        
        # 人员配置合理性
        if 'required_personnel' in input_data and 'actual_personnel' in input_data:
            personnel_rationality = ResourceConfigCalculator.calculate_personnel_config_rationality(
                input_data['required_personnel'], input_data['actual_personnel']
            )
            results['personnel_config_rationality'] = personnel_rationality
        
        # 资源利用率
        if 'used_resources' in input_data and 'total_resources' in input_data:
            resource_utilization = ResourceConfigCalculator.calculate_resource_utilization_rate(
                Decimal(str(input_data['used_resources'])),
                Decimal(str(input_data['total_resources']))
            )
            results['resource_utilization_rate'] = resource_utilization
        
        # 成本效益比
        if 'total_benefit' in input_data and 'total_cost' in input_data:
            cost_benefit_ratio = ResourceConfigCalculator.calculate_cost_benefit_ratio(
                Decimal(str(input_data['total_benefit'])),
                Decimal(str(input_data['total_cost']))
            )
            results['cost_benefit_ratio'] = cost_benefit_ratio
        
        return results
    
    async def _calculate_coordination_indicators(self, input_data: Dict[str, Any]) -> Dict[str, Decimal]:
        """计算协调性指标"""
        results = {}
        
        # 设备协调度
        if 'coordination_events' in input_data and 'total_events' in input_data:
            equipment_coordination = CoordinationCalculator.calculate_equipment_coordination_degree(
                input_data['coordination_events'], input_data['total_events']
            )
            results['equipment_coordination_degree'] = equipment_coordination
        
        # 人机协调度
        if 'smooth_operations' in input_data and 'total_operations' in input_data:
            human_machine_coordination = CoordinationCalculator.calculate_human_machine_coordination_degree(
                input_data['smooth_operations'], input_data['total_operations']
            )
            results['human_machine_coordination_degree'] = human_machine_coordination
        
        # 流程顺畅度
        if 'smooth_processes' in input_data and 'total_processes' in input_data:
            process_smoothness = CoordinationCalculator.calculate_process_smoothness_degree(
                input_data['smooth_processes'], input_data['total_processes']
            )
            results['process_smoothness_degree'] = process_smoothness
        
        # 等待时间比
        if 'waiting_time' in input_data and 'total_time' in input_data:
            waiting_time_ratio = CoordinationCalculator.calculate_waiting_time_ratio(
                Decimal(str(input_data['waiting_time'])),
                Decimal(str(input_data['total_time']))
            )
            results['waiting_time_ratio'] = waiting_time_ratio
        
        return results
    
    async def _calculate_equipment_contributions(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算设备贡献值"""
        equipment_data = input_data.get('equipment_data', {})
        contributions = {}
        
        for equipment_id, data in equipment_data.items():
            # 基于设备使用时间、处理量等计算贡献值
            usage_time = data.get('usage_time', 0)
            processed_volume = data.get('processed_volume', 0)
            efficiency_score = data.get('efficiency_score', 0)
            
            # 简化的贡献值计算公式
            contribution_score = (usage_time * 0.3 + processed_volume * 0.4 + efficiency_score * 0.3)
            contributions[equipment_id] = {
                'contribution_score': round(contribution_score, 2),
                'usage_time': usage_time,
                'processed_volume': processed_volume,
                'efficiency_score': efficiency_score
            }
        
        return contributions
    
    async def _calculate_personnel_contributions(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """计算人员贡献值"""
        personnel_data = input_data.get('personnel_data', {})
        contributions = {}
        
        for personnel_id, data in personnel_data.items():
            # 基于人员工作时间、完成任务数等计算贡献值
            work_time = data.get('work_time', 0)
            completed_tasks = data.get('completed_tasks', 0)
            quality_score = data.get('quality_score', 0)
            
            # 简化的贡献值计算公式
            contribution_score = (work_time * 0.3 + completed_tasks * 0.4 + quality_score * 0.3)
            contributions[personnel_id] = {
                'contribution_score': round(contribution_score, 2),
                'work_time': work_time,
                'completed_tasks': completed_tasks,
                'quality_score': quality_score
            }
        
        return contributions
    
    def _generate_efficiency_summary(self, efficiency_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成效能摘要"""
        summary = {}
        
        for category, indicators in efficiency_results.items():
            if indicators:
                # 计算该分类的平均值
                values = [float(value) for value in indicators.values() if isinstance(value, Decimal)]
                if values:
                    summary[category] = {
                        'average_score': round(sum(values) / len(values), 2),
                        'indicator_count': len(indicators),
                        'max_score': round(max(values), 2),
                        'min_score': round(min(values), 2)
                    }
        
        return summary
    
    def _generate_contribution_analysis(self, contribution_values: Dict[str, Any]) -> Dict[str, Any]:
        """生成贡献值分析"""
        analysis = {}
        
        # 设备贡献值分析
        equipment_contributions = contribution_values.get('equipment', {})
        if equipment_contributions:
            equipment_scores = [data['contribution_score'] for data in equipment_contributions.values()]
            analysis['equipment'] = {
                'total_equipment': len(equipment_contributions),
                'average_contribution': round(sum(equipment_scores) / len(equipment_scores), 2),
                'top_contributor': max(equipment_contributions.items(), key=lambda x: x[1]['contribution_score'])[0]
            }
        
        # 人员贡献值分析
        personnel_contributions = contribution_values.get('personnel', {})
        if personnel_contributions:
            personnel_scores = [data['contribution_score'] for data in personnel_contributions.values()]
            analysis['personnel'] = {
                'total_personnel': len(personnel_contributions),
                'average_contribution': round(sum(personnel_scores) / len(personnel_scores), 2),
                'top_contributor': max(personnel_contributions.items(), key=lambda x: x[1]['contribution_score'])[0]
            }
        
        return analysis
    
    def _generate_recommendations(self, efficiency_results: Dict[str, Any], 
                                contribution_values: Dict[str, Any]) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        # 基于效能结果生成建议
        for category, indicators in efficiency_results.items():
            if indicators:
                values = [float(value) for value in indicators.values() if isinstance(value, Decimal)]
                if values:
                    avg_score = sum(values) / len(values)
                    if avg_score < 70:
                        recommendations.append(f"建议重点关注{category}指标的改进，当前平均得分为{avg_score:.1f}")
        
        # 基于贡献值生成建议
        equipment_contributions = contribution_values.get('equipment', {})
        if equipment_contributions:
            low_contrib_equipment = [eq_id for eq_id, data in equipment_contributions.items() 
                                   if data['contribution_score'] < 50]
            if low_contrib_equipment:
                recommendations.append(f"建议优化以下设备的使用效率: {', '.join(low_contrib_equipment)}")
        
        if not recommendations:
            recommendations.append("整体效能表现良好，建议继续保持当前作业水平")
        
        return recommendations
