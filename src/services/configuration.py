"""
配置方案管理服务

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含配置方案管理相关的业务逻辑
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from src.database.models.configuration import (
    ConfigurationScheme,
    SchemeVersion,
    SchemeTypeEnum,
    SchemeStatusEnum
)
from src.schemas.configuration import (
    ConfigurationSchemeCreateSchema,
    ConfigurationSchemeUpdateSchema,
    ConfigurationSchemeResponseSchema,
    ConfigurationSchemeListResponseSchema,
    SchemeValidationResultSchema,
    SchemeVersionResponseSchema,
    SchemeVersionListResponseSchema
)


class ConfigurationService:
    """
    配置方案管理服务类
    
    提供配置方案的CRUD操作和业务逻辑处理：
    - 创建、查询、更新、删除配置方案
    - 配置方案列表查询和筛选
    - 配置方案验证和版本控制
    """
    
    def __init__(self, db_session: AsyncSession) -> None:
        """
        初始化配置方案服务
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def create_configuration_scheme(
        self,
        scheme_data: ConfigurationSchemeCreateSchema
    ) -> ConfigurationSchemeResponseSchema:
        """
        创建配置方案
        
        Args:
            scheme_data: 配置方案创建数据
            
        Returns:
            ConfigurationSchemeResponseSchema: 创建的配置方案信息
            
        Raises:
            Exception: 数据库操作异常
        """
        # 创建配置方案实例
        configuration_scheme = ConfigurationScheme(
            scheme_name=scheme_data.scheme_name,
            scheme_type=scheme_data.scheme_type,
            description=scheme_data.description,
            equipment_config_ids=scheme_data.equipment_config_ids,
            aircraft_config_ids=scheme_data.aircraft_config_ids,
            personnel_config_ids=scheme_data.personnel_config_ids,
            weather_condition_ids=scheme_data.weather_condition_ids,
            scenario_parameters=scheme_data.scenario_parameters,
            operational_parameters=scheme_data.operational_parameters.model_dump(),
            tags=scheme_data.tags,
            category=scheme_data.category,
            created_by=scheme_data.created_by,
            status=SchemeStatusEnum.DRAFT
        )
        
        # 保存到数据库
        self.db_session.add(configuration_scheme)
        await self.db_session.commit()
        await self.db_session.refresh(configuration_scheme)
        
        # 创建初始版本
        await self._create_scheme_version(
            configuration_scheme.id,
            "1.0.0",
            "初始版本",
            configuration_scheme.to_dict(),
            scheme_data.created_by
        )
        
        return ConfigurationSchemeResponseSchema.model_validate(configuration_scheme)
    
    async def get_configuration_scheme_by_id(
        self,
        scheme_id: str
    ) -> Optional[ConfigurationSchemeResponseSchema]:
        """
        根据ID获取配置方案
        
        Args:
            scheme_id: 配置方案ID
            
        Returns:
            Optional[ConfigurationSchemeResponseSchema]: 配置方案信息，不存在时返回None
        """
        query = select(ConfigurationScheme).where(ConfigurationScheme.id == scheme_id)
        result = await self.db_session.execute(query)
        configuration_scheme = result.scalar_one_or_none()
        
        if configuration_scheme is None:
            return None
        
        return ConfigurationSchemeResponseSchema.model_validate(configuration_scheme)
    
    async def get_configuration_scheme_list(
        self,
        page: int = 1,
        page_size: int = 20,
        scheme_type: Optional[SchemeTypeEnum] = None,
        status: Optional[SchemeStatusEnum] = None,
        category: Optional[str] = None,
        search_keyword: Optional[str] = None
    ) -> ConfigurationSchemeListResponseSchema:
        """
        获取配置方案列表
        
        Args:
            page: 页码
            page_size: 每页数量
            scheme_type: 方案类型筛选
            status: 状态筛选
            category: 分类筛选
            search_keyword: 搜索关键词
            
        Returns:
            ConfigurationSchemeListResponseSchema: 配置方案列表响应
        """
        # 构建查询条件
        conditions = []
        
        if scheme_type:
            conditions.append(ConfigurationScheme.scheme_type == scheme_type)
        
        if status:
            conditions.append(ConfigurationScheme.status == status)
        
        if category:
            conditions.append(ConfigurationScheme.category == category)
        
        if search_keyword:
            search_condition = or_(
                ConfigurationScheme.scheme_name.ilike(f"%{search_keyword}%"),
                ConfigurationScheme.description.ilike(f"%{search_keyword}%")
            )
            conditions.append(search_condition)
        
        # 构建基础查询
        base_query = select(ConfigurationScheme)
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 获取总数
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        offset = (page - 1) * page_size
        schemes_query = (
            base_query
            .order_by(ConfigurationScheme.created_at.desc())
            .offset(offset)
            .limit(page_size)
        )
        
        schemes_result = await self.db_session.execute(schemes_query)
        schemes = schemes_result.scalars().all()
        
        # 转换为响应模式
        scheme_list = [
            ConfigurationSchemeResponseSchema.model_validate(scheme)
            for scheme in schemes
        ]
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size
        
        return ConfigurationSchemeListResponseSchema(
            schemes=scheme_list,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
    
    async def update_configuration_scheme(
        self,
        scheme_id: str,
        update_data: ConfigurationSchemeUpdateSchema
    ) -> Optional[ConfigurationSchemeResponseSchema]:
        """
        更新配置方案信息
        
        Args:
            scheme_id: 配置方案ID
            update_data: 更新数据
            
        Returns:
            Optional[ConfigurationSchemeResponseSchema]: 更新后的配置方案信息，不存在时返回None
        """
        # 查询配置方案
        query = select(ConfigurationScheme).where(ConfigurationScheme.id == scheme_id)
        result = await self.db_session.execute(query)
        configuration_scheme = result.scalar_one_or_none()
        
        if configuration_scheme is None:
            return None
        
        # 保存更新前的数据用于版本控制
        old_data = configuration_scheme.to_dict()
        
        # 更新字段
        update_dict = update_data.model_dump(exclude_unset=True)
        
        for field, value in update_dict.items():
            if hasattr(configuration_scheme, field):
                # 处理嵌套对象
                if field == 'operational_parameters':
                    if value is not None:
                        setattr(configuration_scheme, field, value.model_dump() if hasattr(value, 'model_dump') else value)
                else:
                    setattr(configuration_scheme, field, value)
        
        # 保存更改
        await self.db_session.commit()
        await self.db_session.refresh(configuration_scheme)
        
        # 创建新版本（如果有实质性更改）
        if self._has_significant_changes(old_data, configuration_scheme.to_dict()):
            new_version = self._increment_version(configuration_scheme.version)
            configuration_scheme.version = new_version
            
            await self._create_scheme_version(
                scheme_id,
                new_version,
                "配置更新",
                configuration_scheme.to_dict(),
                update_data.model_dump().get('created_by')
            )
            
            await self.db_session.commit()
            await self.db_session.refresh(configuration_scheme)
        
        return ConfigurationSchemeResponseSchema.model_validate(configuration_scheme)
    
    async def delete_configuration_scheme(self, scheme_id: str) -> bool:
        """
        删除配置方案（软删除）
        
        Args:
            scheme_id: 配置方案ID
            
        Returns:
            bool: 删除成功返回True，配置方案不存在返回False
        """
        # 查询配置方案
        query = select(ConfigurationScheme).where(ConfigurationScheme.id == scheme_id)
        result = await self.db_session.execute(query)
        configuration_scheme = result.scalar_one_or_none()
        
        if configuration_scheme is None:
            return False
        
        # 软删除：更新状态为已归档
        configuration_scheme.status = SchemeStatusEnum.ARCHIVED
        await self.db_session.commit()
        
        return True
    
    async def validate_configuration_scheme(
        self,
        scheme_id: str
    ) -> SchemeValidationResultSchema:
        """
        验证配置方案的合理性
        
        Args:
            scheme_id: 配置方案ID
            
        Returns:
            SchemeValidationResultSchema: 验证结果
        """
        # 获取配置方案
        scheme = await self.get_configuration_scheme_by_id(scheme_id)
        if scheme is None:
            return SchemeValidationResultSchema(
                overall_status="error",
                validation_errors=["配置方案不存在"],
                validation_warnings=[],
                resource_compatibility={},
                optimization_suggestions=[],
                performance_prediction={}
            )
        
        errors = []
        warnings = []
        
        # 验证配置ID的有效性
        if not scheme.equipment_config_ids and not scheme.aircraft_config_ids:
            errors.append("必须至少配置一种设备或飞机")
        
        # 验证作业参数的合理性
        operational_params = scheme.operational_parameters
        if operational_params.get('max_operation_duration', 0) <= 0:
            errors.append("最大作业时长必须大于0")
        
        if operational_params.get('safety_margin', 0) < 0.1:
            warnings.append("建议安全裕度不少于10%")
        
        # 确定总体状态
        if errors:
            overall_status = "invalid"
        elif warnings:
            overall_status = "valid_with_warnings"
        else:
            overall_status = "valid"
        
        return SchemeValidationResultSchema(
            overall_status=overall_status,
            validation_errors=errors,
            validation_warnings=warnings,
            resource_compatibility=self._analyze_resource_compatibility(scheme),
            optimization_suggestions=self._generate_optimization_suggestions(scheme),
            performance_prediction=self._predict_performance(scheme)
        )
    
    async def get_scheme_versions(
        self,
        scheme_id: str
    ) -> SchemeVersionListResponseSchema:
        """
        获取配置方案的版本列表
        
        Args:
            scheme_id: 配置方案ID
            
        Returns:
            SchemeVersionListResponseSchema: 版本列表响应
        """
        query = (
            select(SchemeVersion)
            .where(SchemeVersion.scheme_id == scheme_id)
            .order_by(SchemeVersion.created_at.desc())
        )
        
        result = await self.db_session.execute(query)
        versions = result.scalars().all()
        
        version_list = [
            SchemeVersionResponseSchema.model_validate(version)
            for version in versions
        ]
        
        return SchemeVersionListResponseSchema(
            versions=version_list,
            total=len(version_list)
        )
    
    async def _create_scheme_version(
        self,
        scheme_id: str,
        version_number: str,
        change_description: str,
        configuration_snapshot: Dict[str, Any],
        created_by: Optional[str] = None
    ) -> None:
        """
        创建方案版本
        
        Args:
            scheme_id: 方案ID
            version_number: 版本号
            change_description: 变更描述
            configuration_snapshot: 配置数据快照
            created_by: 创建者
        """
        # 将之前的版本设为非当前版本
        update_query = (
            select(SchemeVersion)
            .where(SchemeVersion.scheme_id == scheme_id)
            .where(SchemeVersion.is_current == True)
        )
        result = await self.db_session.execute(update_query)
        current_versions = result.scalars().all()
        
        for version in current_versions:
            version.is_current = False
        
        # 创建新版本
        new_version = SchemeVersion(
            scheme_id=scheme_id,
            version_number=version_number,
            change_description=change_description,
            configuration_snapshot=configuration_snapshot,
            is_current=True,
            created_by=created_by
        )
        
        self.db_session.add(new_version)
        await self.db_session.commit()
    
    def _has_significant_changes(
        self,
        old_data: Dict[str, Any],
        new_data: Dict[str, Any]
    ) -> bool:
        """
        检查是否有实质性更改
        
        Args:
            old_data: 旧数据
            new_data: 新数据
            
        Returns:
            bool: 是否有实质性更改
        """
        # 检查关键字段是否有变化
        key_fields = [
            'equipment_config_ids',
            'aircraft_config_ids',
            'personnel_config_ids',
            'operational_parameters'
        ]
        
        for field in key_fields:
            if old_data.get(field) != new_data.get(field):
                return True
        
        return False
    
    def _increment_version(self, current_version: str) -> str:
        """
        递增版本号
        
        Args:
            current_version: 当前版本号
            
        Returns:
            str: 新版本号
        """
        try:
            parts = current_version.split('.')
            if len(parts) == 3:
                major, minor, patch = map(int, parts)
                return f"{major}.{minor}.{patch + 1}"
        except ValueError:
            pass
        
        return "1.0.1"
    
    def _analyze_resource_compatibility(
        self,
        scheme: ConfigurationSchemeResponseSchema
    ) -> Dict[str, Any]:
        """
        分析资源兼容性
        
        Args:
            scheme: 配置方案
            
        Returns:
            Dict[str, Any]: 兼容性分析结果
        """
        return {
            "equipment_aircraft_compatibility": "good",
            "personnel_equipment_ratio": "adequate",
            "overall_compatibility_score": 0.85
        }
    
    def _generate_optimization_suggestions(
        self,
        scheme: ConfigurationSchemeResponseSchema
    ) -> List[Dict[str, Any]]:
        """
        生成优化建议
        
        Args:
            scheme: 配置方案
            
        Returns:
            List[Dict[str, Any]]: 优化建议列表
        """
        suggestions = []
        
        # 基于配置数量提供建议
        if len(scheme.equipment_config_ids) < 2:
            suggestions.append({
                "category": "equipment",
                "suggestion": "建议增加备用设备配置",
                "expected_improvement": 0.1
            })
        
        return suggestions
    
    def _predict_performance(
        self,
        scheme: ConfigurationSchemeResponseSchema
    ) -> Dict[str, Any]:
        """
        预测性能
        
        Args:
            scheme: 配置方案
            
        Returns:
            Dict[str, Any]: 性能预测结果
        """
        return {
            "estimated_completion_time": 8.5,
            "efficiency_score": 0.87,
            "resource_utilization": 0.92,
            "safety_score": 0.94
        }
