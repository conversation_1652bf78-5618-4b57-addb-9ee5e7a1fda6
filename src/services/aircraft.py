"""
飞机配置管理服务

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含飞机配置管理相关的业务逻辑
"""

from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from src.database.models.aircraft import AircraftConfig, AircraftModelEnum, AircraftStatusEnum
from src.schemas.aircraft import (
    AircraftCreateSchema,
    AircraftUpdateSchema,
    AircraftResponseSchema,
    AircraftListResponseSchema
)


class AircraftService:
    """
    飞机配置管理服务类
    
    提供飞机配置的CRUD操作和业务逻辑处理：
    - 创建、查询、更新、删除飞机配置
    - 飞机配置列表查询和筛选
    - 飞机配置参数验证和处理
    """
    
    def __init__(self, db_session: AsyncSession) -> None:
        """
        初始化飞机配置服务
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def create_aircraft_config(
        self,
        aircraft_data: AircraftCreateSchema
    ) -> AircraftResponseSchema:
        """
        创建飞机配置
        
        Args:
            aircraft_data: 飞机配置创建数据
            
        Returns:
            AircraftResponseSchema: 创建的飞机配置信息
            
        Raises:
            Exception: 数据库操作异常
        """
        # 创建飞机配置实例
        aircraft_config = AircraftConfig(
            aircraft_model=aircraft_data.aircraft_model,
            quantity=aircraft_data.quantity,
            payload_capacity=aircraft_data.payload_capacity,
            operational_range=aircraft_data.operational_range,
            cruise_speed=aircraft_data.cruise_speed,
            fuel_consumption=aircraft_data.fuel_consumption,
            loading_time=aircraft_data.loading_time,
            unloading_time=aircraft_data.unloading_time,
            ground_taxi_time=aircraft_data.ground_taxi_time,
            crew_requirements=aircraft_data.crew_requirements.model_dump(),
            compatible_equipment=aircraft_data.compatible_equipment,
            maintenance_schedule=aircraft_data.maintenance_schedule.model_dump(),
            status=aircraft_data.status,
            remarks=aircraft_data.remarks
        )
        
        # 保存到数据库
        self.db_session.add(aircraft_config)
        await self.db_session.commit()
        await self.db_session.refresh(aircraft_config)
        
        return AircraftResponseSchema.model_validate(aircraft_config)
    
    async def get_aircraft_config_by_id(
        self,
        aircraft_id: str
    ) -> Optional[AircraftResponseSchema]:
        """
        根据ID获取飞机配置
        
        Args:
            aircraft_id: 飞机配置ID
            
        Returns:
            Optional[AircraftResponseSchema]: 飞机配置信息，不存在时返回None
        """
        query = select(AircraftConfig).where(AircraftConfig.id == aircraft_id)
        result = await self.db_session.execute(query)
        aircraft_config = result.scalar_one_or_none()
        
        if aircraft_config is None:
            return None
        
        return AircraftResponseSchema.model_validate(aircraft_config)
    
    async def get_aircraft_config_list(
        self,
        page: int = 1,
        page_size: int = 20,
        aircraft_model: Optional[AircraftModelEnum] = None,
        status: Optional[AircraftStatusEnum] = None,
        search_keyword: Optional[str] = None
    ) -> AircraftListResponseSchema:
        """
        获取飞机配置列表
        
        Args:
            page: 页码
            page_size: 每页数量
            aircraft_model: 飞机型号筛选
            status: 状态筛选
            search_keyword: 搜索关键词
            
        Returns:
            AircraftListResponseSchema: 飞机配置列表响应
        """
        # 构建查询条件
        conditions = []
        
        if aircraft_model:
            conditions.append(AircraftConfig.aircraft_model == aircraft_model)
        
        if status:
            conditions.append(AircraftConfig.status == status)
        
        if search_keyword:
            search_condition = or_(
                AircraftConfig.aircraft_model.ilike(f"%{search_keyword}%"),
                AircraftConfig.remarks.ilike(f"%{search_keyword}%")
            )
            conditions.append(search_condition)
        
        # 构建基础查询
        base_query = select(AircraftConfig)
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 获取总数
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        offset = (page - 1) * page_size
        aircraft_configs_query = (
            base_query
            .order_by(AircraftConfig.created_at.desc())
            .offset(offset)
            .limit(page_size)
        )
        
        aircraft_configs_result = await self.db_session.execute(aircraft_configs_query)
        aircraft_configs = aircraft_configs_result.scalars().all()
        
        # 转换为响应模式
        aircraft_config_list = [
            AircraftResponseSchema.model_validate(aircraft_config)
            for aircraft_config in aircraft_configs
        ]
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size
        
        return AircraftListResponseSchema(
            aircraft_configs=aircraft_config_list,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
    
    async def update_aircraft_config(
        self,
        aircraft_id: str,
        update_data: AircraftUpdateSchema
    ) -> Optional[AircraftResponseSchema]:
        """
        更新飞机配置信息
        
        Args:
            aircraft_id: 飞机配置ID
            update_data: 更新数据
            
        Returns:
            Optional[AircraftResponseSchema]: 更新后的飞机配置信息，不存在时返回None
        """
        # 查询飞机配置
        query = select(AircraftConfig).where(AircraftConfig.id == aircraft_id)
        result = await self.db_session.execute(query)
        aircraft_config = result.scalar_one_or_none()
        
        if aircraft_config is None:
            return None
        
        # 更新字段
        update_dict = update_data.model_dump(exclude_unset=True)
        
        for field, value in update_dict.items():
            if hasattr(aircraft_config, field):
                # 处理嵌套对象
                if field in ['crew_requirements', 'maintenance_schedule']:
                    if value is not None:
                        setattr(aircraft_config, field, value.model_dump() if hasattr(value, 'model_dump') else value)
                else:
                    setattr(aircraft_config, field, value)
        
        # 保存更改
        await self.db_session.commit()
        await self.db_session.refresh(aircraft_config)
        
        return AircraftResponseSchema.model_validate(aircraft_config)
    
    async def delete_aircraft_config(self, aircraft_id: str) -> bool:
        """
        删除飞机配置
        
        Args:
            aircraft_id: 飞机配置ID
            
        Returns:
            bool: 删除成功返回True，飞机配置不存在返回False
        """
        # 查询飞机配置
        query = select(AircraftConfig).where(AircraftConfig.id == aircraft_id)
        result = await self.db_session.execute(query)
        aircraft_config = result.scalar_one_or_none()
        
        if aircraft_config is None:
            return False
        
        # 删除飞机配置
        await self.db_session.delete(aircraft_config)
        await self.db_session.commit()
        
        return True
    
    async def validate_aircraft_parameters(
        self,
        aircraft_data: AircraftCreateSchema
    ) -> List[str]:
        """
        验证飞机配置参数的合理性
        
        Args:
            aircraft_data: 飞机配置数据
            
        Returns:
            List[str]: 验证错误信息列表，空列表表示验证通过
        """
        errors = []
        
        # 根据机型验证参数的合理性
        model_specs = self._get_aircraft_model_specifications(aircraft_data.aircraft_model)
        
        # 验证载重能力
        if aircraft_data.payload_capacity > model_specs["max_payload"]:
            errors.append(f"{aircraft_data.aircraft_model}的载重能力不能超过{model_specs['max_payload']}kg")
        
        # 验证航程
        if aircraft_data.operational_range > model_specs["max_range"]:
            errors.append(f"{aircraft_data.aircraft_model}的航程不能超过{model_specs['max_range']}km")
        
        # 验证巡航速度
        if aircraft_data.cruise_speed > model_specs["max_speed"]:
            errors.append(f"{aircraft_data.aircraft_model}的巡航速度不能超过{model_specs['max_speed']}km/h")
        
        # 验证装卸时间的合理性
        if aircraft_data.loading_time < 10:  # 少于10分钟
            errors.append("装载时间不能少于10分钟")
        
        if aircraft_data.unloading_time < 10:  # 少于10分钟
            errors.append("卸载时间不能少于10分钟")
        
        # 验证机组人员配置
        crew_errors = self._validate_crew_requirements(
            aircraft_data.aircraft_model,
            aircraft_data.crew_requirements
        )
        errors.extend(crew_errors)
        
        return errors
    
    def _get_aircraft_model_specifications(self, model: AircraftModelEnum) -> dict:
        """
        获取飞机型号的技术规格
        
        Args:
            model: 飞机型号
            
        Returns:
            dict: 技术规格字典
        """
        specifications = {
            AircraftModelEnum.Y_20: {
                "max_payload": 66000,
                "max_range": 4400,
                "max_speed": 800
            },
            AircraftModelEnum.Y_8: {
                "max_payload": 20000,
                "max_range": 5700,
                "max_speed": 550
            },
            AircraftModelEnum.Y_7: {
                "max_payload": 5500,
                "max_range": 1400,
                "max_speed": 400
            },
            AircraftModelEnum.Y_31: {
                "max_payload": 30000,
                "max_range": 3000,
                "max_speed": 600
            }
        }
        
        return specifications.get(model, {
            "max_payload": 100000,
            "max_range": 10000,
            "max_speed": 1000
        })
    
    def _validate_crew_requirements(
        self,
        model: AircraftModelEnum,
        crew_requirements: dict
    ) -> List[str]:
        """
        验证机组人员配置的合理性
        
        Args:
            model: 飞机型号
            crew_requirements: 机组人员需求
            
        Returns:
            List[str]: 验证错误信息列表
        """
        errors = []
        
        # 验证飞行员数量
        pilots = crew_requirements.get("pilots", 0)
        if pilots < 2:
            errors.append("飞行员数量不能少于2人")
        
        # 根据机型验证机组配置
        if model in [AircraftModelEnum.Y_20, AircraftModelEnum.Y_31]:
            # 大型运输机需要飞行工程师
            flight_engineers = crew_requirements.get("flight_engineers", 0)
            if flight_engineers < 1:
                errors.append(f"{model}需要至少1名飞行工程师")
        
        return errors
