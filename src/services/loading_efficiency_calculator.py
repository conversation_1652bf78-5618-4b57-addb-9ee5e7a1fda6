"""
装卸载作业效能指标计算器

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含装卸载作业效能指标计算相关的逻辑
"""

from typing import Dict, Any, List, Optional
from decimal import Decimal
from datetime import datetime, timedelta
import math


class TimelinessCalculator:
    """
    装卸载作业时效性指标计算器
    
    实现时效性相关指标的计算：
    - 作业完成时间
    - 平均响应时间
    - 准时完成率
    - 单位时间处理量
    """
    
    @staticmethod
    def calculate_operation_completion_time(start_time: datetime, end_time: datetime) -> Decimal:
        """
        计算作业完成时间
        
        Args:
            start_time: 作业开始时间
            end_time: 作业结束时间
            
        Returns:
            Decimal: 作业完成时间（小时）
            
        Raises:
            ValueError: 当结束时间早于或等于开始时间时
        """
        if end_time <= start_time:
            raise ValueError("结束时间必须晚于开始时间")
        
        duration = end_time - start_time
        hours = Decimal(str(duration.total_seconds() / 3600))
        return hours.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_average_response_time(response_times: List[float]) -> Decimal:
        """
        计算平均响应时间
        
        Args:
            response_times: 响应时间列表（分钟）
            
        Returns:
            Decimal: 平均响应时间（分钟）
        """
        if not response_times:
            return Decimal('0')
        
        average = sum(response_times) / len(response_times)
        return Decimal(str(average)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_on_time_completion_rate(total_operations: int, on_time_operations: int) -> Decimal:
        """
        计算准时完成率
        
        Args:
            total_operations: 总作业数
            on_time_operations: 准时完成的作业数
            
        Returns:
            Decimal: 准时完成率（百分比）
        """
        if total_operations <= 0:
            return Decimal('0')
        
        rate = (on_time_operations / total_operations) * 100
        return Decimal(str(rate)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_processing_volume_per_hour(total_volume: Decimal, duration_hours: Decimal) -> Decimal:
        """
        计算单位时间处理量
        
        Args:
            total_volume: 总处理量（吨或件）
            duration_hours: 持续时间（小时）
            
        Returns:
            Decimal: 单位时间处理量（吨/小时或件/小时）
        """
        if duration_hours <= 0:
            return Decimal('0')
        
        volume_per_hour = total_volume / duration_hours
        return volume_per_hour.quantize(Decimal('0.01'))


class EfficiencyCalculator:
    """
    装卸载作业效率指标计算器
    
    实现效率相关指标的计算：
    - 设备利用率
    - 人员利用率
    - 作业成功率
    - 设备作业效率
    """
    
    @staticmethod
    def calculate_equipment_utilization_rate(actual_usage_hours: Decimal, available_hours: Decimal) -> Decimal:
        """
        计算设备利用率
        
        Args:
            actual_usage_hours: 实际使用时间（小时）
            available_hours: 可用时间（小时）
            
        Returns:
            Decimal: 设备利用率（百分比）
        """
        if available_hours <= 0:
            return Decimal('0')
        
        utilization_rate = (actual_usage_hours / available_hours) * 100
        return utilization_rate.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_personnel_utilization_rate(actual_work_hours: Decimal, scheduled_hours: Decimal) -> Decimal:
        """
        计算人员利用率
        
        Args:
            actual_work_hours: 实际工作时间（小时）
            scheduled_hours: 计划工作时间（小时）
            
        Returns:
            Decimal: 人员利用率（百分比）
        """
        if scheduled_hours <= 0:
            return Decimal('0')
        
        utilization_rate = (actual_work_hours / scheduled_hours) * 100
        return utilization_rate.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_operation_success_rate(total_operations: int, successful_operations: int) -> Decimal:
        """
        计算作业成功率
        
        Args:
            total_operations: 总作业数
            successful_operations: 成功完成的作业数
            
        Returns:
            Decimal: 作业成功率（百分比）
        """
        if total_operations <= 0:
            return Decimal('0')
        
        success_rate = (successful_operations / total_operations) * 100
        return Decimal(str(success_rate)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_equipment_operation_efficiency(processed_volume: Decimal, operation_time: Decimal) -> Decimal:
        """
        计算设备作业效率
        
        Args:
            processed_volume: 处理量（吨或件）
            operation_time: 作业时间（小时）
            
        Returns:
            Decimal: 设备作业效率（吨/小时或件/小时）
        """
        if operation_time <= 0:
            return Decimal('0')
        
        efficiency = processed_volume / operation_time
        return efficiency.quantize(Decimal('0.01'))


class QualityCalculator:
    """
    装卸载作业质量指标计算器
    
    实现质量相关指标的计算：
    - 货物完好率
    - 作业精度
    - 安全事故率
    - 返工率
    """
    
    @staticmethod
    def calculate_cargo_integrity_rate(total_cargo: int, intact_cargo: int) -> Decimal:
        """
        计算货物完好率
        
        Args:
            total_cargo: 总货物数量
            intact_cargo: 完好货物数量
            
        Returns:
            Decimal: 货物完好率（百分比）
        """
        if total_cargo <= 0:
            return Decimal('0')
        
        integrity_rate = (intact_cargo / total_cargo) * 100
        return Decimal(str(integrity_rate)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_operation_accuracy(target_positions: int, accurate_positions: int) -> Decimal:
        """
        计算作业精度
        
        Args:
            target_positions: 目标位置数
            accurate_positions: 准确位置数
            
        Returns:
            Decimal: 作业精度（百分比）
        """
        if target_positions <= 0:
            return Decimal('0')
        
        accuracy = (accurate_positions / target_positions) * 100
        return Decimal(str(accuracy)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_safety_incident_rate(total_operations: int, safety_incidents: int) -> Decimal:
        """
        计算安全事故率
        
        Args:
            total_operations: 总作业数
            safety_incidents: 安全事故数
            
        Returns:
            Decimal: 安全事故率（百分比）
        """
        if total_operations <= 0:
            return Decimal('0')
        
        incident_rate = (safety_incidents / total_operations) * 100
        return Decimal(str(incident_rate)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_rework_rate(total_operations: int, rework_operations: int) -> Decimal:
        """
        计算返工率
        
        Args:
            total_operations: 总作业数
            rework_operations: 返工作业数
            
        Returns:
            Decimal: 返工率（百分比）
        """
        if total_operations <= 0:
            return Decimal('0')
        
        rework_rate = (rework_operations / total_operations) * 100
        return Decimal(str(rework_rate)).quantize(Decimal('0.01'))


class ResourceConfigCalculator:
    """
    装卸载作业资源配置指标计算器
    
    实现资源配置相关指标的计算：
    - 设备配置合理性
    - 人员配置合理性
    - 资源利用率
    - 成本效益比
    """
    
    @staticmethod
    def calculate_equipment_config_rationality(required_equipment: int, actual_equipment: int) -> Decimal:
        """
        计算设备配置合理性

        Args:
            required_equipment: 所需设备数量
            actual_equipment: 实际设备数量

        Returns:
            Decimal: 设备配置合理性评分（0-100）
        """
        if required_equipment <= 0:
            return Decimal('0')

        # 配置合理性评分：实际配置与需求的匹配度
        ratio = actual_equipment / required_equipment
        if ratio >= 0.9 and ratio <= 1.1:
            # 在90%-110%范围内认为配置合理
            score = Decimal('100')
        elif ratio < 0.9:
            # 配置不足
            score = Decimal(str(ratio * 100))
        else:
            # 配置过多，效率递减
            score = Decimal(str(100 - (ratio - 1) * 50))
            if score < 0:
                score = Decimal('0')

        return score.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_personnel_config_rationality(required_personnel: int, actual_personnel: int) -> Decimal:
        """
        计算人员配置合理性
        
        Args:
            required_personnel: 所需人员数量
            actual_personnel: 实际人员数量
            
        Returns:
            Decimal: 人员配置合理性评分（0-100）
        """
        if required_personnel <= 0:
            return Decimal('0')
        
        # 人员配置合理性评分：实际配置与需求的匹配度
        ratio = actual_personnel / required_personnel
        if ratio >= 0.9 and ratio <= 1.1:
            # 在90%-110%范围内认为配置合理
            score = Decimal('100')
        elif ratio < 0.9:
            # 人员不足
            score = Decimal(str(ratio * 100))
        else:
            # 人员过多，成本增加
            score = Decimal(str(100 - (ratio - 1) * 40))
            if score < 0:
                score = Decimal('0')
        
        return score.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_resource_utilization_rate(used_resources: Decimal, total_resources: Decimal) -> Decimal:
        """
        计算资源利用率
        
        Args:
            used_resources: 已使用资源
            total_resources: 总资源
            
        Returns:
            Decimal: 资源利用率（百分比）
        """
        if total_resources <= 0:
            return Decimal('0')
        
        utilization_rate = (used_resources / total_resources) * 100
        return utilization_rate.quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_cost_benefit_ratio(total_benefit: Decimal, total_cost: Decimal) -> Decimal:
        """
        计算成本效益比
        
        Args:
            total_benefit: 总效益
            total_cost: 总成本
            
        Returns:
            Decimal: 成本效益比
        """
        if total_cost <= 0:
            return Decimal('0')
        
        ratio = total_benefit / total_cost
        return ratio.quantize(Decimal('0.01'))


class CoordinationCalculator:
    """
    装卸载作业协调性指标计算器
    
    实现协调性相关指标的计算：
    - 设备协调度
    - 人机协调度
    - 流程顺畅度
    - 等待时间比
    """
    
    @staticmethod
    def calculate_equipment_coordination_degree(coordination_events: int, total_events: int) -> Decimal:
        """
        计算设备协调度
        
        Args:
            coordination_events: 协调事件数
            total_events: 总事件数
            
        Returns:
            Decimal: 设备协调度（百分比）
        """
        if total_events <= 0:
            return Decimal('0')
        
        coordination_degree = (coordination_events / total_events) * 100
        return Decimal(str(coordination_degree)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_human_machine_coordination_degree(smooth_operations: int, total_operations: int) -> Decimal:
        """
        计算人机协调度
        
        Args:
            smooth_operations: 顺畅作业数
            total_operations: 总作业数
            
        Returns:
            Decimal: 人机协调度（百分比）
        """
        if total_operations <= 0:
            return Decimal('0')
        
        coordination_degree = (smooth_operations / total_operations) * 100
        return Decimal(str(coordination_degree)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_process_smoothness_degree(smooth_processes: int, total_processes: int) -> Decimal:
        """
        计算流程顺畅度
        
        Args:
            smooth_processes: 顺畅流程数
            total_processes: 总流程数
            
        Returns:
            Decimal: 流程顺畅度（百分比）
        """
        if total_processes <= 0:
            return Decimal('0')
        
        smoothness_degree = (smooth_processes / total_processes) * 100
        return Decimal(str(smoothness_degree)).quantize(Decimal('0.01'))
    
    @staticmethod
    def calculate_waiting_time_ratio(waiting_time: Decimal, total_time: Decimal) -> Decimal:
        """
        计算等待时间比
        
        Args:
            waiting_time: 等待时间（小时）
            total_time: 总时间（小时）
            
        Returns:
            Decimal: 等待时间比（百分比）
        """
        if total_time <= 0:
            return Decimal('0')
        
        waiting_ratio = (waiting_time / total_time) * 100
        return waiting_ratio.quantize(Decimal('0.01'))
