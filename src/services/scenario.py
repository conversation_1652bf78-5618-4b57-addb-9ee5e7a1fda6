"""
场景管理服务

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含场景管理相关的业务逻辑
"""

from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
from src.database.models.scenario import Scenario, ScenarioStatusEnum
from src.schemas.scenario import (
    ScenarioCreateSchema,
    ScenarioUpdateSchema,
    ScenarioResponseSchema,
    ScenarioListResponseSchema
)


class ScenarioService:
    """
    场景管理服务类
    
    提供场景的CRUD操作和业务逻辑处理：
    - 创建、查询、更新、删除场景
    - 场景列表查询和筛选
    - 场景参数验证和处理
    """
    
    def __init__(self, db_session: AsyncSession) -> None:
        """
        初始化场景服务
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def create_scenario(self, scenario_data: ScenarioCreateSchema) -> ScenarioResponseSchema:
        """
        创建新场景
        
        Args:
            scenario_data: 场景创建数据
            
        Returns:
            ScenarioResponseSchema: 创建的场景信息
            
        Raises:
            ValueError: 数据验证失败
            Exception: 数据库操作异常
        """
        # 创建场景实例
        scenario = Scenario(
            scenario_name=scenario_data.scenario_name,
            scenario_type=scenario_data.scenario_type,
            description=scenario_data.description,
            task_type=scenario_data.task_type,
            environment_conditions=scenario_data.environment_conditions.model_dump(),
            resource_constraints=scenario_data.resource_constraints.model_dump(),
            mission_requirements=scenario_data.mission_requirements.model_dump(),
            threat_factors=scenario_data.threat_factors.model_dump(),
            threat_level=scenario_data.threat_level,
            created_by=scenario_data.created_by,
            status=ScenarioStatusEnum.DRAFT
        )
        
        # 保存到数据库
        self.db_session.add(scenario)
        await self.db_session.commit()
        await self.db_session.refresh(scenario)
        
        return ScenarioResponseSchema.model_validate(scenario)
    
    async def get_scenario_by_id(self, scenario_id: str) -> Optional[ScenarioResponseSchema]:
        """
        根据ID获取场景
        
        Args:
            scenario_id: 场景ID
            
        Returns:
            Optional[ScenarioResponseSchema]: 场景信息，不存在时返回None
        """
        query = select(Scenario).where(Scenario.id == scenario_id)
        result = await self.db_session.execute(query)
        scenario = result.scalar_one_or_none()
        
        if scenario is None:
            return None
        
        return ScenarioResponseSchema.model_validate(scenario)
    
    async def get_scenario_list(
        self,
        page: int = 1,
        page_size: int = 20,
        scenario_type: Optional[str] = None,
        task_type: Optional[str] = None,
        status: Optional[ScenarioStatusEnum] = None,
        search_keyword: Optional[str] = None
    ) -> ScenarioListResponseSchema:
        """
        获取场景列表
        
        Args:
            page: 页码
            page_size: 每页数量
            scenario_type: 场景类型筛选
            task_type: 任务类型筛选
            status: 状态筛选
            search_keyword: 搜索关键词
            
        Returns:
            ScenarioListResponseSchema: 场景列表响应
        """
        # 构建查询条件
        conditions = []
        
        if scenario_type:
            conditions.append(Scenario.scenario_type == scenario_type)
        
        if task_type:
            conditions.append(Scenario.task_type == task_type)
        
        if status:
            conditions.append(Scenario.status == status)
        
        if search_keyword:
            search_condition = or_(
                Scenario.scenario_name.ilike(f"%{search_keyword}%"),
                Scenario.description.ilike(f"%{search_keyword}%")
            )
            conditions.append(search_condition)
        
        # 构建基础查询
        base_query = select(Scenario)
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 获取总数
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        offset = (page - 1) * page_size
        scenarios_query = (
            base_query
            .order_by(Scenario.created_at.desc())
            .offset(offset)
            .limit(page_size)
        )
        
        scenarios_result = await self.db_session.execute(scenarios_query)
        scenarios = scenarios_result.scalars().all()
        
        # 转换为响应模式
        scenario_list = [
            ScenarioResponseSchema.model_validate(scenario)
            for scenario in scenarios
        ]
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size
        
        return ScenarioListResponseSchema(
            scenarios=scenario_list,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
    
    async def update_scenario(
        self,
        scenario_id: str,
        update_data: ScenarioUpdateSchema
    ) -> Optional[ScenarioResponseSchema]:
        """
        更新场景信息
        
        Args:
            scenario_id: 场景ID
            update_data: 更新数据
            
        Returns:
            Optional[ScenarioResponseSchema]: 更新后的场景信息，不存在时返回None
        """
        # 查询场景
        query = select(Scenario).where(Scenario.id == scenario_id)
        result = await self.db_session.execute(query)
        scenario = result.scalar_one_or_none()
        
        if scenario is None:
            return None
        
        # 更新字段
        update_dict = update_data.model_dump(exclude_unset=True)
        
        for field, value in update_dict.items():
            if hasattr(scenario, field):
                # 处理嵌套对象
                if field in ['environment_conditions', 'resource_constraints', 
                           'mission_requirements', 'threat_factors']:
                    if value is not None:
                        setattr(scenario, field, value.model_dump() if hasattr(value, 'model_dump') else value)
                else:
                    setattr(scenario, field, value)
        
        # 保存更改
        await self.db_session.commit()
        await self.db_session.refresh(scenario)
        
        return ScenarioResponseSchema.model_validate(scenario)
    
    async def delete_scenario(self, scenario_id: str) -> bool:
        """
        删除场景（软删除）
        
        Args:
            scenario_id: 场景ID
            
        Returns:
            bool: 删除成功返回True，场景不存在返回False
        """
        # 查询场景
        query = select(Scenario).where(Scenario.id == scenario_id)
        result = await self.db_session.execute(query)
        scenario = result.scalar_one_or_none()
        
        if scenario is None:
            return False
        
        # 软删除：更新状态为已删除
        scenario.status = ScenarioStatusEnum.DELETED
        await self.db_session.commit()
        
        return True
    
    async def validate_scenario_parameters(self, scenario_data: Dict[str, Any]) -> List[str]:
        """
        验证场景参数的合理性
        
        Args:
            scenario_data: 场景数据
            
        Returns:
            List[str]: 验证错误信息列表，空列表表示验证通过
        """
        errors = []
        
        # 验证任务需求的合理性
        mission_requirements = scenario_data.get('mission_requirements', {})
        
        # 验证货物重量
        cargo_weight = mission_requirements.get('cargo_weight', 0)
        if cargo_weight <= 0:
            errors.append("货物重量必须大于0")
        
        # 验证起讫点
        origin = mission_requirements.get('origin')
        destination = mission_requirements.get('destination')
        
        if not origin:
            errors.append("必须指定出发地")
        
        if not destination:
            errors.append("必须指定目的地")
        
        if origin == destination:
            errors.append("出发地和目的地不能相同")
        
        # 验证环境条件的一致性
        environment_conditions = scenario_data.get('environment_conditions', {})
        weather = environment_conditions.get('weather', {})
        
        # 验证气象参数的合理性
        visibility = weather.get('visibility', 10000)
        if visibility < 0:
            errors.append("能见度不能为负数")
        
        wind_speed = weather.get('wind_speed', 0)
        if wind_speed < 0:
            errors.append("风速不能为负数")
        
        return errors
