"""
设备配置管理服务

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含设备配置管理相关的业务逻辑
"""

from typing import Optional, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from src.database.models.equipment import EquipmentConfig, EquipmentTypeEnum, EquipmentStatusEnum
from src.schemas.equipment import (
    EquipmentCreateSchema,
    EquipmentUpdateSchema,
    EquipmentResponseSchema,
    EquipmentListResponseSchema
)


class EquipmentService:
    """
    设备配置管理服务类
    
    提供设备配置的CRUD操作和业务逻辑处理：
    - 创建、查询、更新、删除设备配置
    - 设备配置列表查询和筛选
    - 设备配置参数验证和处理
    """
    
    def __init__(self, db_session: AsyncSession) -> None:
        """
        初始化设备配置服务
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def create_equipment_config(
        self,
        equipment_data: EquipmentCreateSchema
    ) -> EquipmentResponseSchema:
        """
        创建设备配置
        
        Args:
            equipment_data: 设备配置创建数据
            
        Returns:
            EquipmentResponseSchema: 创建的设备配置信息
            
        Raises:
            Exception: 数据库操作异常
        """
        # 创建设备配置实例
        equipment_config = EquipmentConfig(
            equipment_type=equipment_data.equipment_type,
            equipment_model=equipment_data.equipment_model,
            quantity=equipment_data.quantity,
            max_load_capacity=equipment_data.max_load_capacity,
            loading_speed=equipment_data.loading_speed,
            unloading_speed=equipment_data.unloading_speed,
            operation_radius=equipment_data.operation_radius,
            power_consumption=equipment_data.power_consumption,
            unit_operation_duration=equipment_data.unit_operation_duration,
            efficiency_factor=equipment_data.efficiency_factor,
            maintenance_interval=equipment_data.maintenance_interval,
            operational_status=equipment_data.operational_status,
            maintenance_info=equipment_data.maintenance_info,
            remarks=equipment_data.remarks
        )
        
        # 保存到数据库
        self.db_session.add(equipment_config)
        await self.db_session.commit()
        await self.db_session.refresh(equipment_config)
        
        return EquipmentResponseSchema.model_validate(equipment_config)
    
    async def get_equipment_config_by_id(
        self,
        equipment_id: str
    ) -> Optional[EquipmentResponseSchema]:
        """
        根据ID获取设备配置
        
        Args:
            equipment_id: 设备配置ID
            
        Returns:
            Optional[EquipmentResponseSchema]: 设备配置信息，不存在时返回None
        """
        query = select(EquipmentConfig).where(EquipmentConfig.id == equipment_id)
        result = await self.db_session.execute(query)
        equipment_config = result.scalar_one_or_none()
        
        if equipment_config is None:
            return None
        
        return EquipmentResponseSchema.model_validate(equipment_config)
    
    async def get_equipment_config_list(
        self,
        page: int = 1,
        page_size: int = 20,
        equipment_type: Optional[EquipmentTypeEnum] = None,
        status: Optional[EquipmentStatusEnum] = None,
        search_keyword: Optional[str] = None
    ) -> EquipmentListResponseSchema:
        """
        获取设备配置列表
        
        Args:
            page: 页码
            page_size: 每页数量
            equipment_type: 设备类型筛选
            status: 状态筛选
            search_keyword: 搜索关键词
            
        Returns:
            EquipmentListResponseSchema: 设备配置列表响应
        """
        # 构建查询条件
        conditions = []
        
        if equipment_type:
            conditions.append(EquipmentConfig.equipment_type == equipment_type)
        
        if status:
            conditions.append(EquipmentConfig.operational_status == status)
        
        if search_keyword:
            search_condition = or_(
                EquipmentConfig.equipment_model.ilike(f"%{search_keyword}%"),
                EquipmentConfig.remarks.ilike(f"%{search_keyword}%")
            )
            conditions.append(search_condition)
        
        # 构建基础查询
        base_query = select(EquipmentConfig)
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 获取总数
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        offset = (page - 1) * page_size
        equipment_configs_query = (
            base_query
            .order_by(EquipmentConfig.created_at.desc())
            .offset(offset)
            .limit(page_size)
        )
        
        equipment_configs_result = await self.db_session.execute(equipment_configs_query)
        equipment_configs = equipment_configs_result.scalars().all()
        
        # 转换为响应模式
        equipment_config_list = [
            EquipmentResponseSchema.model_validate(equipment_config)
            for equipment_config in equipment_configs
        ]
        
        # 计算总页数
        total_pages = (total + page_size - 1) // page_size
        
        return EquipmentListResponseSchema(
            equipment_configs=equipment_config_list,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
    
    async def update_equipment_config(
        self,
        equipment_id: str,
        update_data: EquipmentUpdateSchema
    ) -> Optional[EquipmentResponseSchema]:
        """
        更新设备配置信息
        
        Args:
            equipment_id: 设备配置ID
            update_data: 更新数据
            
        Returns:
            Optional[EquipmentResponseSchema]: 更新后的设备配置信息，不存在时返回None
        """
        # 查询设备配置
        query = select(EquipmentConfig).where(EquipmentConfig.id == equipment_id)
        result = await self.db_session.execute(query)
        equipment_config = result.scalar_one_or_none()
        
        if equipment_config is None:
            return None
        
        # 更新字段
        update_dict = update_data.model_dump(exclude_unset=True)
        
        for field, value in update_dict.items():
            if hasattr(equipment_config, field):
                setattr(equipment_config, field, value)
        
        # 保存更改
        await self.db_session.commit()
        await self.db_session.refresh(equipment_config)
        
        return EquipmentResponseSchema.model_validate(equipment_config)
    
    async def delete_equipment_config(self, equipment_id: str) -> bool:
        """
        删除设备配置
        
        Args:
            equipment_id: 设备配置ID
            
        Returns:
            bool: 删除成功返回True，设备配置不存在返回False
        """
        # 查询设备配置
        query = select(EquipmentConfig).where(EquipmentConfig.id == equipment_id)
        result = await self.db_session.execute(query)
        equipment_config = result.scalar_one_or_none()
        
        if equipment_config is None:
            return False
        
        # 删除设备配置
        await self.db_session.delete(equipment_config)
        await self.db_session.commit()
        
        return True
    
    async def validate_equipment_parameters(
        self,
        equipment_data: EquipmentCreateSchema
    ) -> List[str]:
        """
        验证设备配置参数的合理性
        
        Args:
            equipment_data: 设备配置数据
            
        Returns:
            List[str]: 验证错误信息列表，空列表表示验证通过
        """
        errors = []
        
        # 验证装卸速度的合理性
        if equipment_data.loading_speed <= 0:
            errors.append("装载速度必须大于0")
        
        if equipment_data.unloading_speed <= 0:
            errors.append("卸载速度必须大于0")
        
        # 验证载重能力和速度的匹配性
        if equipment_data.max_load_capacity < 1000:  # 小于1吨
            if equipment_data.loading_speed > 10000:  # 装载速度大于10吨/小时
                errors.append("小型设备的装载速度设置过高")
        
        # 验证效率系数
        if not (0 <= equipment_data.efficiency_factor <= 1):
            errors.append("效率系数必须在0-1之间")
        
        # 验证维护间隔
        if equipment_data.maintenance_interval < 24:  # 少于24小时
            errors.append("维护间隔不能少于24小时")
        
        # 验证作业半径
        if equipment_data.operation_radius <= 0:
            errors.append("作业半径必须大于0")
        
        return errors
