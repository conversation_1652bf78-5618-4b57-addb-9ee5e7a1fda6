"""
运输车辆配置服务层

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含运输车辆配置相关的业务逻辑
"""

import math
from typing import List, Optional
from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.models.vehicle import VehicleConfig, VehicleTypeEnum, VehicleStatusEnum
from src.schemas.vehicle import (
    VehicleCreateSchema,
    VehicleUpdateSchema,
    VehicleResponseSchema,
    VehicleListResponseSchema
)


class VehicleService:
    """
    运输车辆配置服务类
    
    提供运输车辆配置的完整业务逻辑，包括：
    - CRUD操作
    - 参数验证
    - 业务规则检查
    """
    
    def __init__(self, db_session: AsyncSession):
        """
        初始化运输车辆配置服务
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def create_vehicle_config(
        self,
        vehicle_data: VehicleCreateSchema
    ) -> VehicleResponseSchema:
        """
        创建运输车辆配置
        
        Args:
            vehicle_data: 运输车辆配置创建数据
            
        Returns:
            VehicleResponseSchema: 创建的运输车辆配置信息
        """
        # 创建运输车辆配置实例
        vehicle_config = VehicleConfig(**vehicle_data.model_dump())
        
        # 保存到数据库
        self.db_session.add(vehicle_config)
        await self.db_session.commit()
        await self.db_session.refresh(vehicle_config)
        
        return VehicleResponseSchema.model_validate(vehicle_config)
    
    async def get_vehicle_config_by_id(self, vehicle_id: str) -> Optional[VehicleResponseSchema]:
        """
        根据ID获取运输车辆配置
        
        Args:
            vehicle_id: 运输车辆配置ID
            
        Returns:
            Optional[VehicleResponseSchema]: 运输车辆配置信息，不存在时返回None
        """
        query = select(VehicleConfig).where(VehicleConfig.id == vehicle_id)
        result = await self.db_session.execute(query)
        vehicle_config = result.scalar_one_or_none()
        
        if vehicle_config is None:
            return None
        
        return VehicleResponseSchema.model_validate(vehicle_config)
    
    async def get_vehicle_config_list(
        self,
        page: int = 1,
        page_size: int = 20,
        vehicle_type: Optional[VehicleTypeEnum] = None,
        status: Optional[VehicleStatusEnum] = None,
        search_keyword: Optional[str] = None
    ) -> VehicleListResponseSchema:
        """
        获取运输车辆配置列表
        
        Args:
            page: 页码
            page_size: 每页数量
            vehicle_type: 车辆类型筛选
            status: 状态筛选
            search_keyword: 搜索关键词
            
        Returns:
            VehicleListResponseSchema: 运输车辆配置列表响应
        """
        # 构建查询条件
        conditions = []
        
        if vehicle_type:
            conditions.append(VehicleConfig.vehicle_type == vehicle_type)
        
        if status:
            conditions.append(VehicleConfig.operational_status == status)
        
        if search_keyword:
            search_condition = or_(
                VehicleConfig.vehicle_model.ilike(f"%{search_keyword}%"),
                VehicleConfig.remarks.ilike(f"%{search_keyword}%")
            )
            conditions.append(search_condition)
        
        # 构建基础查询
        base_query = select(VehicleConfig)
        if conditions:
            base_query = base_query.where(and_(*conditions))
        
        # 获取总数
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await self.db_session.execute(count_query)
        total = total_result.scalar()
        
        # 分页查询
        offset = (page - 1) * page_size
        query = base_query.offset(offset).limit(page_size)
        result = await self.db_session.execute(query)
        vehicle_configs = result.scalars().all()
        
        # 转换为响应格式
        vehicle_list = [
            VehicleResponseSchema.model_validate(config)
            for config in vehicle_configs
        ]
        
        # 计算总页数
        total_pages = math.ceil(total / page_size) if total > 0 else 1
        
        return VehicleListResponseSchema(
            vehicle_configs=vehicle_list,
            total=total,
            page=page,
            page_size=page_size,
            total_pages=total_pages
        )
    
    async def update_vehicle_config(
        self,
        vehicle_id: str,
        update_data: VehicleUpdateSchema
    ) -> Optional[VehicleResponseSchema]:
        """
        更新运输车辆配置
        
        Args:
            vehicle_id: 运输车辆配置ID
            update_data: 更新数据
            
        Returns:
            Optional[VehicleResponseSchema]: 更新后的运输车辆配置信息，不存在时返回None
        """
        # 查询运输车辆配置
        query = select(VehicleConfig).where(VehicleConfig.id == vehicle_id)
        result = await self.db_session.execute(query)
        vehicle_config = result.scalar_one_or_none()
        
        if vehicle_config is None:
            return None
        
        # 更新字段
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(vehicle_config, field, value)
        
        # 保存更改
        await self.db_session.commit()
        await self.db_session.refresh(vehicle_config)
        
        return VehicleResponseSchema.model_validate(vehicle_config)
    
    async def delete_vehicle_config(self, vehicle_id: str) -> bool:
        """
        删除运输车辆配置
        
        Args:
            vehicle_id: 运输车辆配置ID
            
        Returns:
            bool: 删除成功返回True，运输车辆配置不存在返回False
        """
        # 查询运输车辆配置
        query = select(VehicleConfig).where(VehicleConfig.id == vehicle_id)
        result = await self.db_session.execute(query)
        vehicle_config = result.scalar_one_or_none()
        
        if vehicle_config is None:
            return False
        
        # 删除运输车辆配置
        await self.db_session.delete(vehicle_config)
        await self.db_session.commit()
        
        return True
    
    async def validate_vehicle_parameters(
        self,
        vehicle_data: VehicleCreateSchema | VehicleUpdateSchema
    ) -> List[str]:
        """
        验证运输车辆配置参数
        
        Args:
            vehicle_data: 运输车辆配置数据
            
        Returns:
            List[str]: 验证错误列表，无错误时返回空列表
        """
        errors = []
        
        # 获取数据字典
        if isinstance(vehicle_data, VehicleUpdateSchema):
            data_dict = vehicle_data.model_dump(exclude_unset=True)
        else:
            data_dict = vehicle_data.model_dump()
        
        # 验证载重和容积的匹配性
        max_payload = data_dict.get('max_payload')
        cargo_volume = data_dict.get('cargo_volume')
        
        if max_payload and cargo_volume:
            # 计算载重密度（kg/m³）
            density = max_payload / cargo_volume
            if density > 2000:  # 密度过高，可能不合理
                errors.append("载重与容积比例不合理，密度过高")
            elif density < 50:  # 密度过低，可能浪费空间
                errors.append("载重与容积比例不合理，密度过低")
        
        # 验证速度和燃油消耗的合理性
        max_speed = data_dict.get('max_speed')
        fuel_consumption = data_dict.get('fuel_consumption')
        
        if max_speed and fuel_consumption:
            if max_speed > 120 and fuel_consumption < 20:
                errors.append("高速车辆的燃油消耗设置过低")
            elif max_speed < 30 and fuel_consumption > 50:
                errors.append("低速车辆的燃油消耗设置过高")
        
        # 验证装卸时间的合理性
        loading_time = data_dict.get('loading_time')
        unloading_time = data_dict.get('unloading_time')
        
        if loading_time and loading_time < 5:
            errors.append("装载时间不能少于5分钟")
        
        if unloading_time and unloading_time < 3:
            errors.append("卸载时间不能少于3分钟")
        
        # 验证维护周期的合理性
        maintenance_cycle = data_dict.get('maintenance_cycle')
        if maintenance_cycle and maintenance_cycle < 100:
            errors.append("维护周期不能少于100小时")
        
        # 验证可用率的合理性
        availability_rate = data_dict.get('availability_rate')
        if availability_rate and availability_rate < 0.5:
            errors.append("可用率不能低于50%")
        
        return errors
