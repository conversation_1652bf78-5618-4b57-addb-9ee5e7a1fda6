"""
效能指标管理服务

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 函数参数不超过5个
- 使用描述性的函数命名
- 模块职责单一性：只包含效能指标管理相关的业务逻辑
"""

from typing import Optional, List, Dict, Any
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_
from sqlalchemy.orm import selectinload
from src.database.models.efficiency import (
    EfficiencyIndicator,
    IndicatorCalculationResult,
    IndicatorWeightConfig,
    IndicatorCategoryEnum,
    IndicatorStatusEnum,
    CalculationStatusEnum
)
from src.schemas.efficiency import (
    EfficiencyIndicatorCreateSchema,
    EfficiencyIndicatorUpdateSchema,
    EfficiencyIndicatorResponseSchema,
    IndicatorCalculationRequestSchema,
    IndicatorCalculationResultResponseSchema,
    IndicatorWeightConfigCreateSchema,
    IndicatorWeightConfigResponseSchema
)
from src.services.efficiency_calculator import (
    TimelinessCalculator,
    CapabilityCalculator,
    EconomyCalculator,
    RobustnessCalculator,
    SafetyCalculator
)


class EfficiencyIndicatorService:
    """
    效能指标管理服务类
    
    提供效能指标的CRUD操作和业务逻辑处理：
    - 创建、查询、更新、删除效能指标
    - 指标列表查询和筛选
    - 指标参数验证和处理
    """
    
    def __init__(self, db_session: AsyncSession) -> None:
        """
        初始化效能指标服务
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def create_indicator(self, indicator_data: EfficiencyIndicatorCreateSchema) -> EfficiencyIndicatorResponseSchema:
        """
        创建新的效能指标
        
        Args:
            indicator_data: 指标创建数据
            
        Returns:
            EfficiencyIndicatorResponseSchema: 创建的指标信息
            
        Raises:
            ValueError: 当指标名称或编码已存在时
        """
        # 检查指标名称和编码是否已存在
        existing_indicator = await self._check_indicator_exists(
            indicator_data.indicator_name, 
            indicator_data.indicator_code
        )
        if existing_indicator:
            raise ValueError("指标名称或编码已存在")
        
        # 创建新指标
        new_indicator = EfficiencyIndicator(**indicator_data.model_dump())
        self.db_session.add(new_indicator)
        await self.db_session.commit()
        await self.db_session.refresh(new_indicator)
        
        return EfficiencyIndicatorResponseSchema.model_validate(new_indicator)
    
    async def get_indicator_by_id(self, indicator_id: str) -> Optional[EfficiencyIndicatorResponseSchema]:
        """
        根据ID获取效能指标
        
        Args:
            indicator_id: 指标ID
            
        Returns:
            Optional[EfficiencyIndicatorResponseSchema]: 指标信息，不存在时返回None
        """
        stmt = select(EfficiencyIndicator).where(EfficiencyIndicator.id == indicator_id)
        result = await self.db_session.execute(stmt)
        indicator = result.scalar_one_or_none()
        
        if indicator:
            return EfficiencyIndicatorResponseSchema.model_validate(indicator)
        return None
    
    async def get_indicators_by_category(self, category: IndicatorCategoryEnum) -> List[EfficiencyIndicatorResponseSchema]:
        """
        根据分类获取效能指标列表
        
        Args:
            category: 指标分类
            
        Returns:
            List[EfficiencyIndicatorResponseSchema]: 指标列表
        """
        stmt = select(EfficiencyIndicator).where(
            and_(
                EfficiencyIndicator.category == category,
                EfficiencyIndicator.status == IndicatorStatusEnum.ACTIVE
            )
        ).order_by(EfficiencyIndicator.priority_level.desc())
        
        result = await self.db_session.execute(stmt)
        indicators = result.scalars().all()
        
        return [EfficiencyIndicatorResponseSchema.model_validate(indicator) for indicator in indicators]
    
    async def get_all_active_indicators(self) -> List[EfficiencyIndicatorResponseSchema]:
        """
        获取所有激活的效能指标
        
        Returns:
            List[EfficiencyIndicatorResponseSchema]: 激活的指标列表
        """
        stmt = select(EfficiencyIndicator).where(
            EfficiencyIndicator.status == IndicatorStatusEnum.ACTIVE
        ).order_by(EfficiencyIndicator.category, EfficiencyIndicator.priority_level.desc())
        
        result = await self.db_session.execute(stmt)
        indicators = result.scalars().all()
        
        return [EfficiencyIndicatorResponseSchema.model_validate(indicator) for indicator in indicators]
    
    async def update_indicator(self, indicator_id: str, update_data: EfficiencyIndicatorUpdateSchema) -> Optional[EfficiencyIndicatorResponseSchema]:
        """
        更新效能指标
        
        Args:
            indicator_id: 指标ID
            update_data: 更新数据
            
        Returns:
            Optional[EfficiencyIndicatorResponseSchema]: 更新后的指标信息，不存在时返回None
        """
        stmt = select(EfficiencyIndicator).where(EfficiencyIndicator.id == indicator_id)
        result = await self.db_session.execute(stmt)
        indicator = result.scalar_one_or_none()
        
        if not indicator:
            return None
        
        # 更新指标信息
        update_dict = update_data.model_dump(exclude_unset=True)
        for field, value in update_dict.items():
            setattr(indicator, field, value)
        
        await self.db_session.commit()
        await self.db_session.refresh(indicator)
        
        return EfficiencyIndicatorResponseSchema.model_validate(indicator)
    
    async def delete_indicator(self, indicator_id: str) -> bool:
        """
        删除效能指标（软删除，设置状态为已弃用）
        
        Args:
            indicator_id: 指标ID
            
        Returns:
            bool: 删除成功返回True，指标不存在返回False
        """
        stmt = select(EfficiencyIndicator).where(EfficiencyIndicator.id == indicator_id)
        result = await self.db_session.execute(stmt)
        indicator = result.scalar_one_or_none()
        
        if not indicator:
            return False
        
        indicator.status = IndicatorStatusEnum.DEPRECATED
        await self.db_session.commit()
        
        return True
    
    async def _check_indicator_exists(self, name: str, code: str) -> bool:
        """
        检查指标名称或编码是否已存在
        
        Args:
            name: 指标名称
            code: 指标编码
            
        Returns:
            bool: 存在返回True，不存在返回False
        """
        stmt = select(EfficiencyIndicator).where(
            or_(
                EfficiencyIndicator.indicator_name == name,
                EfficiencyIndicator.indicator_code == code
            )
        )
        result = await self.db_session.execute(stmt)
        existing_indicator = result.scalar_one_or_none()
        
        return existing_indicator is not None


class IndicatorCalculationService:
    """
    指标计算服务类
    
    提供指标计算相关的业务逻辑处理：
    - 启动指标计算任务
    - 查询计算结果
    - 管理计算状态
    """
    
    def __init__(self, db_session: AsyncSession) -> None:
        """
        初始化指标计算服务
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def create_calculation_result(self, scenario_id: str, indicator_id: str, 
                                      calculated_value: Decimal, calculation_method: str,
                                      input_parameters: Dict[str, Any]) -> IndicatorCalculationResultResponseSchema:
        """
        创建指标计算结果
        
        Args:
            scenario_id: 场景ID
            indicator_id: 指标ID
            calculated_value: 计算值
            calculation_method: 计算方法
            input_parameters: 输入参数
            
        Returns:
            IndicatorCalculationResultResponseSchema: 计算结果信息
        """
        calculation_result = IndicatorCalculationResult(
            scenario_id=scenario_id,
            indicator_id=indicator_id,
            calculated_value=calculated_value,
            calculation_method=calculation_method,
            calculation_status=CalculationStatusEnum.COMPLETED,
            input_parameters=input_parameters,
            calculation_metadata={}
        )
        
        self.db_session.add(calculation_result)
        await self.db_session.commit()
        await self.db_session.refresh(calculation_result)
        
        return IndicatorCalculationResultResponseSchema.model_validate(calculation_result)
    
    async def get_calculation_results_by_scenario(self, scenario_id: str) -> List[IndicatorCalculationResultResponseSchema]:
        """
        根据场景ID获取计算结果列表

        Args:
            scenario_id: 场景ID

        Returns:
            List[IndicatorCalculationResultResponseSchema]: 计算结果列表
        """
        stmt = select(IndicatorCalculationResult).where(
            IndicatorCalculationResult.scenario_id == scenario_id
        ).order_by(IndicatorCalculationResult.created_at.desc())

        result = await self.db_session.execute(stmt)
        calculation_results = result.scalars().all()

        return [IndicatorCalculationResultResponseSchema.model_validate(result) for result in calculation_results]

    def calculate_indicator_by_category(self, category: IndicatorCategoryEnum,
                                      indicator_code: str, input_data: Dict[str, Any]) -> Decimal:
        """
        根据指标分类和编码计算指标值

        Args:
            category: 指标分类
            indicator_code: 指标编码
            input_data: 输入数据

        Returns:
            Decimal: 计算结果

        Raises:
            ValueError: 当指标编码不支持或输入数据不足时
        """
        if category == IndicatorCategoryEnum.TIMELINESS:
            return self._calculate_timeliness_indicator(indicator_code, input_data)
        elif category == IndicatorCategoryEnum.CAPABILITY:
            return self._calculate_capability_indicator(indicator_code, input_data)
        elif category == IndicatorCategoryEnum.ECONOMY:
            return self._calculate_economy_indicator(indicator_code, input_data)
        elif category == IndicatorCategoryEnum.ROBUSTNESS:
            return self._calculate_robustness_indicator(indicator_code, input_data)
        elif category == IndicatorCategoryEnum.SAFETY:
            return self._calculate_safety_indicator(indicator_code, input_data)
        else:
            raise ValueError(f"不支持的指标分类: {category}")

    def _calculate_timeliness_indicator(self, indicator_code: str, input_data: Dict[str, Any]) -> Decimal:
        """计算时效性指标"""
        if indicator_code == "TASK_COMPLETION_TIME":
            return TimelinessCalculator.calculate_task_completion_time(
                input_data["start_time"], input_data["end_time"]
            )
        elif indicator_code == "AVERAGE_RESPONSE_TIME":
            return TimelinessCalculator.calculate_average_response_time(
                input_data["response_times"]
            )
        elif indicator_code == "ON_TIME_DELIVERY_RATE":
            return TimelinessCalculator.calculate_on_time_delivery_rate(
                input_data["total_tasks"], input_data["on_time_tasks"]
            )
        elif indicator_code == "TRANSPORT_VOLUME_PER_HOUR":
            return TimelinessCalculator.calculate_transport_volume_per_hour(
                input_data["total_volume"], input_data["duration_hours"]
            )
        else:
            raise ValueError(f"不支持的时效性指标编码: {indicator_code}")

    def _calculate_capability_indicator(self, indicator_code: str, input_data: Dict[str, Any]) -> Decimal:
        """计算能力指标"""
        if indicator_code == "MAX_SUSTAINED_CAPACITY":
            return CapabilityCalculator.calculate_max_sustained_capacity(
                input_data["peak_capacity"], input_data["sustainability_factor"]
            )
        elif indicator_code == "MISSION_SUCCESS_RATE":
            return CapabilityCalculator.calculate_mission_success_rate(
                input_data["total_missions"], input_data["successful_missions"]
            )
        elif indicator_code == "AVAILABLE_SORTIE_RATE":
            return CapabilityCalculator.calculate_available_sortie_rate(
                input_data["total_sorties"], input_data["available_sorties"]
            )
        elif indicator_code == "EQUIPMENT_UTILIZATION_RATE":
            return CapabilityCalculator.calculate_equipment_utilization_rate(
                input_data["actual_usage_hours"], input_data["available_hours"]
            )
        else:
            raise ValueError(f"不支持的能力指标编码: {indicator_code}")

    def _calculate_economy_indicator(self, indicator_code: str, input_data: Dict[str, Any]) -> Decimal:
        """计算经济性指标"""
        if indicator_code == "TON_KM_COST":
            return EconomyCalculator.calculate_ton_km_cost(
                input_data["total_cost"], input_data["transport_volume"], input_data["distance"]
            )
        elif indicator_code == "RESOURCE_UTILIZATION_RATE":
            return EconomyCalculator.calculate_resource_utilization_rate(
                input_data["used_resources"], input_data["total_resources"]
            )
        elif indicator_code == "REDUNDANCY_RATIO":
            return EconomyCalculator.calculate_redundancy_ratio(
                input_data["reserved_resources"], input_data["required_resources"]
            )
        elif indicator_code == "FUEL_EFFICIENCY":
            return EconomyCalculator.calculate_fuel_efficiency(
                input_data["transport_volume"], input_data["fuel_consumption"]
            )
        else:
            raise ValueError(f"不支持的经济性指标编码: {indicator_code}")

    def _calculate_robustness_indicator(self, indicator_code: str, input_data: Dict[str, Any]) -> Decimal:
        """计算鲁棒性指标"""
        if indicator_code == "SCENARIO_ADAPTABILITY":
            return RobustnessCalculator.calculate_scenario_adaptability(
                input_data["performance_scores"]
            )
        elif indicator_code == "DAMAGE_RESISTANCE":
            return RobustnessCalculator.calculate_damage_resistance(
                input_data["operational_capacity_after_damage"], input_data["original_capacity"]
            )
        elif indicator_code == "RECOVERY_TIME":
            return RobustnessCalculator.calculate_recovery_time(
                input_data["failure_time"], input_data["recovery_time"]
            )
        elif indicator_code == "RISK_RESISTANCE":
            return RobustnessCalculator.calculate_risk_resistance(
                input_data["risk_mitigation_score"], input_data["risk_exposure_score"]
            )
        else:
            raise ValueError(f"不支持的鲁棒性指标编码: {indicator_code}")

    def _calculate_safety_indicator(self, indicator_code: str, input_data: Dict[str, Any]) -> Decimal:
        """计算安全性指标"""
        if indicator_code == "MISSION_RISK_PROBABILITY":
            return SafetyCalculator.calculate_mission_risk_probability(
                input_data["risk_events"], input_data["total_missions"]
            )
        elif indicator_code == "LOSS_RATE":
            return SafetyCalculator.calculate_loss_rate(
                input_data["lost_volume"], input_data["total_volume"]
            )
        elif indicator_code == "SAFETY_MARGIN":
            return SafetyCalculator.calculate_safety_margin(
                input_data["safety_measures_score"], input_data["minimum_required_score"]
            )
        elif indicator_code == "EMERGENCY_RESPONSE_CAPABILITY":
            return SafetyCalculator.calculate_emergency_response_capability(
                input_data["response_time"], input_data["standard_time"]
            )
        else:
            raise ValueError(f"不支持的安全性指标编码: {indicator_code}")


class IndicatorWeightConfigService:
    """
    指标权重配置服务类
    
    提供指标权重配置的管理功能：
    - 创建、查询、更新权重配置
    - 获取默认权重配置
    - 权重配置验证
    """
    
    def __init__(self, db_session: AsyncSession) -> None:
        """
        初始化权重配置服务
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def create_weight_config(self, config_data: IndicatorWeightConfigCreateSchema) -> IndicatorWeightConfigResponseSchema:
        """
        创建权重配置
        
        Args:
            config_data: 权重配置数据
            
        Returns:
            IndicatorWeightConfigResponseSchema: 创建的权重配置信息
        """
        # 验证权重总和是否为1
        total_weight = sum(config_data.weight_settings.values())
        if abs(total_weight - 1.0) > 0.001:  # 允许小的浮点误差
            raise ValueError("权重总和必须等于1.0")
        
        new_config = IndicatorWeightConfig(**config_data.model_dump())
        self.db_session.add(new_config)
        await self.db_session.commit()
        await self.db_session.refresh(new_config)
        
        return IndicatorWeightConfigResponseSchema.model_validate(new_config)
    
    async def get_default_weight_config(self, scenario_type: str) -> Optional[IndicatorWeightConfigResponseSchema]:
        """
        获取指定场景类型的默认权重配置
        
        Args:
            scenario_type: 场景类型
            
        Returns:
            Optional[IndicatorWeightConfigResponseSchema]: 默认权重配置，不存在时返回None
        """
        stmt = select(IndicatorWeightConfig).where(
            and_(
                IndicatorWeightConfig.scenario_type == scenario_type,
                IndicatorWeightConfig.is_default == True,
                IndicatorWeightConfig.is_active == True
            )
        )
        
        result = await self.db_session.execute(stmt)
        config = result.scalar_one_or_none()
        
        if config:
            return IndicatorWeightConfigResponseSchema.model_validate(config)
        return None
