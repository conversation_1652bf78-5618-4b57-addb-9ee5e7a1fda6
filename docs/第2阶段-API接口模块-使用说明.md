# 第2阶段 - API接口模块使用说明

## 模块概述

API接口模块提供了效能计算引擎的RESTful API接口，支持效能指标管理、计算结果查询、权重配置和综合评估等功能。所有接口都遵循REST规范，提供完整的CRUD操作和业务功能。

### API基础信息
- **基础路径**: `/api/v1/efficiency`
- **数据格式**: JSON
- **认证方式**: 根据项目配置
- **错误处理**: 统一的HTTP状态码和错误响应格式

## API端点详细说明

### 1. 效能指标管理接口

#### 创建效能指标
```http
POST /api/v1/efficiency/indicators
Content-Type: application/json

{
    "indicator_name": "示例指标",
    "indicator_code": "EXAMPLE_INDICATOR",
    "category": "timeliness",
    "description": "用于演示的效能指标",
    "unit": "小时",
    "min_threshold": 0,
    "max_threshold": 48,
    "target_value": 8,
    "default_weight": 0.25,
    "priority_level": 1,
    "created_by": "API用户"
}
```

**响应示例**:
```json
{
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "indicator_name": "示例指标",
    "indicator_code": "EXAMPLE_INDICATOR",
    "category": "timeliness",
    "description": "用于演示的效能指标",
    "unit": "小时",
    "min_threshold": 0,
    "max_threshold": 48,
    "target_value": 8,
    "default_weight": 0.25,
    "priority_level": 1,
    "status": "active",
    "created_by": "API用户",
    "metadata_info": {},
    "created_at": "2024-01-01T10:00:00Z",
    "updated_at": "2024-01-01T10:00:00Z"
}
```

#### 获取效能指标详情
```http
GET /api/v1/efficiency/indicators/{indicator_id}
```

#### 获取效能指标列表
```http
GET /api/v1/efficiency/indicators?category=timeliness
```

**查询参数**:
- `category` (可选): 指标分类筛选，可选值: `timeliness`, `capability`, `economy`, `robustness`, `safety`

#### 更新效能指标
```http
PUT /api/v1/efficiency/indicators/{indicator_id}
Content-Type: application/json

{
    "description": "更新后的指标描述",
    "target_value": 6,
    "priority_level": 2
}
```

#### 删除效能指标
```http
DELETE /api/v1/efficiency/indicators/{indicator_id}
```

### 2. 计算结果查询接口

#### 获取场景计算结果
```http
GET /api/v1/efficiency/calculations/scenarios/{scenario_id}/results
```

**响应示例**:
```json
[
    {
        "id": "result_id_1",
        "scenario_id": "scenario_id",
        "indicator_id": "indicator_id_1",
        "calculated_value": 85.50,
        "confidence_level": 0.95,
        "calculation_method": "analytical_model",
        "calculation_status": "completed",
        "input_parameters": {
            "start_time": "2024-01-01T09:00:00Z",
            "end_time": "2024-01-01T17:30:00Z"
        },
        "calculation_metadata": {
            "execution_time_ms": 150,
            "algorithm_version": "1.0"
        },
        "error_message": null,
        "created_at": "2024-01-01T10:00:00Z",
        "updated_at": "2024-01-01T10:00:00Z"
    }
]
```

### 3. 权重配置管理接口

#### 创建权重配置
```http
POST /api/v1/efficiency/weight-configs
Content-Type: application/json

{
    "config_name": "人员投送权重配置",
    "scenario_type": "personnel_transport",
    "description": "人员投送场景的效能指标权重配置",
    "weight_settings": {
        "timeliness": 0.35,
        "capability": 0.25,
        "economy": 0.15,
        "robustness": 0.15,
        "safety": 0.10
    },
    "is_default": true,
    "created_by": "配置管理员"
}
```

#### 获取默认权重配置
```http
GET /api/v1/efficiency/weight-configs/default/{scenario_type}
```

### 4. 综合评估接口

#### 计算综合效能评分
```http
GET /api/v1/efficiency/evaluations/scenarios/{scenario_id}/comprehensive-score?scenario_type=personnel_transport
```

**响应示例**:
```json
{
    "timeliness": 85.50,
    "capability": 90.25,
    "economy": 78.80,
    "robustness": 82.15,
    "safety": 95.00,
    "total": 86.34
}
```

## 完整的API使用示例

### Python客户端示例

```python
import asyncio
import aiohttp
import json
from typing import Dict, List, Optional

class EfficiencyAPIClient:
    """效能指标API客户端"""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化API客户端
        
        Args:
            base_url: API服务器基础URL
        """
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1/efficiency"
    
    async def create_indicator(self, indicator_data: Dict) -> Dict:
        """
        创建效能指标
        
        Args:
            indicator_data: 指标数据
            
        Returns:
            Dict: 创建的指标信息
        """
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.api_base}/indicators",
                json=indicator_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 201:
                    return await response.json()
                else:
                    error_detail = await response.text()
                    raise Exception(f"创建指标失败: {response.status} - {error_detail}")
    
    async def get_indicator(self, indicator_id: str) -> Dict:
        """
        获取效能指标详情
        
        Args:
            indicator_id: 指标ID
            
        Returns:
            Dict: 指标详细信息
        """
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{self.api_base}/indicators/{indicator_id}") as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 404:
                    raise Exception("指标不存在")
                else:
                    error_detail = await response.text()
                    raise Exception(f"获取指标失败: {response.status} - {error_detail}")
    
    async def get_indicators(self, category: Optional[str] = None) -> List[Dict]:
        """
        获取效能指标列表
        
        Args:
            category: 指标分类筛选
            
        Returns:
            List[Dict]: 指标列表
        """
        url = f"{self.api_base}/indicators"
        if category:
            url += f"?category={category}"
        
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_detail = await response.text()
                    raise Exception(f"获取指标列表失败: {response.status} - {error_detail}")
    
    async def update_indicator(self, indicator_id: str, update_data: Dict) -> Dict:
        """
        更新效能指标
        
        Args:
            indicator_id: 指标ID
            update_data: 更新数据
            
        Returns:
            Dict: 更新后的指标信息
        """
        async with aiohttp.ClientSession() as session:
            async with session.put(
                f"{self.api_base}/indicators/{indicator_id}",
                json=update_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 404:
                    raise Exception("指标不存在")
                else:
                    error_detail = await response.text()
                    raise Exception(f"更新指标失败: {response.status} - {error_detail}")
    
    async def delete_indicator(self, indicator_id: str) -> bool:
        """
        删除效能指标
        
        Args:
            indicator_id: 指标ID
            
        Returns:
            bool: 删除是否成功
        """
        async with aiohttp.ClientSession() as session:
            async with session.delete(f"{self.api_base}/indicators/{indicator_id}") as response:
                if response.status == 204:
                    return True
                elif response.status == 404:
                    raise Exception("指标不存在")
                else:
                    error_detail = await response.text()
                    raise Exception(f"删除指标失败: {response.status} - {error_detail}")
    
    async def get_calculation_results(self, scenario_id: str) -> List[Dict]:
        """
        获取场景计算结果
        
        Args:
            scenario_id: 场景ID
            
        Returns:
            List[Dict]: 计算结果列表
        """
        async with aiohttp.ClientSession() as session:
            url = f"{self.api_base}/calculations/scenarios/{scenario_id}/results"
            async with session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_detail = await response.text()
                    raise Exception(f"获取计算结果失败: {response.status} - {error_detail}")
    
    async def create_weight_config(self, config_data: Dict) -> Dict:
        """
        创建权重配置
        
        Args:
            config_data: 权重配置数据
            
        Returns:
            Dict: 创建的权重配置信息
        """
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.api_base}/weight-configs",
                json=config_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                if response.status == 201:
                    return await response.json()
                else:
                    error_detail = await response.text()
                    raise Exception(f"创建权重配置失败: {response.status} - {error_detail}")
    
    async def get_default_weight_config(self, scenario_type: str) -> Dict:
        """
        获取默认权重配置
        
        Args:
            scenario_type: 场景类型
            
        Returns:
            Dict: 默认权重配置信息
        """
        async with aiohttp.ClientSession() as session:
            url = f"{self.api_base}/weight-configs/default/{scenario_type}"
            async with session.get(url) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 404:
                    raise Exception(f"未找到场景类型 {scenario_type} 的默认权重配置")
                else:
                    error_detail = await response.text()
                    raise Exception(f"获取权重配置失败: {response.status} - {error_detail}")
    
    async def calculate_comprehensive_score(self, scenario_id: str, scenario_type: str) -> Dict:
        """
        计算综合效能评分
        
        Args:
            scenario_id: 场景ID
            scenario_type: 场景类型
            
        Returns:
            Dict: 综合评分结果
        """
        async with aiohttp.ClientSession() as session:
            url = f"{self.api_base}/evaluations/scenarios/{scenario_id}/comprehensive-score"
            params = {"scenario_type": scenario_type}
            
            async with session.get(url, params=params) as response:
                if response.status == 200:
                    return await response.json()
                elif response.status == 404:
                    raise Exception("场景或权重配置不存在")
                else:
                    error_detail = await response.text()
                    raise Exception(f"计算综合评分失败: {response.status} - {error_detail}")


async def api_client_example():
    """API客户端使用示例"""
    
    # 初始化客户端
    client = EfficiencyAPIClient("http://localhost:8000")
    
    try:
        print("=== 效能指标API使用示例 ===")
        
        # 1. 创建效能指标
        print("\n1. 创建效能指标")
        indicator_data = {
            "indicator_name": "API测试指标",
            "indicator_code": "API_TEST_INDICATOR",
            "category": "timeliness",
            "description": "通过API创建的测试指标",
            "unit": "小时",
            "min_threshold": 0,
            "max_threshold": 24,
            "target_value": 8,
            "default_weight": 0.25,
            "priority_level": 1,
            "created_by": "API客户端"
        }
        
        created_indicator = await client.create_indicator(indicator_data)
        print(f"创建指标成功: {created_indicator['indicator_name']}")
        print(f"指标ID: {created_indicator['id']}")
        
        indicator_id = created_indicator['id']
        
        # 2. 获取指标详情
        print("\n2. 获取指标详情")
        indicator_detail = await client.get_indicator(indicator_id)
        print(f"指标名称: {indicator_detail['indicator_name']}")
        print(f"指标分类: {indicator_detail['category']}")
        print(f"创建时间: {indicator_detail['created_at']}")
        
        # 3. 获取指标列表
        print("\n3. 获取指标列表")
        all_indicators = await client.get_indicators()
        print(f"总指标数量: {len(all_indicators)}")
        
        # 按分类获取
        timeliness_indicators = await client.get_indicators(category="timeliness")
        print(f"时效性指标数量: {len(timeliness_indicators)}")
        
        # 4. 更新指标
        print("\n4. 更新指标")
        update_data = {
            "description": "通过API更新的指标描述",
            "target_value": 6,
            "priority_level": 2
        }
        
        updated_indicator = await client.update_indicator(indicator_id, update_data)
        print(f"更新成功: {updated_indicator['description']}")
        print(f"新目标值: {updated_indicator['target_value']}")
        
        # 5. 创建权重配置
        print("\n5. 创建权重配置")
        weight_config_data = {
            "config_name": "API测试权重配置",
            "scenario_type": "api_test_scenario",
            "description": "通过API创建的测试权重配置",
            "weight_settings": {
                "timeliness": 0.30,
                "capability": 0.25,
                "economy": 0.20,
                "robustness": 0.15,
                "safety": 0.10
            },
            "is_default": True,
            "created_by": "API客户端"
        }
        
        weight_config = await client.create_weight_config(weight_config_data)
        print(f"权重配置创建成功: {weight_config['config_name']}")
        
        # 6. 获取默认权重配置
        print("\n6. 获取默认权重配置")
        default_config = await client.get_default_weight_config("api_test_scenario")
        print(f"默认配置: {default_config['config_name']}")
        print(f"权重设置: {default_config['weight_settings']}")
        
        # 7. 删除指标
        print("\n7. 删除指标")
        delete_success = await client.delete_indicator(indicator_id)
        if delete_success:
            print("指标删除成功")
        
        print("\n=== API测试完成 ===")
        
    except Exception as e:
        print(f"API调用失败: {e}")


# 运行示例
if __name__ == "__main__":
    asyncio.run(api_client_example())
```

### JavaScript/Node.js客户端示例

```javascript
const axios = require('axios');

class EfficiencyAPIClient {
    constructor(baseUrl = 'http://localhost:8000') {
        this.baseUrl = baseUrl;
        this.apiBase = `${baseUrl}/api/v1/efficiency`;
    }

    async createIndicator(indicatorData) {
        try {
            const response = await axios.post(`${this.apiBase}/indicators`, indicatorData);
            return response.data;
        } catch (error) {
            throw new Error(`创建指标失败: ${error.response?.status} - ${error.response?.data}`);
        }
    }

    async getIndicator(indicatorId) {
        try {
            const response = await axios.get(`${this.apiBase}/indicators/${indicatorId}`);
            return response.data;
        } catch (error) {
            if (error.response?.status === 404) {
                throw new Error('指标不存在');
            }
            throw new Error(`获取指标失败: ${error.response?.status} - ${error.response?.data}`);
        }
    }

    async getIndicators(category = null) {
        try {
            let url = `${this.apiBase}/indicators`;
            if (category) {
                url += `?category=${category}`;
            }
            const response = await axios.get(url);
            return response.data;
        } catch (error) {
            throw new Error(`获取指标列表失败: ${error.response?.status} - ${error.response?.data}`);
        }
    }

    async calculateComprehensiveScore(scenarioId, scenarioType) {
        try {
            const response = await axios.get(
                `${this.apiBase}/evaluations/scenarios/${scenarioId}/comprehensive-score`,
                { params: { scenario_type: scenarioType } }
            );
            return response.data;
        } catch (error) {
            throw new Error(`计算综合评分失败: ${error.response?.status} - ${error.response?.data}`);
        }
    }
}

// 使用示例
async function jsClientExample() {
    const client = new EfficiencyAPIClient();
    
    try {
        console.log('=== JavaScript API客户端示例 ===');
        
        // 创建指标
        const indicatorData = {
            indicator_name: 'JS测试指标',
            indicator_code: 'JS_TEST_INDICATOR',
            category: 'capability',
            description: '通过JavaScript客户端创建的测试指标',
            unit: '百分比',
            target_value: 90,
            default_weight: 0.3
        };
        
        const createdIndicator = await client.createIndicator(indicatorData);
        console.log('创建指标成功:', createdIndicator.indicator_name);
        
        // 获取指标列表
        const indicators = await client.getIndicators('capability');
        console.log('能力指标数量:', indicators.length);
        
    } catch (error) {
        console.error('API调用失败:', error.message);
    }
}

// 运行示例
jsClientExample();
```

## 错误处理和状态码

### HTTP状态码说明
- `200 OK`: 请求成功
- `201 Created`: 资源创建成功
- `204 No Content`: 删除成功
- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 资源不存在
- `422 Unprocessable Entity`: 数据验证失败
- `500 Internal Server Error`: 服务器内部错误

### 错误响应格式
```json
{
    "detail": "错误详细信息",
    "error_code": "VALIDATION_ERROR",
    "timestamp": "2024-01-01T10:00:00Z"
}
```

## 常见问题和故障排除

### Q1: API服务器连接失败
**解决方案**: 
1. 检查服务器是否正在运行
2. 验证API基础URL是否正确
3. 检查网络连接和防火墙设置

### Q2: 权重验证失败
**解决方案**: 确保权重值总和等于1.0，检查数据类型是否为数值。

### Q3: 指标创建时编码重复
**解决方案**: 使用唯一的指标编码，检查数据库中是否已存在相同编码。

### Q4: 综合评分计算失败
**解决方案**: 
1. 确保场景存在且有计算结果
2. 验证场景类型的权重配置是否存在
3. 检查权重配置是否为默认且激活状态
