# 第2阶段 - 综合评估和权重配置模块使用说明

## 模块概述

综合评估和权重配置模块提供了效能指标的权重管理和综合评估功能。该模块支持不同场景类型的权重配置，并能够基于权重计算综合效能评分，支持多场景比较和性能分析。

### 主要功能
- 指标权重配置管理
- 综合效能评分计算
- 多场景比较分析
- 性能分析和排名

## 核心类详细说明

### 1. IndicatorWeightConfigService - 权重配置服务

#### 类的用途和职责
`IndicatorWeightConfigService` 负责管理效能指标的权重配置，支持不同场景类型的权重设定和查询。

#### 构造函数
```python
def __init__(self, db_session: AsyncSession) -> None:
    """
    初始化权重配置服务
    
    Args:
        db_session: 数据库会话，用于执行数据库操作
    """
```

#### 主要方法说明

##### create_weight_config()
```python
async def create_weight_config(self, config_data: IndicatorWeightConfigCreateSchema) -> IndicatorWeightConfigResponseSchema:
    """
    创建权重配置
    
    Args:
        config_data: 权重配置数据，包含各指标的权重分配
        
    Returns:
        IndicatorWeightConfigResponseSchema: 创建的权重配置信息
        
    Raises:
        ValueError: 当权重总和不等于1.0时抛出异常
    """
```

##### get_default_weight_config()
```python
async def get_default_weight_config(self, scenario_type: str) -> Optional[IndicatorWeightConfigResponseSchema]:
    """
    获取指定场景类型的默认权重配置
    
    Args:
        scenario_type: 场景类型
        
    Returns:
        Optional[IndicatorWeightConfigResponseSchema]: 默认权重配置，不存在时返回None
    """
```

#### 完整使用示例

```python
import asyncio
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session
from src.services.efficiency import IndicatorWeightConfigService
from src.schemas.efficiency import IndicatorWeightConfigCreateSchema

async def weight_config_service_example():
    """权重配置服务使用示例"""
    
    # 获取数据库会话
    async for db_session in get_database_session():
        try:
            # 初始化服务
            service = IndicatorWeightConfigService(db_session)
            
            # 1. 创建权重配置
            print("=== 创建权重配置 ===")
            config_data = IndicatorWeightConfigCreateSchema(
                config_name="人员投送标准权重配置",
                scenario_type="personnel_transport",
                description="人员投送场景的标准效能指标权重配置",
                weight_settings={
                    "timeliness": Decimal('0.35'),      # 时效性35%
                    "capability": Decimal('0.25'),      # 能力25%
                    "economy": Decimal('0.15'),         # 经济性15%
                    "robustness": Decimal('0.15'),      # 鲁棒性15%
                    "safety": Decimal('0.10')           # 安全性10%
                },
                is_default=True,
                created_by="系统管理员"
            )
            
            created_config = await service.create_weight_config(config_data)
            print(f"创建权重配置成功: {created_config.config_name}")
            print(f"配置ID: {created_config.id}")
            print(f"适用场景类型: {created_config.scenario_type}")
            
            # 显示权重分配
            print("权重分配:")
            for category, weight in created_config.weight_settings.items():
                print(f"  - {category}: {float(weight)*100}%")
            
            # 2. 获取默认权重配置
            print("\n=== 获取默认权重配置 ===")
            default_config = await service.get_default_weight_config("personnel_transport")
            if default_config:
                print(f"默认配置: {default_config.config_name}")
                print(f"是否为默认: {default_config.is_default}")
                print(f"是否激活: {default_config.is_active}")
            
            # 3. 创建其他场景类型的权重配置
            print("\n=== 创建装备运输权重配置 ===")
            equipment_config_data = IndicatorWeightConfigCreateSchema(
                config_name="装备运输权重配置",
                scenario_type="equipment_transport",
                description="装备运输场景更注重能力和经济性",
                weight_settings={
                    "timeliness": Decimal('0.25'),      # 时效性25%
                    "capability": Decimal('0.30'),      # 能力30%
                    "economy": Decimal('0.25'),         # 经济性25%
                    "robustness": Decimal('0.15'),      # 鲁棒性15%
                    "safety": Decimal('0.05')           # 安全性5%
                },
                is_default=True,
                created_by="系统管理员"
            )
            
            equipment_config = await service.create_weight_config(equipment_config_data)
            print(f"装备运输权重配置创建成功: {equipment_config.config_name}")
            
            # 4. 权重验证示例
            print("\n=== 权重验证示例 ===")
            try:
                # 尝试创建权重总和不为1的配置
                invalid_config_data = IndicatorWeightConfigCreateSchema(
                    config_name="无效权重配置",
                    scenario_type="invalid_test",
                    weight_settings={
                        "timeliness": Decimal('0.30'),
                        "capability": Decimal('0.30'),
                        "economy": Decimal('0.30')  # 总和为0.9，不等于1.0
                    }
                )
                await service.create_weight_config(invalid_config_data)
            except ValueError as e:
                print(f"权重验证失败（预期行为）: {e}")
            
        except Exception as e:
            print(f"操作失败: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
        break

# 运行示例
if __name__ == "__main__":
    asyncio.run(weight_config_service_example())
```

### 2. EfficiencyEvaluator - 综合评估服务

#### 类的用途和职责
`EfficiencyEvaluator` 提供效能指标的综合评估功能，支持基于权重的综合评分计算、多场景比较和性能分析。

#### 构造函数
```python
def __init__(self, db_session: AsyncSession) -> None:
    """
    初始化效能评估器
    
    Args:
        db_session: 数据库会话
    """
```

#### 主要方法说明

##### calculate_comprehensive_score()
```python
async def calculate_comprehensive_score(self, scenario_id: str, scenario_type: str) -> Dict[str, Decimal]:
    """
    计算场景的综合效能评分
    
    Args:
        scenario_id: 场景ID
        scenario_type: 场景类型
        
    Returns:
        Dict[str, Decimal]: 包含各分类评分和总评分的字典
        
    Raises:
        ValueError: 当未找到权重配置或计算结果时
    """
```

##### compare_scenarios()
```python
async def compare_scenarios(self, scenario_ids: List[str], scenario_type: str) -> List[Dict[str, any]]:
    """
    比较多个场景的效能评分
    
    Args:
        scenario_ids: 场景ID列表
        scenario_type: 场景类型
        
    Returns:
        List[Dict[str, any]]: 按评分排序的场景比较结果
    """
```

##### calculate_weighted_score()
```python
def calculate_weighted_score(self, indicator_values: Dict[str, Decimal], weights: Dict[str, Decimal]) -> Decimal:
    """
    计算加权评分
    
    Args:
        indicator_values: 指标值字典
        weights: 权重字典
        
    Returns:
        Decimal: 加权评分
    """
```

#### 完整使用示例

```python
import asyncio
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session
from src.services.efficiency_evaluator import EfficiencyEvaluator

async def efficiency_evaluator_example():
    """综合评估服务使用示例"""
    
    # 获取数据库会话
    async for db_session in get_database_session():
        try:
            # 初始化评估器
            evaluator = EfficiencyEvaluator(db_session)
            
            # 1. 计算加权评分示例
            print("=== 计算加权评分示例 ===")
            
            # 模拟指标值
            indicator_values = {
                "task_completion_time": Decimal('85.00'),      # 任务完成时间指标
                "mission_success_rate": Decimal('92.00'),      # 任务达成率指标
                "fuel_efficiency": Decimal('78.00'),           # 燃油效率指标
                "damage_resistance": Decimal('88.00'),         # 抗毁伤能力指标
                "loss_rate": Decimal('95.00')                  # 损失率指标（越高越好）
            }
            
            # 对应的权重
            weights = {
                "task_completion_time": Decimal('0.25'),       # 25%
                "mission_success_rate": Decimal('0.30'),       # 30%
                "fuel_efficiency": Decimal('0.20'),            # 20%
                "damage_resistance": Decimal('0.15'),          # 15%
                "loss_rate": Decimal('0.10')                   # 10%
            }
            
            weighted_score = evaluator.calculate_weighted_score(indicator_values, weights)
            
            print("指标值:")
            for indicator, value in indicator_values.items():
                weight = weights[indicator]
                print(f"  - {indicator}: {value} (权重: {float(weight)*100}%)")
            
            print(f"\n加权综合评分: {weighted_score}")
            
            # 2. 手动验证计算过程
            print("\n=== 手动验证计算过程 ===")
            manual_calculation = Decimal('0')
            total_weight = Decimal('0')
            
            print("计算过程:")
            for indicator, value in indicator_values.items():
                weight = weights[indicator]
                contribution = value * weight
                manual_calculation += contribution
                total_weight += weight
                print(f"  {indicator}: {value} × {weight} = {contribution}")
            
            final_score = manual_calculation / total_weight if total_weight > 0 else Decimal('0')
            print(f"\n手动计算结果: {final_score}")
            print(f"服务计算结果: {weighted_score}")
            print(f"结果一致性: {'✓' if abs(final_score - weighted_score) < Decimal('0.01') else '✗'}")
            
            # 3. 不同权重配置的影响分析
            print("\n=== 不同权重配置的影响分析 ===")
            
            # 时效性优先的权重配置
            timeliness_focused_weights = {
                "task_completion_time": Decimal('0.40'),       # 40%
                "mission_success_rate": Decimal('0.25'),       # 25%
                "fuel_efficiency": Decimal('0.15'),            # 15%
                "damage_resistance": Decimal('0.15'),          # 15%
                "loss_rate": Decimal('0.05')                   # 5%
            }
            
            # 能力优先的权重配置
            capability_focused_weights = {
                "task_completion_time": Decimal('0.15'),       # 15%
                "mission_success_rate": Decimal('0.45'),       # 45%
                "fuel_efficiency": Decimal('0.15'),            # 15%
                "damage_resistance": Decimal('0.20'),          # 20%
                "loss_rate": Decimal('0.05')                   # 5%
            }
            
            timeliness_score = evaluator.calculate_weighted_score(
                indicator_values, timeliness_focused_weights
            )
            capability_score = evaluator.calculate_weighted_score(
                indicator_values, capability_focused_weights
            )
            
            print(f"均衡权重评分: {weighted_score}")
            print(f"时效性优先评分: {timeliness_score}")
            print(f"能力优先评分: {capability_score}")
            
            # 分析权重配置对评分的影响
            print("\n权重配置影响分析:")
            print(f"时效性优先 vs 均衡权重: {timeliness_score - weighted_score:+.2f}")
            print(f"能力优先 vs 均衡权重: {capability_score - weighted_score:+.2f}")
            
            # 4. 场景比较示例（需要实际的场景数据）
            print("\n=== 场景比较功能说明 ===")
            print("场景比较功能需要实际的场景数据和计算结果")
            print("使用方法:")
            print("1. 确保数据库中有场景数据和对应的计算结果")
            print("2. 调用 compare_scenarios() 方法")
            print("3. 方法会返回按评分排序的场景比较结果")
            
            # 示例调用（需要真实数据）
            # scenario_ids = ["scenario_1", "scenario_2", "scenario_3"]
            # comparison_results = await evaluator.compare_scenarios(scenario_ids, "personnel_transport")
            # for result in comparison_results:
            #     print(f"场景 {result['scenario_id']}: 总评分 {result['total_score']}")
            
        except Exception as e:
            print(f"操作失败: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
        break

# 运行示例
if __name__ == "__main__":
    asyncio.run(efficiency_evaluator_example())
```

### 3. IndicatorWeightConfig - 权重配置数据模型

#### 类的用途和职责
`IndicatorWeightConfig` 存储不同场景类型下的指标权重配置信息。

#### 主要属性说明

```python
class IndicatorWeightConfig(BaseModel):
    """指标权重配置模型"""
    
    # 配置基本信息
    config_name: str                    # 配置名称
    scenario_type: str                  # 适用的场景类型
    description: Optional[str]          # 配置说明
    
    # 权重配置
    weight_settings: dict               # 各指标的权重分配
    
    # 状态信息
    is_default: bool                    # 是否为默认配置
    is_active: bool                     # 是否激活
    created_by: Optional[str]           # 创建者
```

#### 使用示例

```python
from src.database.models.efficiency import IndicatorWeightConfig
from decimal import Decimal

# 创建权重配置实例
weight_config = IndicatorWeightConfig(
    config_name="医疗后送权重配置",
    scenario_type="medical_evacuation",
    description="医疗后送场景强调时效性和安全性",
    weight_settings={
        "timeliness": 0.40,      # 时效性40%
        "capability": 0.20,      # 能力20%
        "economy": 0.10,         # 经济性10%
        "robustness": 0.15,      # 鲁棒性15%
        "safety": 0.15           # 安全性15%
    },
    is_default=True,
    is_active=True,
    created_by="医疗部门"
)

print(f"配置名称: {weight_config.config_name}")
print(f"适用场景: {weight_config.scenario_type}")
print(f"权重分配: {weight_config.weight_settings}")
```

## Schema层类说明

### IndicatorWeightConfigCreateSchema

```python
from src.schemas.efficiency import IndicatorWeightConfigCreateSchema
from decimal import Decimal

# 创建权重配置请求数据
create_data = IndicatorWeightConfigCreateSchema(
    config_name="特种运输权重配置",
    scenario_type="special_transport",
    description="特种运输场景平衡各项指标",
    weight_settings={
        "timeliness": Decimal('0.20'),
        "capability": Decimal('0.25'),
        "economy": Decimal('0.20'),
        "robustness": Decimal('0.20'),
        "safety": Decimal('0.15')
    },
    is_default=False,
    created_by="特种作业部门"
)

# 验证权重总和
total_weight = sum(create_data.weight_settings.values())
print(f"权重总和: {total_weight}")  # 应该等于1.0
```

## 完整的综合评估流程示例

```python
import asyncio
from decimal import Decimal
from src.database.connection import get_database_session
from src.services.efficiency import IndicatorWeightConfigService
from src.services.efficiency_evaluator import EfficiencyEvaluator
from src.schemas.efficiency import IndicatorWeightConfigCreateSchema

async def comprehensive_evaluation_workflow():
    """完整的综合评估流程示例"""
    
    async for db_session in get_database_session():
        try:
            # 初始化服务
            weight_service = IndicatorWeightConfigService(db_session)
            evaluator = EfficiencyEvaluator(db_session)
            
            print("=== 完整的综合评估流程 ===")
            
            # 步骤1: 创建权重配置
            print("\n步骤1: 创建权重配置")
            config_data = IndicatorWeightConfigCreateSchema(
                config_name="综合评估示例配置",
                scenario_type="comprehensive_example",
                description="用于演示综合评估流程的权重配置",
                weight_settings={
                    "timeliness": Decimal('0.30'),
                    "capability": Decimal('0.25'),
                    "economy": Decimal('0.20'),
                    "robustness": Decimal('0.15'),
                    "safety": Decimal('0.10')
                },
                is_default=True
            )
            
            weight_config = await weight_service.create_weight_config(config_data)
            print(f"权重配置创建成功: {weight_config.config_name}")
            
            # 步骤2: 模拟多个场景的指标值
            print("\n步骤2: 模拟场景指标值")
            scenarios_data = {
                "场景A": {
                    "timeliness": Decimal('85.0'),
                    "capability": Decimal('90.0'),
                    "economy": Decimal('75.0'),
                    "robustness": Decimal('80.0'),
                    "safety": Decimal('95.0')
                },
                "场景B": {
                    "timeliness": Decimal('92.0'),
                    "capability": Decimal('85.0'),
                    "economy": Decimal('88.0'),
                    "robustness": Decimal('78.0'),
                    "safety": Decimal('90.0')
                },
                "场景C": {
                    "timeliness": Decimal('78.0'),
                    "capability": Decimal('95.0'),
                    "economy": Decimal('82.0'),
                    "robustness": Decimal('88.0'),
                    "safety": Decimal('85.0')
                }
            }
            
            # 步骤3: 计算各场景的综合评分
            print("\n步骤3: 计算综合评分")
            scenario_scores = {}
            weights = {
                "timeliness": Decimal('0.30'),
                "capability": Decimal('0.25'),
                "economy": Decimal('0.20'),
                "robustness": Decimal('0.15'),
                "safety": Decimal('0.10')
            }
            
            for scenario_name, indicator_values in scenarios_data.items():
                score = evaluator.calculate_weighted_score(indicator_values, weights)
                scenario_scores[scenario_name] = score
                
                print(f"\n{scenario_name}:")
                print(f"  指标值: {dict(indicator_values)}")
                print(f"  综合评分: {score}")
            
            # 步骤4: 场景排名和比较
            print("\n步骤4: 场景排名和比较")
            sorted_scenarios = sorted(
                scenario_scores.items(), 
                key=lambda x: x[1], 
                reverse=True
            )
            
            print("场景排名（从高到低）:")
            for rank, (scenario_name, score) in enumerate(sorted_scenarios, 1):
                print(f"  {rank}. {scenario_name}: {score}")
            
            # 步骤5: 敏感性分析
            print("\n步骤5: 权重敏感性分析")
            
            # 测试时效性权重变化的影响
            original_weights = weights.copy()
            modified_weights = weights.copy()
            modified_weights["timeliness"] = Decimal('0.50')  # 提高时效性权重到50%
            modified_weights["capability"] = Decimal('0.15')  # 相应降低其他权重
            modified_weights["economy"] = Decimal('0.15')
            modified_weights["robustness"] = Decimal('0.10')
            modified_weights["safety"] = Decimal('0.10')
            
            print("权重敏感性分析（提高时效性权重到50%）:")
            for scenario_name, indicator_values in scenarios_data.items():
                original_score = evaluator.calculate_weighted_score(indicator_values, original_weights)
                modified_score = evaluator.calculate_weighted_score(indicator_values, modified_weights)
                change = modified_score - original_score
                
                print(f"  {scenario_name}: {original_score} → {modified_score} (变化: {change:+.2f})")
            
        except Exception as e:
            print(f"流程执行失败: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
        break

# 运行完整流程示例
if __name__ == "__main__":
    asyncio.run(comprehensive_evaluation_workflow())
```

## 常见问题和故障排除

### Q1: 权重总和验证失败
**解决方案**: 确保所有权重值的总和等于1.0，允许小的浮点误差（±0.001）。

### Q2: 场景比较时找不到计算结果
**解决方案**: 
1. 确保场景存在于数据库中
2. 确保场景有对应的指标计算结果
3. 运行数据生成脚本生成测试数据

### Q3: 综合评分计算异常
**解决方案**: 
1. 检查权重配置是否存在
2. 验证指标值的数据类型（应为Decimal）
3. 确保权重字典的键与指标值字典的键匹配

### Q4: 默认权重配置获取失败
**解决方案**: 
1. 检查场景类型是否正确
2. 确保数据库中存在该场景类型的默认配置
3. 验证配置的is_default和is_active状态
