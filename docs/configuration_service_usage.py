#!/usr/bin/env python3
"""
配置方案服务层使用示例

演示ConfigurationService的初始化和主要方法调用
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.services.configuration import ConfigurationService
from src.schemas.configuration import (
    ConfigurationSchemeCreateSchema,
    ConfigurationSchemeUpdateSchema,
    OperationalParametersSchema
)
from src.database.models.configuration import SchemeTypeEnum, SchemeStatusEnum
from src.database.connection import get_database_session


async def demonstrate_configuration_service_usage():
    """
    演示ConfigurationService的完整使用流程
    """
    print("=== ConfigurationService 使用示例 ===\n")
    
    async for db_session in get_database_session():
        try:
            # 1. 初始化服务
            print("1. 初始化ConfigurationService")
            configuration_service = ConfigurationService(db_session)
            print("✅ ConfigurationService初始化完成")
            
            # 2. 创建配置方案
            print("\n2. 创建新配置方案")
            
            # 构建创建数据
            create_data = ConfigurationSchemeCreateSchema(
                scheme_name="技术文档示例综合配置方案",
                scheme_type=SchemeTypeEnum.MIXED_TRANSPORT,
                description="用于技术文档演示的综合配置方案，整合多种资源类型",
                equipment_config_ids=[
                    "doc_equipment_001",  # 示例设备配置ID
                    "doc_equipment_002"
                ],
                aircraft_config_ids=[
                    "doc_aircraft_001",   # 示例飞机配置ID
                    "doc_aircraft_002"
                ],
                personnel_config_ids=[
                    "doc_personnel_001",  # 示例人员配置ID
                    "doc_personnel_002",
                    "doc_personnel_003"
                ],
                weather_condition_ids=[
                    "doc_weather_001",    # 示例气象条件ID
                    "doc_weather_002"
                ],
                scenario_parameters={
                    "max_operation_time": 20,
                    "safety_priority": "high",
                    "backup_resource_ratio": 0.25,
                    "coordination_complexity": "medium"
                },
                operational_parameters=OperationalParametersSchema(
                    max_operation_duration=14.0,
                    safety_margin=0.22,
                    efficiency_target=0.87,
                    priority_level="high"
                ),
                tags=["技术文档", "综合配置", "示例方案", "多资源"],
                category="技术演示",
                created_by="doc_generator"
            )
            
            # 调用创建方法
            created_scheme = await configuration_service.create_configuration_scheme(create_data)
            scheme_id = created_scheme.id
            
            print(f"✅ 配置方案创建成功")
            print(f"   方案ID: {scheme_id}")
            print(f"   方案名称: {created_scheme.scheme_name}")
            print(f"   方案类型: {created_scheme.scheme_type}")
            print(f"   版本号: {created_scheme.version}")
            print(f"   状态: {created_scheme.status}")
            print(f"   创建时间: {created_scheme.created_at}")
            
            # 3. 获取配置方案详情
            print("\n3. 获取配置方案详情")
            scheme_detail = await configuration_service.get_configuration_scheme_by_id(scheme_id)
            
            if scheme_detail:
                print(f"✅ 配置方案详情获取成功")
                print(f"   方案名称: {scheme_detail.scheme_name}")
                print(f"   方案描述: {scheme_detail.description}")
                print(f"   设备配置数量: {len(scheme_detail.equipment_config_ids)}")
                print(f"   飞机配置数量: {len(scheme_detail.aircraft_config_ids)}")
                print(f"   人员配置数量: {len(scheme_detail.personnel_config_ids)}")
                print(f"   气象条件数量: {len(scheme_detail.weather_condition_ids)}")
                print(f"   标签: {', '.join(scheme_detail.tags)}")
                print(f"   分类: {scheme_detail.category}")
                
                # 显示作业参数
                operational = scheme_detail.operational_parameters
                print(f"   作业参数:")
                print(f"     - 最大持续时间: {operational.get('max_operation_duration')}小时")
                print(f"     - 安全裕度: {operational.get('safety_margin')}")
                print(f"     - 效率目标: {operational.get('efficiency_target')}")
                print(f"     - 优先级: {operational.get('priority_level')}")
            else:
                print("❌ 配置方案不存在")
            
            # 4. 获取配置方案列表
            print("\n4. 获取配置方案列表")
            scheme_list = await configuration_service.get_configuration_scheme_list(
                page=1,
                page_size=10,
                scheme_type=SchemeTypeEnum.MIXED_TRANSPORT,
                status=SchemeStatusEnum.DRAFT,
                category="技术演示",
                search_keyword="技术文档"
            )
            
            print(f"✅ 配置方案列表获取成功")
            print(f"   总数量: {scheme_list.total}")
            print(f"   当前页: {scheme_list.page}")
            print(f"   每页数量: {scheme_list.page_size}")
            print(f"   总页数: {scheme_list.total_pages}")
            print(f"   方案数量: {len(scheme_list.schemes)}")
            
            for scheme in scheme_list.schemes:
                print(f"   - {scheme.scheme_name} (类型: {scheme.scheme_type}, 版本: {scheme.version})")
            
            # 5. 更新配置方案
            print("\n5. 更新配置方案")
            update_data = ConfigurationSchemeUpdateSchema(
                scheme_name="更新后的技术文档示例综合配置方案",
                description="已优化的技术文档演示配置方案",
                equipment_config_ids=[
                    "doc_equipment_001",
                    "doc_equipment_002",
                    "doc_equipment_003"  # 新增设备配置
                ],
                operational_parameters=OperationalParametersSchema(
                    max_operation_duration=12.0,  # 优化时间
                    safety_margin=0.25,           # 提高安全裕度
                    efficiency_target=0.90,       # 提高效率目标
                    priority_level="high"
                ),
                tags=["技术文档", "综合配置", "示例方案", "多资源", "已优化"],  # 新增标签
                status=SchemeStatusEnum.VALIDATED,  # 更新状态
                rating=4.2  # 设置评分
            )
            
            updated_scheme = await configuration_service.update_configuration_scheme(
                scheme_id, update_data
            )
            
            if updated_scheme:
                print(f"✅ 配置方案更新成功")
                print(f"   新名称: {updated_scheme.scheme_name}")
                print(f"   新版本: {updated_scheme.version}")
                print(f"   新状态: {updated_scheme.status}")
                print(f"   新评分: {updated_scheme.rating}")
                print(f"   设备配置数量: {len(updated_scheme.equipment_config_ids)}")
                print(f"   更新时间: {updated_scheme.updated_at}")
                
                # 显示更新后的作业参数
                new_operational = updated_scheme.operational_parameters
                print(f"   更新后作业参数:")
                print(f"     - 最大持续时间: {new_operational.get('max_operation_duration')}小时")
                print(f"     - 安全裕度: {new_operational.get('safety_margin')}")
                print(f"     - 效率目标: {new_operational.get('efficiency_target')}")
            else:
                print("❌ 配置方案更新失败")
            
            # 6. 验证配置方案
            print("\n6. 验证配置方案")
            validation_result = await configuration_service.validate_configuration_scheme(scheme_id)
            
            print(f"验证结果:")
            print(f"   总体状态: {validation_result.overall_status}")
            
            if validation_result.validation_errors:
                print(f"   验证错误:")
                for error in validation_result.validation_errors:
                    print(f"     - {error}")
            
            if validation_result.validation_warnings:
                print(f"   验证警告:")
                for warning in validation_result.validation_warnings:
                    print(f"     - {warning}")
            
            print(f"   资源兼容性: {validation_result.resource_compatibility}")
            
            if validation_result.optimization_suggestions:
                print(f"   优化建议:")
                for suggestion in validation_result.optimization_suggestions:
                    print(f"     - {suggestion.get('suggestion')} (预期改进: {suggestion.get('expected_improvement', 0)})")
            
            print(f"   性能预测: {validation_result.performance_prediction}")
            
            # 7. 获取方案版本列表
            print("\n7. 获取方案版本列表")
            version_list = await configuration_service.get_scheme_versions(scheme_id)
            
            print(f"✅ 方案版本列表获取成功")
            print(f"   版本总数: {version_list.total}")
            
            for version in version_list.versions:
                current_flag = " (当前版本)" if version.is_current else ""
                print(f"   - 版本 {version.version_number}{current_flag}")
                print(f"     变更描述: {version.change_description or '无描述'}")
                print(f"     创建时间: {version.created_at}")
                print(f"     创建者: {version.created_by or '未知'}")
            
            # 8. 删除配置方案（软删除）
            print("\n8. 删除配置方案")
            delete_success = await configuration_service.delete_configuration_scheme(scheme_id)
            
            if delete_success:
                print("✅ 配置方案删除成功（软删除）")
                
                # 验证软删除
                deleted_scheme = await configuration_service.get_configuration_scheme_by_id(scheme_id)
                if deleted_scheme and deleted_scheme.status == SchemeStatusEnum.ARCHIVED:
                    print("✅ 确认配置方案已标记为归档状态")
            else:
                print("❌ 配置方案删除失败")
            
        except Exception as e:
            print(f"❌ 操作过程中发生错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


async def demonstrate_configuration_service_advanced_features():
    """
    演示ConfigurationService的高级功能
    """
    print("\n=== ConfigurationService 高级功能示例 ===\n")
    
    async for db_session in get_database_session():
        try:
            configuration_service = ConfigurationService(db_session)
            
            # 1. 创建多个配置方案进行对比
            print("1. 创建多个配置方案进行对比分析")
            
            schemes_data = [
                {
                    "name": "高效率方案",
                    "data": ConfigurationSchemeCreateSchema(
                        scheme_name="高效率运输配置方案",
                        scheme_type=SchemeTypeEnum.PERSONNEL_TRANSPORT,
                        description="注重效率的人员运输配置方案",
                        equipment_config_ids=["eq_001", "eq_002"],
                        aircraft_config_ids=["ac_001"],
                        personnel_config_ids=["pe_001", "pe_002"],
                        operational_parameters=OperationalParametersSchema(
                            max_operation_duration=10.0,
                            safety_margin=0.15,
                            efficiency_target=0.95,
                            priority_level="high"
                        ),
                        tags=["高效率", "快速"],
                        category="人员运输",
                        created_by="efficiency_optimizer"
                    )
                },
                {
                    "name": "安全优先方案",
                    "data": ConfigurationSchemeCreateSchema(
                        scheme_name="安全优先运输配置方案",
                        scheme_type=SchemeTypeEnum.PERSONNEL_TRANSPORT,
                        description="注重安全的人员运输配置方案",
                        equipment_config_ids=["eq_001", "eq_002", "eq_003"],
                        aircraft_config_ids=["ac_001", "ac_002"],
                        personnel_config_ids=["pe_001", "pe_002", "pe_003"],
                        operational_parameters=OperationalParametersSchema(
                            max_operation_duration=16.0,
                            safety_margin=0.35,
                            efficiency_target=0.80,
                            priority_level="medium"
                        ),
                        tags=["安全优先", "稳定"],
                        category="人员运输",
                        created_by="safety_specialist"
                    )
                }
            ]
            
            created_scheme_ids = []
            
            for scheme_info in schemes_data:
                created_scheme = await configuration_service.create_configuration_scheme(
                    scheme_info["data"]
                )
                created_scheme_ids.append(created_scheme.id)
                print(f"   ✅ 创建方案: {scheme_info['name']} (ID: {created_scheme.id})")
            
            # 2. 对比分析不同方案
            print(f"\n2. 对比分析不同配置方案")
            
            print(f"{'方案名称':<25} {'效率目标':<10} {'安全裕度':<10} {'资源总数':<10} {'持续时间':<10}")
            print("-" * 75)
            
            for scheme_id in created_scheme_ids:
                scheme = await configuration_service.get_configuration_scheme_by_id(scheme_id)
                if scheme:
                    operational = scheme.operational_parameters
                    resource_count = (len(scheme.equipment_config_ids) + 
                                    len(scheme.aircraft_config_ids) + 
                                    len(scheme.personnel_config_ids))
                    
                    print(f"{scheme.scheme_name:<25} "
                          f"{operational.get('efficiency_target', 0):<10} "
                          f"{operational.get('safety_margin', 0):<10} "
                          f"{resource_count:<10} "
                          f"{operational.get('max_operation_duration', 0):<10}")
            
            # 3. 批量验证方案
            print(f"\n3. 批量验证配置方案")
            
            for i, scheme_id in enumerate(created_scheme_ids, 1):
                print(f"   验证方案 {i}:")
                validation_result = await configuration_service.validate_configuration_scheme(scheme_id)
                
                print(f"     - 总体状态: {validation_result.overall_status}")
                print(f"     - 错误数量: {len(validation_result.validation_errors)}")
                print(f"     - 警告数量: {len(validation_result.validation_warnings)}")
                
                # 显示性能预测
                performance = validation_result.performance_prediction
                if performance:
                    print(f"     - 预计完成时间: {performance.get('estimated_completion_time', 'N/A')}小时")
                    print(f"     - 效率评分: {performance.get('efficiency_score', 'N/A')}")
                    print(f"     - 安全评分: {performance.get('safety_score', 'N/A')}")
            
            # 4. 方案版本管理演示
            print(f"\n4. 方案版本管理演示")
            
            # 选择第一个方案进行版本更新
            first_scheme_id = created_scheme_ids[0]
            
            # 进行多次更新以创建版本历史
            updates = [
                {
                    "description": "优化效率参数",
                    "data": ConfigurationSchemeUpdateSchema(
                        operational_parameters=OperationalParametersSchema(
                            max_operation_duration=9.0,
                            safety_margin=0.15,
                            efficiency_target=0.97,
                            priority_level="high"
                        )
                    )
                },
                {
                    "description": "增加备用资源",
                    "data": ConfigurationSchemeUpdateSchema(
                        equipment_config_ids=["eq_001", "eq_002", "eq_004"],
                        operational_parameters=OperationalParametersSchema(
                            max_operation_duration=9.0,
                            safety_margin=0.18,
                            efficiency_target=0.97,
                            priority_level="high"
                        )
                    )
                }
            ]
            
            for update_info in updates:
                updated_scheme = await configuration_service.update_configuration_scheme(
                    first_scheme_id, update_info["data"]
                )
                if updated_scheme:
                    print(f"   ✅ 版本更新: {update_info['description']} (版本: {updated_scheme.version})")
            
            # 查看版本历史
            version_list = await configuration_service.get_scheme_versions(first_scheme_id)
            print(f"   版本历史 (共{version_list.total}个版本):")
            for version in version_list.versions:
                current_flag = " [当前]" if version.is_current else ""
                print(f"     - {version.version_number}{current_flag}: {version.change_description}")
            
            # 5. 清理测试数据
            print(f"\n5. 清理测试数据")
            deleted_count = 0
            for scheme_id in created_scheme_ids:
                if await configuration_service.delete_configuration_scheme(scheme_id):
                    deleted_count += 1
            
            print(f"   ✅ 成功删除 {deleted_count} 个配置方案")
            
        except Exception as e:
            print(f"❌ 高级功能演示过程中发生错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


async def demonstrate_configuration_service_optimization():
    """
    演示ConfigurationService的优化建议功能
    """
    print("\n=== ConfigurationService 优化建议示例 ===\n")
    
    async for db_session in get_database_session():
        try:
            configuration_service = ConfigurationService(db_session)
            
            # 创建一个需要优化的配置方案
            print("1. 创建需要优化的配置方案")
            
            suboptimal_data = ConfigurationSchemeCreateSchema(
                scheme_name="待优化配置方案",
                scheme_type=SchemeTypeEnum.MATERIAL_SUPPLY,
                description="存在优化空间的物资补给配置方案",
                equipment_config_ids=["eq_001"],  # 设备配置较少
                aircraft_config_ids=["ac_001"],
                personnel_config_ids=["pe_001"],
                operational_parameters=OperationalParametersSchema(
                    max_operation_duration=20.0,  # 时间较长
                    safety_margin=0.10,           # 安全裕度较低
                    efficiency_target=0.70,       # 效率目标较低
                    priority_level="low"
                ),
                tags=["待优化"],
                category="物资补给",
                created_by="test_user"
            )
            
            created_scheme = await configuration_service.create_configuration_scheme(suboptimal_data)
            scheme_id = created_scheme.id
            
            print(f"   ✅ 创建待优化方案: {created_scheme.scheme_name}")
            
            # 2. 获取优化建议
            print(f"\n2. 获取优化建议")
            
            validation_result = await configuration_service.validate_configuration_scheme(scheme_id)
            
            print(f"   当前方案状态: {validation_result.overall_status}")
            
            if validation_result.optimization_suggestions:
                print(f"   优化建议:")
                for i, suggestion in enumerate(validation_result.optimization_suggestions, 1):
                    print(f"     {i}. {suggestion.get('suggestion', '无建议')}")
                    print(f"        分类: {suggestion.get('category', '未分类')}")
                    print(f"        预期改进: {suggestion.get('expected_improvement', 0)}")
            else:
                print(f"   暂无优化建议")
            
            # 3. 应用优化建议
            print(f"\n3. 应用优化建议进行方案改进")
            
            optimized_data = ConfigurationSchemeUpdateSchema(
                scheme_name="优化后的配置方案",
                description="根据优化建议改进的物资补给配置方案",
                equipment_config_ids=["eq_001", "eq_002"],  # 增加设备配置
                operational_parameters=OperationalParametersSchema(
                    max_operation_duration=16.0,  # 缩短时间
                    safety_margin=0.20,           # 提高安全裕度
                    efficiency_target=0.85,       # 提高效率目标
                    priority_level="medium"
                ),
                tags=["待优化", "已改进"],
                rating=3.8  # 设置评分
            )
            
            optimized_scheme = await configuration_service.update_configuration_scheme(
                scheme_id, optimized_data
            )
            
            if optimized_scheme:
                print(f"   ✅ 方案优化完成: {optimized_scheme.scheme_name}")
                print(f"   新版本: {optimized_scheme.version}")
                print(f"   新评分: {optimized_scheme.rating}")
            
            # 4. 验证优化效果
            print(f"\n4. 验证优化效果")
            
            optimized_validation = await configuration_service.validate_configuration_scheme(scheme_id)
            
            print(f"   优化后状态: {optimized_validation.overall_status}")
            print(f"   错误数量: {len(optimized_validation.validation_errors)}")
            print(f"   警告数量: {len(optimized_validation.validation_warnings)}")
            
            # 对比性能预测
            original_performance = validation_result.performance_prediction
            optimized_performance = optimized_validation.performance_prediction
            
            print(f"   性能对比:")
            print(f"     原方案完成时间: {original_performance.get('estimated_completion_time', 'N/A')}小时")
            print(f"     优化后完成时间: {optimized_performance.get('estimated_completion_time', 'N/A')}小时")
            print(f"     原方案效率评分: {original_performance.get('efficiency_score', 'N/A')}")
            print(f"     优化后效率评分: {optimized_performance.get('efficiency_score', 'N/A')}")
            
            # 清理测试数据
            await configuration_service.delete_configuration_scheme(scheme_id)
            print(f"\n   ✅ 测试数据清理完成")
            
        except Exception as e:
            print(f"❌ 优化建议演示过程中发生错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


if __name__ == "__main__":
    asyncio.run(demonstrate_configuration_service_usage())
    asyncio.run(demonstrate_configuration_service_advanced_features())
    asyncio.run(demonstrate_configuration_service_optimization())
