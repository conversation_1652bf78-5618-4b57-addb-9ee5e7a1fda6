#!/usr/bin/env python3
"""
配置方案数据模型使用示例

演示ConfigurationScheme模型的创建、属性访问和方法调用
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.models.configuration import (
    ConfigurationScheme,
    SchemeVersion,
    SchemeTypeEnum,
    SchemeStatusEnum
)
from src.database.connection import get_database_session


async def demonstrate_configuration_model_usage():
    """
    演示ConfigurationScheme模型的使用方法
    """
    print("=== ConfigurationScheme 数据模型使用示例 ===\n")
    
    # 1. 创建配置方案实例
    print("1. 创建配置方案实例")
    configuration_scheme = ConfigurationScheme(
        scheme_name="重型装备运输综合配置方案",
        scheme_type=SchemeTypeEnum.HEAVY_EQUIPMENT_TRANSPORT,
        description="适用于重型装备运输的综合配置方案，整合飞机、设备、人员等资源",
        version="1.0.0",
        equipment_config_ids=[
            "equipment_001",  # 装载车配置ID
            "equipment_002",  # 叉车配置ID
            "equipment_003"   # 牵引车配置ID
        ],
        aircraft_config_ids=[
            "aircraft_001",   # 运-20配置ID
            "aircraft_002"    # 运-8配置ID
        ],
        personnel_config_ids=[
            "personnel_001",  # 设备操作员配置ID
            "personnel_002",  # 地勤人员配置ID
            "personnel_003"   # 指挥协调员配置ID
        ],
        weather_condition_ids=[
            "weather_001",    # 晴天条件ID
            "weather_002"     # 多云条件ID
        ],
        scenario_parameters={
            "max_operation_time": 24,        # 最大作业时间（小时）
            "safety_priority": "high",       # 安全优先级
            "efficiency_target": 0.85,       # 效率目标
            "backup_resource_ratio": 0.2     # 备用资源比例
        },
        operational_parameters={
            "max_operation_duration": 16,    # 最大作业持续时间（小时）
            "safety_margin": 0.25,           # 安全裕度
            "efficiency_target": 0.88,       # 效率目标
            "priority_level": "high"         # 优先级别
        },
        tags=["重型运输", "综合配置", "高效率", "安全优先"],
        category="运输保障",
        created_by="system_designer",
        status=SchemeStatusEnum.DRAFT,
        usage_count=0,
        rating=None
    )
    
    print(f"方案名称: {configuration_scheme.scheme_name}")
    print(f"方案类型: {configuration_scheme.scheme_type}")
    print(f"版本号: {configuration_scheme.version}")
    print(f"方案状态: {configuration_scheme.status}")
    print(f"创建者: {configuration_scheme.created_by}")
    print(f"分类: {configuration_scheme.category}")
    
    # 2. 访问配置ID列表
    print("\n2. 访问配置ID列表")
    print(f"设备配置数量: {len(configuration_scheme.equipment_config_ids)}")
    for i, eq_id in enumerate(configuration_scheme.equipment_config_ids, 1):
        print(f"  {i}. 设备配置ID: {eq_id}")
    
    print(f"飞机配置数量: {len(configuration_scheme.aircraft_config_ids)}")
    for i, ac_id in enumerate(configuration_scheme.aircraft_config_ids, 1):
        print(f"  {i}. 飞机配置ID: {ac_id}")
    
    print(f"人员配置数量: {len(configuration_scheme.personnel_config_ids)}")
    for i, pe_id in enumerate(configuration_scheme.personnel_config_ids, 1):
        print(f"  {i}. 人员配置ID: {pe_id}")
    
    # 3. 访问场景参数
    print("\n3. 访问场景参数")
    scenario_params = configuration_scheme.scenario_parameters
    print(f"场景参数:")
    print(f"  - 最大作业时间: {scenario_params.get('max_operation_time')}小时")
    print(f"  - 安全优先级: {scenario_params.get('safety_priority')}")
    print(f"  - 效率目标: {scenario_params.get('efficiency_target')}")
    print(f"  - 备用资源比例: {scenario_params.get('backup_resource_ratio')}")
    
    # 4. 访问作业参数
    print("\n4. 访问作业参数")
    operational_params = configuration_scheme.operational_parameters
    print(f"作业参数:")
    print(f"  - 最大作业持续时间: {operational_params.get('max_operation_duration')}小时")
    print(f"  - 安全裕度: {operational_params.get('safety_margin')}")
    print(f"  - 效率目标: {operational_params.get('efficiency_target')}")
    print(f"  - 优先级别: {operational_params.get('priority_level')}")
    
    # 5. 访问标签和分类
    print("\n5. 访问标签和分类")
    print(f"标签 ({len(configuration_scheme.tags)}个): {', '.join(configuration_scheme.tags)}")
    print(f"分类: {configuration_scheme.category}")
    
    # 6. 使用模型方法
    print("\n6. 使用模型方法")
    print(f"字符串表示: {configuration_scheme}")
    
    # 转换为字典
    scheme_dict = configuration_scheme.to_dict()
    print(f"字典格式包含字段: {list(scheme_dict.keys())}")
    
    # 7. 数据库操作示例
    print("\n7. 数据库操作示例")
    async for db_session in get_database_session():
        try:
            # 保存到数据库
            db_session.add(configuration_scheme)
            await db_session.commit()
            await db_session.refresh(configuration_scheme)
            
            print(f"配置方案已保存到数据库，ID: {configuration_scheme.id}")
            print(f"创建时间: {configuration_scheme.created_at}")
            print(f"更新时间: {configuration_scheme.updated_at}")
            
            # 更新方案状态和评分
            configuration_scheme.status = SchemeStatusEnum.VALIDATED
            configuration_scheme.rating = 4.5
            configuration_scheme.usage_count = 1
            await db_session.commit()
            print(f"方案状态已更新为: {configuration_scheme.status}")
            print(f"方案评分: {configuration_scheme.rating}")
            
        except Exception as e:
            print(f"数据库操作错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


async def demonstrate_scheme_version_usage():
    """
    演示SchemeVersion模型的使用方法
    """
    print("\n=== SchemeVersion 数据模型使用示例 ===\n")
    
    async for db_session in get_database_session():
        try:
            # 1. 创建方案版本实例
            print("1. 创建方案版本实例")
            
            # 假设已有配置方案ID
            scheme_id = "test_scheme_id"
            
            scheme_version = SchemeVersion(
                scheme_id=scheme_id,
                version_number="1.1.0",
                change_description="优化了设备配置，提高了作业效率",
                configuration_snapshot={
                    "scheme_name": "重型装备运输综合配置方案",
                    "scheme_type": "heavy_equipment_transport",
                    "equipment_config_ids": ["equipment_001", "equipment_002", "equipment_004"],  # 更新了设备
                    "aircraft_config_ids": ["aircraft_001", "aircraft_002"],
                    "operational_parameters": {
                        "max_operation_duration": 14,  # 优化了时间
                        "efficiency_target": 0.90      # 提高了效率目标
                    }
                },
                is_current=True,
                created_by="system_optimizer"
            )
            
            print(f"版本号: {scheme_version.version_number}")
            print(f"变更描述: {scheme_version.change_description}")
            print(f"是否为当前版本: {scheme_version.is_current}")
            print(f"创建者: {scheme_version.created_by}")
            
            # 2. 访问配置快照
            print("\n2. 访问配置快照")
            snapshot = scheme_version.configuration_snapshot
            print(f"快照内容:")
            print(f"  - 方案名称: {snapshot.get('scheme_name')}")
            print(f"  - 方案类型: {snapshot.get('scheme_type')}")
            print(f"  - 设备配置数量: {len(snapshot.get('equipment_config_ids', []))}")
            print(f"  - 飞机配置数量: {len(snapshot.get('aircraft_config_ids', []))}")
            
            operational_params = snapshot.get('operational_parameters', {})
            print(f"  - 作业参数:")
            print(f"    * 最大持续时间: {operational_params.get('max_operation_duration')}小时")
            print(f"    * 效率目标: {operational_params.get('efficiency_target')}")
            
            # 3. 保存版本到数据库
            print("\n3. 保存版本到数据库")
            db_session.add(scheme_version)
            await db_session.commit()
            await db_session.refresh(scheme_version)
            
            print(f"方案版本已保存，ID: {scheme_version.id}")
            print(f"创建时间: {scheme_version.created_at}")
            
        except Exception as e:
            print(f"版本操作错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


async def demonstrate_configuration_queries():
    """
    演示配置方案查询操作
    """
    print("\n=== 配置方案查询操作示例 ===\n")
    
    from sqlalchemy import select, and_, or_, func
    
    async for db_session in get_database_session():
        try:
            # 1. 按方案类型查询
            print("1. 查询所有重型装备运输方案")
            query = select(ConfigurationScheme).where(
                ConfigurationScheme.scheme_type == SchemeTypeEnum.HEAVY_EQUIPMENT_TRANSPORT
            )
            result = await db_session.execute(query)
            heavy_transport_schemes = result.scalars().all()
            
            print(f"找到 {len(heavy_transport_schemes)} 个重型装备运输方案:")
            for scheme in heavy_transport_schemes:
                print(f"  - {scheme.scheme_name} (版本: {scheme.version}, 状态: {scheme.status})")
            
            # 2. 按状态和评分查询
            print("\n2. 查询已验证且评分高于4.0的方案")
            query = select(ConfigurationScheme).where(
                and_(
                    ConfigurationScheme.status == SchemeStatusEnum.VALIDATED,
                    ConfigurationScheme.rating > 4.0
                )
            )
            result = await db_session.execute(query)
            high_rated_schemes = result.scalars().all()
            
            print(f"找到 {len(high_rated_schemes)} 个高评分方案:")
            for scheme in high_rated_schemes:
                print(f"  - {scheme.scheme_name} (评分: {scheme.rating})")
            
            # 3. 按标签查询
            print("\n3. 查询包含'高效率'标签的方案")
            query = select(ConfigurationScheme).where(
                ConfigurationScheme.tags.contains(["高效率"])
            )
            result = await db_session.execute(query)
            efficient_schemes = result.scalars().all()
            
            print(f"找到 {len(efficient_schemes)} 个高效率方案")
            
            # 4. 统计查询
            print("\n4. 配置方案统计信息")
            
            # 按类型统计
            query = select(
                ConfigurationScheme.scheme_type,
                func.count(ConfigurationScheme.id).label('count'),
                func.avg(ConfigurationScheme.rating).label('avg_rating'),
                func.sum(ConfigurationScheme.usage_count).label('total_usage')
            ).group_by(ConfigurationScheme.scheme_type)
            
            result = await db_session.execute(query)
            statistics = result.all()
            
            print("按类型统计:")
            for stat in statistics:
                avg_rating = stat.avg_rating if stat.avg_rating else 0
                total_usage = stat.total_usage if stat.total_usage else 0
                print(f"  - {stat.scheme_type}: {stat.count}个方案, "
                      f"平均评分{avg_rating:.1f}, "
                      f"总使用次数{total_usage}")
            
            # 5. 版本查询
            print("\n5. 查询方案版本信息")
            
            # 查询当前版本
            query = select(SchemeVersion).where(SchemeVersion.is_current == True)
            result = await db_session.execute(query)
            current_versions = result.scalars().all()
            
            print(f"当前版本数量: {len(current_versions)}")
            for version in current_versions:
                print(f"  - 方案ID: {version.scheme_id}, 版本: {version.version_number}")
            
        except Exception as e:
            print(f"查询操作错误: {e}")
        finally:
            await db_session.close()
            break


async def demonstrate_configuration_analysis():
    """
    演示配置方案分析功能
    """
    print("\n=== 配置方案分析示例 ===\n")
    
    # 创建示例配置方案进行分析
    schemes = [
        {
            "name": "高效率方案",
            "config": ConfigurationScheme(
                scheme_name="高效率重型运输方案",
                scheme_type=SchemeTypeEnum.HEAVY_EQUIPMENT_TRANSPORT,
                equipment_config_ids=["eq_001", "eq_002"],
                aircraft_config_ids=["ac_001"],
                operational_parameters={
                    "efficiency_target": 0.92,
                    "max_operation_duration": 12,
                    "safety_margin": 0.15
                },
                tags=["高效率", "快速"],
                usage_count=25,
                rating=4.6
            )
        },
        {
            "name": "安全优先方案",
            "config": ConfigurationScheme(
                scheme_name="安全优先重型运输方案",
                scheme_type=SchemeTypeEnum.HEAVY_EQUIPMENT_TRANSPORT,
                equipment_config_ids=["eq_001", "eq_002", "eq_003"],
                aircraft_config_ids=["ac_001", "ac_002"],
                operational_parameters={
                    "efficiency_target": 0.85,
                    "max_operation_duration": 16,
                    "safety_margin": 0.30
                },
                tags=["安全优先", "稳定"],
                usage_count=18,
                rating=4.8
            )
        }
    ]
    
    print("1. 配置方案对比分析")
    print(f"{'方案名称':<20} {'效率目标':<10} {'安全裕度':<10} {'资源数量':<10} {'使用次数':<10} {'评分':<6}")
    print("-" * 75)
    
    for scheme_info in schemes:
        config = scheme_info["config"]
        operational = config.operational_parameters
        resource_count = len(config.equipment_config_ids) + len(config.aircraft_config_ids)
        
        print(f"{config.scheme_name:<20} "
              f"{operational.get('efficiency_target', 0):<10} "
              f"{operational.get('safety_margin', 0):<10} "
              f"{resource_count:<10} "
              f"{config.usage_count:<10} "
              f"{config.rating or 0:<6}")
    
    # 2. 方案优劣分析
    print(f"\n2. 方案优劣分析")
    
    for scheme_info in schemes:
        config = scheme_info["config"]
        operational = config.operational_parameters
        
        # 计算综合评分
        efficiency_score = operational.get('efficiency_target', 0) * 100
        safety_score = operational.get('safety_margin', 0) * 100
        popularity_score = min(config.usage_count / 30 * 100, 100)  # 标准化使用次数
        rating_score = (config.rating or 0) * 20  # 转换为百分制
        
        comprehensive_score = (efficiency_score * 0.3 + safety_score * 0.3 + 
                             popularity_score * 0.2 + rating_score * 0.2)
        
        print(f"\n{config.scheme_name}:")
        print(f"  - 效率评分: {efficiency_score:.1f}")
        print(f"  - 安全评分: {safety_score:.1f}")
        print(f"  - 受欢迎度: {popularity_score:.1f}")
        print(f"  - 用户评分: {rating_score:.1f}")
        print(f"  - 综合评分: {comprehensive_score:.1f}")
        
        # 给出建议
        if comprehensive_score >= 80:
            print(f"  - 建议: 优秀方案，推荐使用")
        elif comprehensive_score >= 70:
            print(f"  - 建议: 良好方案，可以使用")
        else:
            print(f"  - 建议: 需要优化改进")


if __name__ == "__main__":
    asyncio.run(demonstrate_configuration_model_usage())
    asyncio.run(demonstrate_scheme_version_usage())
    asyncio.run(demonstrate_configuration_queries())
    asyncio.run(demonstrate_configuration_analysis())
