#!/usr/bin/env python3
"""
飞机配置服务层使用示例

演示AircraftService的初始化和主要方法调用
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.services.aircraft import AircraftService
from src.schemas.aircraft import (
    AircraftCreateSchema,
    AircraftUpdateSchema,
    CrewRequirementsSchema,
    MaintenanceScheduleSchema
)
from src.database.models.aircraft import AircraftModelEnum, AircraftStatusEnum
from src.database.connection import get_database_session


async def demonstrate_aircraft_service_usage():
    """
    演示AircraftService的完整使用流程
    """
    print("=== AircraftService 使用示例 ===\n")
    
    async for db_session in get_database_session():
        try:
            # 1. 初始化服务
            print("1. 初始化AircraftService")
            aircraft_service = AircraftService(db_session)
            print("✅ AircraftService初始化完成")
            
            # 2. 创建飞机配置
            print("\n2. 创建新飞机配置")
            
            # 构建创建数据
            create_data = AircraftCreateSchema(
                aircraft_model=AircraftModelEnum.Y_8,
                quantity=2,
                payload_capacity=20000,       # 载重能力（kg）
                operational_range=5700,       # 作战半径（km）
                cruise_speed=500,             # 巡航速度（km/h）
                fuel_consumption=1800,        # 燃油消耗（L/h）
                loading_time=40,              # 装载时间（分钟）
                unloading_time=30,            # 卸载时间（分钟）
                ground_taxi_time=15,          # 地面滑行时间（分钟）
                crew_requirements=CrewRequirementsSchema(
                    pilots=2,                 # 飞行员数量
                    flight_engineers=0,       # 飞行工程师数量
                    loadmasters=1             # 装载员数量
                ),
                compatible_equipment=[
                    "技术文档示例装载车-DOC001",
                    "叉车-FC002"
                ],
                maintenance_schedule=MaintenanceScheduleSchema(
                    routine_maintenance_hours=100,
                    major_maintenance_hours=1000,
                    last_maintenance_date="2025-07-15"
                ),
                status=AircraftStatusEnum.AVAILABLE,
                remarks="技术文档演示用运-8飞机配置"
            )
            
            # 调用创建方法
            created_aircraft = await aircraft_service.create_aircraft_config(create_data)
            aircraft_id = created_aircraft.id
            
            print(f"✅ 飞机配置创建成功")
            print(f"   飞机ID: {aircraft_id}")
            print(f"   飞机型号: {created_aircraft.aircraft_model}")
            print(f"   飞机数量: {created_aircraft.quantity}")
            print(f"   载重能力: {created_aircraft.payload_capacity}kg")
            print(f"   作战半径: {created_aircraft.operational_range}km")
            print(f"   创建时间: {created_aircraft.created_at}")
            
            # 3. 获取飞机配置详情
            print("\n3. 获取飞机配置详情")
            aircraft_detail = await aircraft_service.get_aircraft_config_by_id(aircraft_id)
            
            if aircraft_detail:
                print(f"✅ 飞机配置详情获取成功")
                print(f"   飞机型号: {aircraft_detail.aircraft_model}")
                print(f"   巡航速度: {aircraft_detail.cruise_speed}km/h")
                print(f"   燃油消耗: {aircraft_detail.fuel_consumption}L/h")
                print(f"   装载时间: {aircraft_detail.loading_time}分钟")
                print(f"   卸载时间: {aircraft_detail.unloading_time}分钟")
                print(f"   飞机状态: {aircraft_detail.status}")
                
                # 显示机组人员需求
                crew = aircraft_detail.crew_requirements
                print(f"   机组人员需求:")
                print(f"     - 飞行员: {crew.get('pilots')}人")
                print(f"     - 飞行工程师: {crew.get('flight_engineers')}人")
                print(f"     - 装载员: {crew.get('loadmasters')}人")
                
                # 显示兼容设备
                compatible_eq = aircraft_detail.compatible_equipment
                print(f"   兼容设备: {', '.join(compatible_eq)}")
            else:
                print("❌ 飞机配置不存在")
            
            # 4. 获取飞机配置列表
            print("\n4. 获取飞机配置列表")
            aircraft_list = await aircraft_service.get_aircraft_config_list(
                page=1,
                page_size=10,
                aircraft_model=AircraftModelEnum.Y_8,
                status=AircraftStatusEnum.AVAILABLE,
                search_keyword="技术文档"
            )
            
            print(f"✅ 飞机配置列表获取成功")
            print(f"   总数量: {aircraft_list.total}")
            print(f"   当前页: {aircraft_list.page}")
            print(f"   每页数量: {aircraft_list.page_size}")
            print(f"   总页数: {aircraft_list.total_pages}")
            print(f"   飞机配置数量: {len(aircraft_list.aircraft_configs)}")
            
            for aircraft in aircraft_list.aircraft_configs:
                print(f"   - {aircraft.aircraft_model} (数量: {aircraft.quantity}, 载重: {aircraft.payload_capacity}kg)")
            
            # 5. 更新飞机配置
            print("\n5. 更新飞机配置")
            update_data = AircraftUpdateSchema(
                quantity=3,  # 增加数量
                fuel_consumption=1750,  # 优化燃油消耗
                loading_time=35,  # 优化装载时间
                unloading_time=25,  # 优化卸载时间
                crew_requirements=CrewRequirementsSchema(
                    pilots=2,
                    flight_engineers=0,
                    loadmasters=2  # 增加装载员
                ),
                compatible_equipment=[
                    "技术文档示例装载车-DOC001",
                    "叉车-FC002",
                    "牵引车-TC003"  # 新增兼容设备
                ],
                maintenance_schedule=MaintenanceScheduleSchema(
                    routine_maintenance_hours=100,
                    major_maintenance_hours=1000,
                    last_maintenance_date="2025-07-20"  # 更新维护日期
                ),
                status=AircraftStatusEnum.MAINTENANCE,  # 更改状态
                remarks="已优化的技术文档演示用运-8飞机配置"
            )
            
            updated_aircraft = await aircraft_service.update_aircraft_config(
                aircraft_id, update_data
            )
            
            if updated_aircraft:
                print(f"✅ 飞机配置更新成功")
                print(f"   新数量: {updated_aircraft.quantity}")
                print(f"   新燃油消耗: {updated_aircraft.fuel_consumption}L/h")
                print(f"   新装载时间: {updated_aircraft.loading_time}分钟")
                print(f"   新状态: {updated_aircraft.status}")
                print(f"   更新时间: {updated_aircraft.updated_at}")
                
                # 显示更新后的机组需求
                new_crew = updated_aircraft.crew_requirements
                print(f"   更新后机组需求: 飞行员{new_crew.get('pilots')}人, 装载员{new_crew.get('loadmasters')}人")
            else:
                print("❌ 飞机配置更新失败")
            
            # 6. 验证飞机配置参数
            print("\n6. 验证飞机配置参数")
            validation_errors = await aircraft_service.validate_aircraft_parameters(
                create_data
            )
            
            if validation_errors:
                print("❌ 飞机配置参数验证失败:")
                for error in validation_errors:
                    print(f"   - {error}")
            else:
                print("✅ 飞机配置参数验证通过")
            
            # 7. 计算飞机性能指标
            print("\n7. 计算飞机性能指标")
            if updated_aircraft:
                total_payload = updated_aircraft.payload_capacity * updated_aircraft.quantity
                total_fuel_consumption = updated_aircraft.fuel_consumption * updated_aircraft.quantity
                
                # 计算单次任务时间和成本
                flight_time_hours = updated_aircraft.operational_range / updated_aircraft.cruise_speed * 2  # 往返
                ground_time_hours = (updated_aircraft.loading_time + updated_aircraft.unloading_time + 
                                   updated_aircraft.ground_taxi_time * 2) / 60
                total_mission_time = flight_time_hours + ground_time_hours
                mission_fuel_cost = total_fuel_consumption * total_mission_time
                
                print(f"性能指标计算:")
                print(f"   - 总载重能力: {total_payload}kg")
                print(f"   - 单次任务时间: {total_mission_time:.1f}小时")
                print(f"   - 单次任务燃油消耗: {mission_fuel_cost:.0f}L")
                print(f"   - 载重效率: {total_payload/mission_fuel_cost:.1f}kg/L")
                print(f"   - 时间效率: {total_payload/total_mission_time:.0f}kg/h")
            
            # 8. 删除飞机配置
            print("\n8. 删除飞机配置")
            delete_success = await aircraft_service.delete_aircraft_config(aircraft_id)
            
            if delete_success:
                print("✅ 飞机配置删除成功")
                
                # 验证删除
                deleted_aircraft = await aircraft_service.get_aircraft_config_by_id(aircraft_id)
                if deleted_aircraft is None:
                    print("✅ 确认飞机配置已从数据库中删除")
            else:
                print("❌ 飞机配置删除失败")
            
        except Exception as e:
            print(f"❌ 操作过程中发生错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


async def demonstrate_aircraft_service_validation():
    """
    演示AircraftService的参数验证功能
    """
    print("\n=== AircraftService 参数验证示例 ===\n")
    
    async for db_session in get_database_session():
        try:
            aircraft_service = AircraftService(db_session)
            
            # 1. 测试有效的飞机配置
            print("1. 测试有效的飞机配置")
            valid_data = AircraftCreateSchema(
                aircraft_model=AircraftModelEnum.Y_20,
                quantity=1,
                payload_capacity=60000,  # 在运-20规格范围内
                operational_range=4000,  # 在运-20规格范围内
                cruise_speed=650,        # 在运-20规格范围内
                fuel_consumption=3200,
                loading_time=50,
                unloading_time=40,
                ground_taxi_time=18,
                crew_requirements=CrewRequirementsSchema(
                    pilots=2,
                    flight_engineers=1,  # 运-20需要飞行工程师
                    loadmasters=2
                ),
                maintenance_schedule=MaintenanceScheduleSchema(
                    routine_maintenance_hours=100,
                    major_maintenance_hours=1000
                )
            )
            
            valid_errors = await aircraft_service.validate_aircraft_parameters(valid_data)
            
            if valid_errors:
                print("❌ 有效配置验证失败:")
                for error in valid_errors:
                    print(f"   - {error}")
            else:
                print("✅ 有效配置验证通过")
            
            # 2. 测试无效的飞机配置
            print("\n2. 测试无效的飞机配置")
            invalid_data = AircraftCreateSchema(
                aircraft_model=AircraftModelEnum.Y_20,
                quantity=1,
                payload_capacity=80000,  # 超过运-20最大载重
                operational_range=6000,  # 超过运-20最大航程
                cruise_speed=1000,       # 超过运-20最大速度
                fuel_consumption=3200,
                loading_time=5,          # 装载时间过短
                unloading_time=5,        # 卸载时间过短
                ground_taxi_time=18,
                crew_requirements=CrewRequirementsSchema(
                    pilots=1,            # 飞行员数量不足
                    flight_engineers=0,  # 运-20缺少飞行工程师
                    loadmasters=2
                ),
                maintenance_schedule=MaintenanceScheduleSchema(
                    routine_maintenance_hours=100,
                    major_maintenance_hours=1000
                )
            )
            
            invalid_errors = await aircraft_service.validate_aircraft_parameters(invalid_data)
            
            if invalid_errors:
                print("✅ 正确识别了无效配置:")
                for error in invalid_errors:
                    print(f"   - {error}")
            else:
                print("❌ 未能识别无效配置")
            
            # 3. 测试不同机型的验证
            print("\n3. 测试不同机型的验证")
            
            # 运-7配置（小型机不需要飞行工程师）
            y7_data = AircraftCreateSchema(
                aircraft_model=AircraftModelEnum.Y_7,
                quantity=1,
                payload_capacity=5000,
                operational_range=1200,
                cruise_speed=320,
                fuel_consumption=750,
                loading_time=20,
                unloading_time=15,
                ground_taxi_time=8,
                crew_requirements=CrewRequirementsSchema(
                    pilots=2,
                    flight_engineers=0,  # 运-7不需要飞行工程师
                    loadmasters=1
                ),
                maintenance_schedule=MaintenanceScheduleSchema(
                    routine_maintenance_hours=80,
                    major_maintenance_hours=800
                )
            )
            
            y7_errors = await aircraft_service.validate_aircraft_parameters(y7_data)
            
            if y7_errors:
                print("❌ 运-7配置验证失败:")
                for error in y7_errors:
                    print(f"   - {error}")
            else:
                print("✅ 运-7配置验证通过")
            
        except Exception as e:
            print(f"❌ 参数验证测试中发生错误: {e}")
        finally:
            await db_session.close()
            break


async def demonstrate_aircraft_service_performance_analysis():
    """
    演示AircraftService的性能分析功能
    """
    print("\n=== AircraftService 性能分析示例 ===\n")
    
    async for db_session in get_database_session():
        try:
            aircraft_service = AircraftService(db_session)
            
            # 创建不同型号的飞机配置进行对比
            aircraft_configs = [
                {
                    "name": "运-20重型运输机",
                    "data": AircraftCreateSchema(
                        aircraft_model=AircraftModelEnum.Y_20,
                        quantity=1,
                        payload_capacity=66000,
                        operational_range=4400,
                        cruise_speed=700,
                        fuel_consumption=3500,
                        loading_time=60,
                        unloading_time=45,
                        ground_taxi_time=20,
                        crew_requirements=CrewRequirementsSchema(pilots=2, flight_engineers=1, loadmasters=2),
                        maintenance_schedule=MaintenanceScheduleSchema(
                            routine_maintenance_hours=100,
                            major_maintenance_hours=1000
                        )
                    )
                },
                {
                    "name": "运-8中型运输机",
                    "data": AircraftCreateSchema(
                        aircraft_model=AircraftModelEnum.Y_8,
                        quantity=1,
                        payload_capacity=20000,
                        operational_range=5700,
                        cruise_speed=500,
                        fuel_consumption=1800,
                        loading_time=40,
                        unloading_time=30,
                        ground_taxi_time=15,
                        crew_requirements=CrewRequirementsSchema(pilots=2, flight_engineers=0, loadmasters=1),
                        maintenance_schedule=MaintenanceScheduleSchema(
                            routine_maintenance_hours=100,
                            major_maintenance_hours=1000
                        )
                    )
                }
            ]
            
            created_ids = []
            
            print("1. 创建测试飞机配置并进行性能分析")
            
            for config_info in aircraft_configs:
                # 创建配置
                created_aircraft = await aircraft_service.create_aircraft_config(config_info["data"])
                created_ids.append(created_aircraft.id)
                
                # 性能分析
                data = config_info["data"]
                
                # 计算关键性能指标
                crew_count = sum(data.crew_requirements.model_dump().values())
                payload_per_crew = data.payload_capacity / crew_count
                speed_efficiency = data.cruise_speed / data.fuel_consumption
                fuel_efficiency = data.payload_capacity / data.fuel_consumption
                
                # 计算任务时间
                flight_time = data.operational_range / data.cruise_speed * 2  # 往返
                ground_time = (data.loading_time + data.unloading_time + data.ground_taxi_time * 2) / 60
                total_time = flight_time + ground_time
                
                print(f"\n{config_info['name']} 性能分析:")
                print(f"   - 载重能力: {data.payload_capacity}kg")
                print(f"   - 机组人员: {crew_count}人")
                print(f"   - 人均载重: {payload_per_crew:.0f}kg/人")
                print(f"   - 速度效率: {speed_efficiency:.2f}km/L")
                print(f"   - 燃油效率: {fuel_efficiency:.1f}kg/L")
                print(f"   - 任务时间: {total_time:.1f}小时")
                print(f"   - 时间效率: {data.payload_capacity/total_time:.0f}kg/h")
            
            # 清理测试数据
            print(f"\n2. 清理测试数据")
            for aircraft_id in created_ids:
                await aircraft_service.delete_aircraft_config(aircraft_id)
            print("✅ 测试数据清理完成")
            
        except Exception as e:
            print(f"❌ 性能分析过程中发生错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


if __name__ == "__main__":
    asyncio.run(demonstrate_aircraft_service_usage())
    asyncio.run(demonstrate_aircraft_service_validation())
    asyncio.run(demonstrate_aircraft_service_performance_analysis())
