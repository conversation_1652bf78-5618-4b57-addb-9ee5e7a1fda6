#!/usr/bin/env python3
"""
场景数据模型使用示例

演示Scenario模型的创建、属性访问和方法调用
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.models.scenario import (
    Scenario, 
    TaskTypeEnum, 
    ScenarioStatusEnum, 
    ThreatLevelEnum
)
from src.database.connection import get_database_session


async def demonstrate_scenario_model_usage():
    """
    演示Scenario模型的使用方法
    """
    print("=== Scenario 数据模型使用示例 ===\n")
    
    # 1. 创建场景实例
    print("1. 创建场景实例")
    scenario = Scenario(
        scenario_name="重型装备战略投送场景",
        scenario_type="strategic_transport",
        description="大型重型装备的远程战略投送任务场景",
        task_type=TaskTypeEnum.EQUIPMENT_TRANSPORT,
        environment_conditions={
            "weather": {
                "visibility": 10000,  # 能见度（米）
                "wind_speed": 15,     # 风速（米/秒）
                "precipitation": 0,   # 降水量（毫米/小时）
                "temperature": 25,    # 温度（摄氏度）
                "humidity": 60        # 湿度（百分比）
            },
            "terrain": "plain",       # 地形类型
            "airspace_control": {
                "control_level": "normal"  # 空域管制等级
            }
        },
        resource_constraints={
            "transport_capacity": {
                "max_aircraft": 5,    # 最大飞机数量
                "max_equipment": 10   # 最大设备数量
            },
            "time_window": {
                "max_duration": 24    # 最大持续时间（小时）
            }
        },
        mission_requirements={
            "cargo_weight": 180000,   # 货物重量（kg）
            "origin": "base_001",     # 出发地
            "destination": "base_002", # 目的地
            "priority": "high"        # 优先级
        },
        threat_factors={
            "enemy_threats": {
                "air_defense": "medium"  # 防空威胁等级
            },
            "threat_probabilities": {
                "air_defense_threat": 0.3  # 防空威胁概率
            }
        },
        threat_level=ThreatLevelEnum.MEDIUM,
        created_by="system_admin",
        status=ScenarioStatusEnum.DRAFT
    )
    
    print(f"场景名称: {scenario.scenario_name}")
    print(f"场景类型: {scenario.scenario_type}")
    print(f"任务类型: {scenario.task_type}")
    print(f"威胁等级: {scenario.threat_level}")
    print(f"状态: {scenario.status}")
    
    # 2. 访问复杂字段
    print("\n2. 访问复杂字段")
    weather_info = scenario.environment_conditions.get("weather", {})
    print(f"天气条件:")
    print(f"  - 能见度: {weather_info.get('visibility')}米")
    print(f"  - 风速: {weather_info.get('wind_speed')}米/秒")
    print(f"  - 温度: {weather_info.get('temperature')}°C")
    
    mission_req = scenario.mission_requirements
    print(f"任务需求:")
    print(f"  - 货物重量: {mission_req.get('cargo_weight')}kg")
    print(f"  - 出发地: {mission_req.get('origin')}")
    print(f"  - 目的地: {mission_req.get('destination')}")
    
    # 3. 使用模型方法
    print("\n3. 使用模型方法")
    print(f"字符串表示: {scenario}")
    
    # 转换为字典
    scenario_dict = scenario.to_dict()
    print(f"字典格式包含字段: {list(scenario_dict.keys())}")
    
    # 4. 数据库操作示例
    print("\n4. 数据库操作示例")
    async for db_session in get_database_session():
        try:
            # 保存到数据库
            db_session.add(scenario)
            await db_session.commit()
            await db_session.refresh(scenario)
            
            print(f"场景已保存到数据库，ID: {scenario.id}")
            print(f"创建时间: {scenario.created_at}")
            print(f"更新时间: {scenario.updated_at}")
            
            # 更新场景状态
            scenario.status = ScenarioStatusEnum.ACTIVE
            await db_session.commit()
            print(f"场景状态已更新为: {scenario.status}")
            
        except Exception as e:
            print(f"数据库操作错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


async def demonstrate_scenario_queries():
    """
    演示场景查询操作
    """
    print("\n=== 场景查询操作示例 ===\n")
    
    from sqlalchemy import select, and_, or_
    
    async for db_session in get_database_session():
        try:
            # 1. 基本查询
            print("1. 查询所有激活状态的场景")
            query = select(Scenario).where(Scenario.status == ScenarioStatusEnum.ACTIVE)
            result = await db_session.execute(query)
            scenarios = result.scalars().all()
            
            print(f"找到 {len(scenarios)} 个激活场景:")
            for scenario in scenarios:
                print(f"  - {scenario.scenario_name} ({scenario.task_type})")
            
            # 2. 复合条件查询
            print("\n2. 查询高威胁等级的装备运输场景")
            query = select(Scenario).where(
                and_(
                    Scenario.task_type == TaskTypeEnum.EQUIPMENT_TRANSPORT,
                    Scenario.threat_level == ThreatLevelEnum.HIGH
                )
            )
            result = await db_session.execute(query)
            high_threat_scenarios = result.scalars().all()
            
            print(f"找到 {len(high_threat_scenarios)} 个高威胁装备运输场景")
            
            # 3. 模糊查询
            print("\n3. 按名称模糊查询场景")
            search_keyword = "战略"
            query = select(Scenario).where(
                Scenario.scenario_name.ilike(f"%{search_keyword}%")
            )
            result = await db_session.execute(query)
            search_results = result.scalars().all()
            
            print(f"包含'{search_keyword}'的场景:")
            for scenario in search_results:
                print(f"  - {scenario.scenario_name}")
            
        except Exception as e:
            print(f"查询操作错误: {e}")
        finally:
            await db_session.close()
            break


if __name__ == "__main__":
    asyncio.run(demonstrate_scenario_model_usage())
    asyncio.run(demonstrate_scenario_queries())
