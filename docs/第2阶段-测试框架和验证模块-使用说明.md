# 第2阶段 - 测试框架和验证模块使用说明

## 模块概述

测试框架和验证模块为第2阶段的效能计算引擎提供了完整的测试覆盖，包括单元测试、集成测试、功能验证和性能测试。该模块确保所有功能的正确性、稳定性和性能符合要求。

### 测试覆盖范围
- **单元测试**: 覆盖率达到90%以上
- **集成测试**: 验证模块间协作
- **功能验证**: 端到端功能测试
- **性能测试**: 算法性能和响应时间测试
- **数据完整性测试**: 数据库操作和约束验证

## 测试框架结构

### 1. 单元测试框架

#### 测试文件组织
```
tests/
├── test_efficiency_complete.py          # 效能指标模块完整测试
├── test_calculators.py                  # 计算器单元测试
├── test_services.py                     # 服务层单元测试
├── test_models.py                       # 数据模型测试
├── test_api_integration.py              # API集成测试
└── conftest.py                          # 测试配置和夹具
```

#### 测试夹具配置

```python
# conftest.py - 测试配置文件
import pytest
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from src.database.models.base import BaseModel
from src.database.connection import get_database_session

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环夹具"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
async def db_session():
    """数据库会话夹具"""
    # 使用测试数据库
    engine = create_async_engine(
        "postgresql+asyncpg://test_user:test_pass@localhost/test_db",
        echo=False
    )
    
    # 创建表结构
    async with engine.begin() as conn:
        await conn.run_sync(BaseModel.metadata.create_all)
    
    # 创建会话
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session() as session:
        yield session
        await session.rollback()  # 回滚测试数据
    
    await engine.dispose()

@pytest.fixture
def sample_indicator_data():
    """示例指标数据夹具"""
    from src.schemas.efficiency import EfficiencyIndicatorCreateSchema
    from src.database.models.efficiency import IndicatorCategoryEnum
    from decimal import Decimal
    
    return EfficiencyIndicatorCreateSchema(
        indicator_name="测试指标",
        indicator_code="TEST_INDICATOR",
        category=IndicatorCategoryEnum.TIMELINESS,
        description="用于测试的效能指标",
        unit="小时",
        min_threshold=Decimal('0'),
        max_threshold=Decimal('24'),
        target_value=Decimal('8'),
        default_weight=Decimal('0.25'),
        priority_level=1,
        created_by="测试用户"
    )
```

### 2. 完整的测试用例示例

#### 效能指标服务测试

```python
# test_efficiency_service.py
import pytest
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from src.services.efficiency import EfficiencyIndicatorService
from src.schemas.efficiency import EfficiencyIndicatorCreateSchema, EfficiencyIndicatorUpdateSchema
from src.database.models.efficiency import IndicatorCategoryEnum, IndicatorStatusEnum

class TestEfficiencyIndicatorService:
    """效能指标服务测试类"""
    
    @pytest.mark.asyncio
    async def test_create_indicator_with_valid_data_returns_indicator(
        self, db_session: AsyncSession, sample_indicator_data: EfficiencyIndicatorCreateSchema
    ):
        """测试创建效能指标：使用有效数据应返回指标信息"""
        # 准备
        service = EfficiencyIndicatorService(db_session)
        
        # 执行
        result = await service.create_indicator(sample_indicator_data)
        
        # 验证
        assert result.indicator_name == sample_indicator_data.indicator_name, "指标名称应该正确设置"
        assert result.indicator_code == sample_indicator_data.indicator_code, "指标编码应该正确设置"
        assert result.category == sample_indicator_data.category, "指标分类应该正确设置"
        assert result.status == IndicatorStatusEnum.ACTIVE, "新创建的指标应该是激活状态"
        assert result.id is not None, "指标ID应该被自动生成"
        assert result.created_at is not None, "创建时间应该被自动设置"
    
    @pytest.mark.asyncio
    async def test_create_indicator_with_duplicate_name_raises_error(
        self, db_session: AsyncSession, sample_indicator_data: EfficiencyIndicatorCreateSchema
    ):
        """测试创建效能指标：重复名称应抛出错误"""
        # 准备
        service = EfficiencyIndicatorService(db_session)
        
        # 先创建一个指标
        await service.create_indicator(sample_indicator_data)
        
        # 尝试创建同名指标
        duplicate_data = EfficiencyIndicatorCreateSchema(
            indicator_name=sample_indicator_data.indicator_name,  # 相同名称
            indicator_code="DIFFERENT_CODE",  # 不同编码
            category=IndicatorCategoryEnum.CAPABILITY,
            unit="百分比"
        )
        
        # 执行和验证
        with pytest.raises(ValueError, match="指标名称或编码已存在"):
            await service.create_indicator(duplicate_data)
    
    @pytest.mark.asyncio
    async def test_get_indicator_by_id_with_existing_id_returns_indicator(
        self, db_session: AsyncSession, sample_indicator_data: EfficiencyIndicatorCreateSchema
    ):
        """测试根据ID获取指标：存在的ID应返回指标信息"""
        # 准备
        service = EfficiencyIndicatorService(db_session)
        created_indicator = await service.create_indicator(sample_indicator_data)
        
        # 执行
        result = await service.get_indicator_by_id(created_indicator.id)
        
        # 验证
        assert result is not None, "应该返回指标信息"
        assert result.id == created_indicator.id, "返回的指标ID应该匹配"
        assert result.indicator_name == created_indicator.indicator_name, "指标名称应该匹配"
    
    @pytest.mark.asyncio
    async def test_get_indicator_by_id_with_nonexistent_id_returns_none(
        self, db_session: AsyncSession
    ):
        """测试根据ID获取指标：不存在的ID应返回None"""
        # 准备
        service = EfficiencyIndicatorService(db_session)
        nonexistent_id = "00000000-0000-0000-0000-000000000000"
        
        # 执行
        result = await service.get_indicator_by_id(nonexistent_id)
        
        # 验证
        assert result is None, "不存在的ID应该返回None"
    
    @pytest.mark.asyncio
    async def test_get_indicators_by_category_returns_filtered_list(
        self, db_session: AsyncSession
    ):
        """测试按分类获取指标：应返回过滤后的指标列表"""
        # 准备
        service = EfficiencyIndicatorService(db_session)
        
        # 创建不同分类的指标
        timeliness_data = EfficiencyIndicatorCreateSchema(
            indicator_name="时效性指标",
            indicator_code="TIMELINESS_TEST",
            category=IndicatorCategoryEnum.TIMELINESS,
            unit="小时"
        )
        capability_data = EfficiencyIndicatorCreateSchema(
            indicator_name="能力指标",
            indicator_code="CAPABILITY_TEST",
            category=IndicatorCategoryEnum.CAPABILITY,
            unit="百分比"
        )
        
        await service.create_indicator(timeliness_data)
        await service.create_indicator(capability_data)
        
        # 执行
        timeliness_indicators = await service.get_indicators_by_category(
            IndicatorCategoryEnum.TIMELINESS
        )
        
        # 验证
        assert len(timeliness_indicators) >= 1, "应该返回至少一个时效性指标"
        for indicator in timeliness_indicators:
            assert indicator.category == IndicatorCategoryEnum.TIMELINESS, \
                "所有返回的指标都应该是时效性分类"
            assert indicator.status == IndicatorStatusEnum.ACTIVE, \
                "所有返回的指标都应该是激活状态"
    
    @pytest.mark.asyncio
    async def test_update_indicator_with_valid_data_returns_updated_indicator(
        self, db_session: AsyncSession, sample_indicator_data: EfficiencyIndicatorCreateSchema
    ):
        """测试更新效能指标：使用有效数据应返回更新后的指标"""
        # 准备
        service = EfficiencyIndicatorService(db_session)
        created_indicator = await service.create_indicator(sample_indicator_data)
        
        update_data = EfficiencyIndicatorUpdateSchema(
            description="更新后的描述",
            target_value=Decimal('10.00'),
            priority_level=3
        )
        
        # 执行
        updated_indicator = await service.update_indicator(created_indicator.id, update_data)
        
        # 验证
        assert updated_indicator is not None, "更新应该成功"
        assert updated_indicator.description == "更新后的描述", "描述应该被更新"
        assert updated_indicator.target_value == Decimal('10.00'), "目标值应该被更新"
        assert updated_indicator.priority_level == 3, "优先级应该被更新"
        assert updated_indicator.updated_at > updated_indicator.created_at, "更新时间应该晚于创建时间"
    
    @pytest.mark.asyncio
    async def test_delete_indicator_sets_status_to_deprecated(
        self, db_session: AsyncSession, sample_indicator_data: EfficiencyIndicatorCreateSchema
    ):
        """测试删除效能指标：应将状态设置为已弃用"""
        # 准备
        service = EfficiencyIndicatorService(db_session)
        created_indicator = await service.create_indicator(sample_indicator_data)
        
        # 执行
        success = await service.delete_indicator(created_indicator.id)
        
        # 验证
        assert success is True, "删除操作应该成功"
        
        # 验证指标状态已更改
        deleted_indicator = await service.get_indicator_by_id(created_indicator.id)
        assert deleted_indicator is not None, "指标记录应该仍然存在"
        assert deleted_indicator.status == IndicatorStatusEnum.DEPRECATED, \
            "指标状态应该被设置为已弃用"


class TestEfficiencyCalculators:
    """效能计算器测试类"""
    
    def test_calculate_task_completion_time_with_valid_times_returns_duration(self):
        """测试任务完成时间计算：使用有效时间应返回持续时间"""
        from datetime import datetime
        from src.services.efficiency_calculator import TimelinessCalculator
        
        # 准备
        start_time = datetime(2024, 1, 1, 9, 0, 0)   # 上午9点
        end_time = datetime(2024, 1, 1, 17, 30, 0)   # 下午5点30分
        
        # 执行
        result = TimelinessCalculator.calculate_task_completion_time(start_time, end_time)
        
        # 验证
        expected_hours = Decimal('8.50')  # 8.5小时
        assert result == expected_hours, f"8.5小时的任务应该返回{expected_hours}，实际返回{result}"
    
    def test_calculate_task_completion_time_with_invalid_times_raises_error(self):
        """测试任务完成时间计算：无效时间应抛出错误"""
        from datetime import datetime
        from src.services.efficiency_calculator import TimelinessCalculator
        
        # 准备
        start_time = datetime(2024, 1, 1, 17, 0, 0)  # 下午5点
        end_time = datetime(2024, 1, 1, 9, 0, 0)     # 上午9点（错误的顺序）
        
        # 执行和验证
        with pytest.raises(ValueError, match="结束时间必须晚于开始时间"):
            TimelinessCalculator.calculate_task_completion_time(start_time, end_time)
    
    def test_calculate_average_response_time_with_valid_data_returns_average(self):
        """测试平均响应时间计算：使用有效数据应返回平均值"""
        from src.services.efficiency_calculator import TimelinessCalculator
        
        # 准备
        response_times = [1.5, 2.0, 1.8, 2.2, 1.9]  # 小时
        
        # 执行
        result = TimelinessCalculator.calculate_average_response_time(response_times)
        
        # 验证
        expected_average = Decimal('1.88')  # (1.5+2.0+1.8+2.2+1.9)/5 = 1.88
        assert result == expected_average, f"平均响应时间应该是{expected_average}，实际是{result}"
    
    def test_calculate_average_response_time_with_empty_list_returns_zero(self):
        """测试平均响应时间计算：空列表应返回零"""
        from src.services.efficiency_calculator import TimelinessCalculator
        
        # 准备
        response_times = []
        
        # 执行
        result = TimelinessCalculator.calculate_average_response_time(response_times)
        
        # 验证
        assert result == Decimal('0'), "空列表应该返回0"
    
    @pytest.mark.parametrize("total_tasks,on_time_tasks,expected_rate", [
        (100, 95, Decimal('95.00')),
        (50, 50, Decimal('100.00')),
        (200, 180, Decimal('90.00')),
        (0, 0, Decimal('0')),
    ])
    def test_calculate_on_time_delivery_rate_with_various_inputs(
        self, total_tasks, on_time_tasks, expected_rate
    ):
        """测试准时交付率计算：使用各种输入数据"""
        from src.services.efficiency_calculator import TimelinessCalculator
        
        # 执行
        result = TimelinessCalculator.calculate_on_time_delivery_rate(total_tasks, on_time_tasks)
        
        # 验证
        assert result == expected_rate, \
            f"总任务{total_tasks}，准时{on_time_tasks}，期望{expected_rate}%，实际{result}%"


class TestIntegrationScenarios:
    """集成测试场景"""
    
    @pytest.mark.asyncio
    async def test_complete_efficiency_evaluation_workflow(self, db_session: AsyncSession):
        """测试完整的效能评估工作流程"""
        from src.services.efficiency import (
            EfficiencyIndicatorService, 
            IndicatorWeightConfigService
        )
        from src.services.efficiency_evaluator import EfficiencyEvaluator
        from src.schemas.efficiency import (
            EfficiencyIndicatorCreateSchema,
            IndicatorWeightConfigCreateSchema
        )
        from src.database.models.efficiency import IndicatorCategoryEnum
        
        # 准备服务
        indicator_service = EfficiencyIndicatorService(db_session)
        weight_service = IndicatorWeightConfigService(db_session)
        evaluator = EfficiencyEvaluator(db_session)
        
        # 步骤1: 创建效能指标
        indicator_data = EfficiencyIndicatorCreateSchema(
            indicator_name="集成测试指标",
            indicator_code="INTEGRATION_TEST",
            category=IndicatorCategoryEnum.TIMELINESS,
            unit="小时",
            target_value=Decimal('8.00')
        )
        
        created_indicator = await indicator_service.create_indicator(indicator_data)
        assert created_indicator.id is not None, "指标应该创建成功"
        
        # 步骤2: 创建权重配置
        weight_config_data = IndicatorWeightConfigCreateSchema(
            config_name="集成测试权重配置",
            scenario_type="integration_test",
            weight_settings={
                "timeliness": Decimal('0.40'),
                "capability": Decimal('0.25'),
                "economy": Decimal('0.15'),
                "robustness": Decimal('0.15'),
                "safety": Decimal('0.05')
            },
            is_default=True
        )
        
        weight_config = await weight_service.create_weight_config(weight_config_data)
        assert weight_config.id is not None, "权重配置应该创建成功"
        
        # 步骤3: 测试加权评分计算
        indicator_values = {
            "test_indicator_1": Decimal('85.00'),
            "test_indicator_2": Decimal('90.00'),
            "test_indicator_3": Decimal('78.00')
        }
        
        weights = {
            "test_indicator_1": Decimal('0.40'),
            "test_indicator_2": Decimal('0.35'),
            "test_indicator_3": Decimal('0.25')
        }
        
        weighted_score = evaluator.calculate_weighted_score(indicator_values, weights)
        
        # 验证计算结果
        expected_score = (
            Decimal('85.00') * Decimal('0.40') +
            Decimal('90.00') * Decimal('0.35') +
            Decimal('78.00') * Decimal('0.25')
        )
        
        assert abs(weighted_score - expected_score) < Decimal('0.01'), \
            f"加权评分计算错误，期望{expected_score}，实际{weighted_score}"
        
        print(f"集成测试完成 - 加权评分: {weighted_score}")


# 运行测试的示例命令
"""
# 运行所有测试
pytest tests/ -v

# 运行特定测试文件
pytest tests/test_efficiency_complete.py -v

# 运行特定测试类
pytest tests/test_efficiency_complete.py::TestEfficiencyIndicatorService -v

# 运行特定测试方法
pytest tests/test_efficiency_complete.py::TestEfficiencyIndicatorService::test_create_indicator_with_valid_data_returns_indicator -v

# 生成覆盖率报告
pytest tests/ --cov=src --cov-report=html

# 运行性能测试
pytest tests/ -m performance -v
"""
```

### 3. 功能验证脚本

```python
# scripts/validate_efficiency_functionality.py
import asyncio
import sys
from pathlib import Path
from decimal import Decimal
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.connection import get_database_session
from src.services.efficiency import EfficiencyIndicatorService
from src.services.efficiency_calculator import TimelinessCalculator

class FunctionalityValidator:
    """功能验证器"""
    
    def __init__(self, db_session):
        self.db_session = db_session
        self.indicator_service = EfficiencyIndicatorService(db_session)
    
    async def validate_all_functionality(self):
        """验证所有功能"""
        print("开始功能验证...")
        
        try:
            # 验证数据库连接
            await self.validate_database_connection()
            print("✅ 数据库连接验证通过")
            
            # 验证指标管理功能
            await self.validate_indicator_management()
            print("✅ 指标管理功能验证通过")
            
            # 验证计算器功能
            self.validate_calculators()
            print("✅ 计算器功能验证通过")
            
            print("🎉 所有功能验证成功！")
            
        except Exception as e:
            print(f"❌ 功能验证失败: {e}")
            raise
    
    async def validate_database_connection(self):
        """验证数据库连接"""
        # 尝试执行简单查询
        from sqlalchemy import text
        result = await self.db_session.execute(text("SELECT 1"))
        assert result.scalar() == 1, "数据库连接失败"
    
    async def validate_indicator_management(self):
        """验证指标管理功能"""
        from src.schemas.efficiency import EfficiencyIndicatorCreateSchema
        from src.database.models.efficiency import IndicatorCategoryEnum
        
        # 创建测试指标
        test_data = EfficiencyIndicatorCreateSchema(
            indicator_name="验证测试指标",
            indicator_code="VALIDATION_TEST",
            category=IndicatorCategoryEnum.TIMELINESS,
            unit="小时",
            target_value=Decimal('8.00')
        )
        
        # 测试创建
        created = await self.indicator_service.create_indicator(test_data)
        assert created.indicator_name == "验证测试指标", "指标创建失败"
        
        # 测试查询
        retrieved = await self.indicator_service.get_indicator_by_id(created.id)
        assert retrieved is not None, "指标查询失败"
        
        # 测试删除
        success = await self.indicator_service.delete_indicator(created.id)
        assert success is True, "指标删除失败"
    
    def validate_calculators(self):
        """验证计算器功能"""
        # 测试时效性计算器
        start_time = datetime(2024, 1, 1, 9, 0, 0)
        end_time = datetime(2024, 1, 1, 17, 0, 0)
        
        completion_time = TimelinessCalculator.calculate_task_completion_time(
            start_time, end_time
        )
        
        assert completion_time == Decimal('8.00'), "任务完成时间计算错误"


async def main():
    """主函数"""
    async for db_session in get_database_session():
        try:
            validator = FunctionalityValidator(db_session)
            await validator.validate_all_functionality()
        except Exception as e:
            print(f"验证失败: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
        break

if __name__ == "__main__":
    asyncio.run(main())
```

## 测试执行指南

### 1. 环境准备

```bash
# 安装测试依赖
pip install pytest pytest-asyncio pytest-cov

# 设置测试数据库
export TEST_DATABASE_URL="postgresql+asyncpg://test_user:test_pass@localhost/test_db"

# 初始化测试数据库
python scripts/init_test_db.py
```

### 2. 运行测试

```bash
# 运行所有测试
pytest tests/ -v

# 运行单元测试
pytest tests/test_efficiency_complete.py -v

# 运行集成测试
pytest tests/test_api_integration.py -v

# 生成覆盖率报告
pytest tests/ --cov=src --cov-report=html --cov-report=term

# 运行性能测试
pytest tests/ -m performance -v --benchmark-only
```

### 3. 功能验证

```bash
# 运行功能验证脚本
python scripts/validate_efficiency_functionality.py

# 生成测试数据
python scripts/generate_efficiency_test_data.py

# 运行完整验证流程
python scripts/run_complete_validation.py
```

## 测试最佳实践

### 1. 测试命名规范
- 使用描述性的测试方法名
- 格式：`test_方法名_条件_期望结果`
- 示例：`test_create_indicator_with_valid_data_returns_indicator`

### 2. 测试数据管理
- 使用测试夹具提供测试数据
- 每个测试后清理数据
- 不使用硬编码数据

### 3. 断言最佳实践
- 使用具体的断言方法
- 添加描述性错误消息
- 验证所有重要属性

### 4. 异常测试
- 测试所有可能的异常情况
- 验证异常类型和消息
- 确保错误处理的正确性

## 常见问题和故障排除

### Q1: 测试数据库连接失败
**解决方案**: 检查测试数据库配置，确保数据库服务正在运行。

### Q2: 测试覆盖率不足
**解决方案**: 添加边界条件和异常情况的测试用例。

### Q3: 异步测试执行失败
**解决方案**: 确保使用正确的pytest-asyncio标记和事件循环配置。

### Q4: 测试数据污染
**解决方案**: 在每个测试后正确回滚事务，使用独立的测试数据库。
