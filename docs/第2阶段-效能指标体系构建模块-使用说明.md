# 第2阶段 - 效能指标体系构建模块使用说明

## 模块概述

效能指标体系构建模块是第2阶段的核心模块之一，负责建立完整、可量化的效能评估指标体系。该模块提供了统一的评估标准，支持5大指标分类（时效性、能力、经济性、鲁棒性、安全性）和20个具体指标的管理。

### 主要功能
- 效能指标的CRUD操作管理
- 指标分类和权重配置
- 指标计算结果存储和查询
- 综合评估和权重管理

## 核心类详细说明

### 1. EfficiencyIndicatorService - 效能指标管理服务

#### 类的用途和职责
`EfficiencyIndicatorService` 是效能指标管理的核心服务类，提供指标的创建、查询、更新、删除等业务逻辑处理。

#### 构造函数
```python
def __init__(self, db_session: AsyncSession) -> None:
    """
    初始化效能指标服务
    
    Args:
        db_session: 数据库会话，用于执行数据库操作
    """
```

#### 主要方法说明

##### create_indicator()
```python
async def create_indicator(self, indicator_data: EfficiencyIndicatorCreateSchema) -> EfficiencyIndicatorResponseSchema:
    """
    创建新的效能指标
    
    Args:
        indicator_data: 指标创建数据，包含指标名称、编码、分类等信息
        
    Returns:
        EfficiencyIndicatorResponseSchema: 创建成功的指标信息
        
    Raises:
        ValueError: 当指标名称或编码已存在时抛出异常
    """
```

##### get_indicator_by_id()
```python
async def get_indicator_by_id(self, indicator_id: str) -> Optional[EfficiencyIndicatorResponseSchema]:
    """
    根据ID获取效能指标
    
    Args:
        indicator_id: 指标的唯一标识符
        
    Returns:
        Optional[EfficiencyIndicatorResponseSchema]: 指标信息，不存在时返回None
    """
```

##### get_indicators_by_category()
```python
async def get_indicators_by_category(self, category: IndicatorCategoryEnum) -> List[EfficiencyIndicatorResponseSchema]:
    """
    根据分类获取效能指标列表
    
    Args:
        category: 指标分类枚举值
        
    Returns:
        List[EfficiencyIndicatorResponseSchema]: 该分类下的所有激活指标列表
    """
```

#### 完整使用示例

```python
import asyncio
from decimal import Decimal
from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session
from src.services.efficiency import EfficiencyIndicatorService
from src.schemas.efficiency import EfficiencyIndicatorCreateSchema, EfficiencyIndicatorUpdateSchema
from src.database.models.efficiency import IndicatorCategoryEnum

async def efficiency_indicator_service_example():
    """效能指标服务使用示例"""
    
    # 获取数据库会话
    async for db_session in get_database_session():
        try:
            # 初始化服务
            service = EfficiencyIndicatorService(db_session)
            
            # 1. 创建效能指标
            print("=== 创建效能指标 ===")
            indicator_data = EfficiencyIndicatorCreateSchema(
                indicator_name="示例任务完成时间",
                indicator_code="EXAMPLE_TASK_TIME",
                category=IndicatorCategoryEnum.TIMELINESS,
                description="用于演示的任务完成时间指标",
                calculation_formula="结束时间 - 开始时间",
                unit="小时",
                min_threshold=Decimal('0'),
                max_threshold=Decimal('48'),
                target_value=Decimal('8'),
                default_weight=Decimal('0.3000'),
                priority_level=1,
                created_by="示例用户"
            )
            
            created_indicator = await service.create_indicator(indicator_data)
            print(f"创建指标成功: {created_indicator.indicator_name}")
            print(f"指标ID: {created_indicator.id}")
            
            # 2. 根据ID获取指标
            print("\n=== 根据ID获取指标 ===")
            retrieved_indicator = await service.get_indicator_by_id(created_indicator.id)
            if retrieved_indicator:
                print(f"获取指标: {retrieved_indicator.indicator_name}")
                print(f"指标分类: {retrieved_indicator.category}")
                print(f"计量单位: {retrieved_indicator.unit}")
            
            # 3. 根据分类获取指标列表
            print("\n=== 根据分类获取指标列表 ===")
            timeliness_indicators = await service.get_indicators_by_category(
                IndicatorCategoryEnum.TIMELINESS
            )
            print(f"时效性指标数量: {len(timeliness_indicators)}")
            for indicator in timeliness_indicators:
                print(f"- {indicator.indicator_name} ({indicator.indicator_code})")
            
            # 4. 获取所有激活指标
            print("\n=== 获取所有激活指标 ===")
            all_indicators = await service.get_all_active_indicators()
            print(f"总激活指标数量: {len(all_indicators)}")
            
            # 按分类统计
            category_count = {}
            for indicator in all_indicators:
                category = indicator.category.value
                category_count[category] = category_count.get(category, 0) + 1
            
            for category, count in category_count.items():
                print(f"- {category}: {count}个指标")
            
            # 5. 更新指标
            print("\n=== 更新指标 ===")
            update_data = EfficiencyIndicatorUpdateSchema(
                description="更新后的指标描述",
                target_value=Decimal('6'),
                priority_level=2
            )
            
            updated_indicator = await service.update_indicator(created_indicator.id, update_data)
            if updated_indicator:
                print(f"更新成功: {updated_indicator.description}")
                print(f"新目标值: {updated_indicator.target_value}")
            
            # 6. 删除指标（软删除）
            print("\n=== 删除指标 ===")
            delete_success = await service.delete_indicator(created_indicator.id)
            if delete_success:
                print("指标删除成功（状态设置为已弃用）")
                
                # 验证删除结果
                deleted_indicator = await service.get_indicator_by_id(created_indicator.id)
                if deleted_indicator:
                    print(f"指标状态: {deleted_indicator.status}")
            
        except ValueError as e:
            print(f"业务逻辑错误: {e}")
        except Exception as e:
            print(f"操作失败: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
        break

# 运行示例
if __name__ == "__main__":
    asyncio.run(efficiency_indicator_service_example())
```

### 2. EfficiencyIndicator - 效能指标数据模型

#### 类的用途和职责
`EfficiencyIndicator` 是效能指标的数据模型类，存储指标的基础定义信息，包括指标基本信息、计算公式、阈值设定等。

#### 主要属性说明

```python
class EfficiencyIndicator(BaseModel):
    """效能指标基础信息模型"""
    
    # 指标基本信息
    indicator_name: str          # 指标名称
    indicator_code: str          # 指标编码（唯一）
    category: IndicatorCategoryEnum  # 指标分类
    description: Optional[str]   # 指标详细描述
    
    # 计算相关信息
    calculation_formula: Optional[str]  # 计算公式说明
    unit: str                   # 计量单位
    
    # 阈值设定
    min_threshold: Optional[Decimal]    # 最小阈值
    max_threshold: Optional[Decimal]    # 最大阈值
    target_value: Optional[Decimal]     # 目标值
    
    # 权重和优先级
    default_weight: Decimal     # 默认权重
    priority_level: int         # 优先级等级
    
    # 状态信息
    status: IndicatorStatusEnum # 指标状态
    created_by: Optional[str]   # 创建者
    metadata_info: dict         # 元数据信息
```

#### 使用示例

```python
from src.database.models.efficiency import EfficiencyIndicator, IndicatorCategoryEnum, IndicatorStatusEnum
from decimal import Decimal

# 创建效能指标实例
indicator = EfficiencyIndicator(
    indicator_name="任务完成时间",
    indicator_code="TASK_COMPLETION_TIME",
    category=IndicatorCategoryEnum.TIMELINESS,
    description="从任务开始到完全结束的总耗时",
    calculation_formula="结束时间 - 开始时间",
    unit="小时",
    min_threshold=Decimal('0'),
    max_threshold=Decimal('72'),
    target_value=Decimal('24'),
    default_weight=Decimal('0.3000'),
    priority_level=1,
    status=IndicatorStatusEnum.ACTIVE,
    created_by="系统管理员",
    metadata_info={"source": "system", "version": "1.0"}
)

print(f"指标名称: {indicator.indicator_name}")
print(f"指标分类: {indicator.category.value}")
print(f"目标值: {indicator.target_value} {indicator.unit}")
```

### 3. Schema层类说明

#### EfficiencyIndicatorCreateSchema
用于创建效能指标的请求数据验证。

```python
from src.schemas.efficiency import EfficiencyIndicatorCreateSchema
from src.database.models.efficiency import IndicatorCategoryEnum
from decimal import Decimal

# 创建指标请求数据
create_data = EfficiencyIndicatorCreateSchema(
    indicator_name="准时交付率",
    indicator_code="ON_TIME_DELIVERY_RATE",
    category=IndicatorCategoryEnum.TIMELINESS,
    description="在规定时间内完成任务的比例",
    unit="百分比",
    target_value=Decimal('95.00'),
    default_weight=Decimal('0.2500')
)
```

#### EfficiencyIndicatorResponseSchema
用于返回效能指标信息的响应数据格式。

```python
# 响应数据会自动包含所有字段，包括：
# - 基本信息（id, indicator_name, indicator_code等）
# - 时间戳（created_at, updated_at）
# - 计算相关信息（calculation_formula, unit等）
# - 状态信息（status, priority_level等）
```

## 依赖关系说明

```
EfficiencyIndicatorService
    ├── 依赖: AsyncSession (数据库会话)
    ├── 使用: EfficiencyIndicator (数据模型)
    ├── 输入: EfficiencyIndicatorCreateSchema, EfficiencyIndicatorUpdateSchema
    └── 输出: EfficiencyIndicatorResponseSchema

EfficiencyIndicator
    ├── 继承: BaseModel
    ├── 关联: IndicatorCalculationResult (一对多)
    └── 枚举: IndicatorCategoryEnum, IndicatorStatusEnum
```

## 常见问题和故障排除

### Q1: 创建指标时提示"指标名称或编码已存在"
**解决方案**: 检查数据库中是否已存在相同的指标名称或编码，使用唯一的名称和编码。

### Q2: 获取指标列表为空
**解决方案**: 
1. 检查数据库中是否有数据
2. 确认指标状态是否为ACTIVE
3. 运行数据生成脚本：`python scripts/generate_efficiency_test_data.py`

### Q3: 权重值验证失败
**解决方案**: 确保权重值在0-1之间，且所有相关权重的总和为1.0。

### Q4: 数据库连接错误
**解决方案**: 
1. 检查数据库配置
2. 确保数据库服务正在运行
3. 验证数据库连接字符串的正确性
