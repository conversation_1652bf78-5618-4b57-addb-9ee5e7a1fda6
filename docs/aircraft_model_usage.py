#!/usr/bin/env python3
"""
飞机配置数据模型使用示例

演示AircraftConfig模型的创建、属性访问和方法调用
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.models.aircraft import (
    AircraftConfig,
    AircraftModelEnum,
    AircraftStatusEnum
)
from src.database.connection import get_database_session


async def demonstrate_aircraft_model_usage():
    """
    演示AircraftConfig模型的使用方法
    """
    print("=== AircraftConfig 数据模型使用示例 ===\n")
    
    # 1. 创建飞机配置实例
    print("1. 创建飞机配置实例")
    aircraft_config = AircraftConfig(
        aircraft_model=AircraftModelEnum.Y_20,
        quantity=2,
        payload_capacity=66000,       # 载重能力（kg）
        operational_range=4400,       # 作战半径（km）
        cruise_speed=700,             # 巡航速度（km/h）
        fuel_consumption=3500,        # 燃油消耗（L/h）
        loading_time=60,              # 装载时间（分钟）
        unloading_time=45,            # 卸载时间（分钟）
        ground_taxi_time=20,          # 地面滑行时间（分钟）
        crew_requirements={
            "pilots": 2,              # 飞行员数量
            "flight_engineers": 1,    # 飞行工程师数量
            "loadmasters": 2          # 装载员数量
        },
        compatible_equipment=[
            "重型装载车-ZC2025",
            "叉车-FC002",
            "牵引车-TC003"
        ],
        maintenance_schedule={
            "routine_maintenance_hours": 100,     # 例行维护间隔（小时）
            "major_maintenance_hours": 1000,      # 大修间隔（小时）
            "last_maintenance_date": "2025-07-10",
            "next_maintenance_date": "2025-08-10"
        },
        status=AircraftStatusEnum.AVAILABLE,
        remarks="技术文档演示用运-20飞机配置"
    )
    
    print(f"飞机型号: {aircraft_config.aircraft_model}")
    print(f"飞机数量: {aircraft_config.quantity}")
    print(f"载重能力: {aircraft_config.payload_capacity}kg")
    print(f"作战半径: {aircraft_config.operational_range}km")
    print(f"巡航速度: {aircraft_config.cruise_speed}km/h")
    print(f"燃油消耗: {aircraft_config.fuel_consumption}L/h")
    print(f"装载时间: {aircraft_config.loading_time}分钟")
    print(f"卸载时间: {aircraft_config.unloading_time}分钟")
    print(f"飞机状态: {aircraft_config.status}")
    
    # 2. 访问机组人员需求
    print("\n2. 访问机组人员需求")
    crew_req = aircraft_config.crew_requirements
    print(f"机组人员需求:")
    print(f"  - 飞行员: {crew_req.get('pilots')}人")
    print(f"  - 飞行工程师: {crew_req.get('flight_engineers')}人")
    print(f"  - 装载员: {crew_req.get('loadmasters')}人")
    
    total_crew = sum(crew_req.values())
    print(f"  - 总机组人员: {total_crew}人")
    
    # 3. 访问兼容设备列表
    print("\n3. 访问兼容设备列表")
    compatible_eq = aircraft_config.compatible_equipment
    print(f"兼容设备 ({len(compatible_eq)}种):")
    for i, equipment in enumerate(compatible_eq, 1):
        print(f"  {i}. {equipment}")
    
    # 4. 访问维护计划
    print("\n4. 访问维护计划")
    maintenance = aircraft_config.maintenance_schedule
    print(f"维护计划:")
    print(f"  - 例行维护间隔: {maintenance.get('routine_maintenance_hours')}小时")
    print(f"  - 大修间隔: {maintenance.get('major_maintenance_hours')}小时")
    print(f"  - 上次维护: {maintenance.get('last_maintenance_date')}")
    print(f"  - 下次维护: {maintenance.get('next_maintenance_date')}")
    
    # 5. 计算性能指标
    print("\n5. 计算性能指标")
    total_payload = aircraft_config.payload_capacity * aircraft_config.quantity
    total_fuel_consumption = aircraft_config.fuel_consumption * aircraft_config.quantity
    
    # 计算单次任务时间（装载+飞行+卸载+滑行）
    flight_time_hours = aircraft_config.operational_range / aircraft_config.cruise_speed * 2  # 往返
    loading_time_hours = aircraft_config.loading_time / 60
    unloading_time_hours = aircraft_config.unloading_time / 60
    taxi_time_hours = aircraft_config.ground_taxi_time / 60 * 2  # 起飞和降落
    
    total_mission_time = flight_time_hours + loading_time_hours + unloading_time_hours + taxi_time_hours
    mission_fuel_consumption = total_fuel_consumption * total_mission_time
    
    print(f"性能指标:")
    print(f"  - 总载重能力: {total_payload}kg")
    print(f"  - 单次任务时间: {total_mission_time:.1f}小时")
    print(f"  - 单次任务燃油消耗: {mission_fuel_consumption:.0f}L")
    print(f"  - 载重效率: {total_payload/mission_fuel_consumption:.1f}kg/L")
    
    # 6. 使用模型方法
    print("\n6. 使用模型方法")
    print(f"字符串表示: {aircraft_config}")
    
    # 转换为字典
    aircraft_dict = aircraft_config.to_dict()
    print(f"字典格式包含字段: {list(aircraft_dict.keys())}")
    
    # 7. 数据库操作示例
    print("\n7. 数据库操作示例")
    async for db_session in get_database_session():
        try:
            # 保存到数据库
            db_session.add(aircraft_config)
            await db_session.commit()
            await db_session.refresh(aircraft_config)
            
            print(f"飞机配置已保存到数据库，ID: {aircraft_config.id}")
            print(f"创建时间: {aircraft_config.created_at}")
            print(f"更新时间: {aircraft_config.updated_at}")
            
            # 更新飞机状态
            aircraft_config.status = AircraftStatusEnum.MAINTENANCE
            aircraft_config.maintenance_schedule.update({
                "current_maintenance_type": "scheduled",
                "maintenance_start_date": "2025-08-01",
                "estimated_completion": "2025-08-05"
            })
            await db_session.commit()
            print(f"飞机状态已更新为: {aircraft_config.status}")
            
        except Exception as e:
            print(f"数据库操作错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


async def demonstrate_aircraft_models_comparison():
    """
    演示不同飞机型号的对比
    """
    print("\n=== 不同飞机型号对比示例 ===\n")
    
    # 1. 创建不同型号的飞机配置
    aircraft_configs = [
        {
            "name": "运-20 重型运输机",
            "config": AircraftConfig(
                aircraft_model=AircraftModelEnum.Y_20,
                quantity=1,
                payload_capacity=66000,
                operational_range=4400,
                cruise_speed=700,
                fuel_consumption=3500,
                loading_time=60,
                unloading_time=45,
                ground_taxi_time=20,
                crew_requirements={"pilots": 2, "flight_engineers": 1, "loadmasters": 2},
                status=AircraftStatusEnum.AVAILABLE
            )
        },
        {
            "name": "运-8 中型运输机",
            "config": AircraftConfig(
                aircraft_model=AircraftModelEnum.Y_8,
                quantity=1,
                payload_capacity=20000,
                operational_range=5700,
                cruise_speed=500,
                fuel_consumption=1800,
                loading_time=40,
                unloading_time=30,
                ground_taxi_time=15,
                crew_requirements={"pilots": 2, "flight_engineers": 0, "loadmasters": 1},
                status=AircraftStatusEnum.AVAILABLE
            )
        },
        {
            "name": "运-7 轻型运输机",
            "config": AircraftConfig(
                aircraft_model=AircraftModelEnum.Y_7,
                quantity=1,
                payload_capacity=5500,
                operational_range=1400,
                cruise_speed=350,
                fuel_consumption=800,
                loading_time=25,
                unloading_time=20,
                ground_taxi_time=10,
                crew_requirements={"pilots": 2, "flight_engineers": 0, "loadmasters": 1},
                status=AircraftStatusEnum.AVAILABLE
            )
        }
    ]
    
    print("1. 飞机型号基本参数对比:")
    print(f"{'型号':<15} {'载重(kg)':<12} {'航程(km)':<12} {'速度(km/h)':<12} {'油耗(L/h)':<12}")
    print("-" * 70)
    
    for item in aircraft_configs:
        config = item["config"]
        print(f"{config.aircraft_model.value:<15} "
              f"{config.payload_capacity:<12} "
              f"{config.operational_range:<12} "
              f"{config.cruise_speed:<12} "
              f"{config.fuel_consumption:<12}")
    
    # 2. 作业时间对比
    print(f"\n2. 作业时间对比:")
    print(f"{'型号':<15} {'装载(min)':<12} {'卸载(min)':<12} {'滑行(min)':<12} {'总地面时间(min)':<15}")
    print("-" * 75)
    
    for item in aircraft_configs:
        config = item["config"]
        total_ground_time = config.loading_time + config.unloading_time + config.ground_taxi_time
        print(f"{config.aircraft_model.value:<15} "
              f"{config.loading_time:<12} "
              f"{config.unloading_time:<12} "
              f"{config.ground_taxi_time:<12} "
              f"{total_ground_time:<15}")
    
    # 3. 机组人员需求对比
    print(f"\n3. 机组人员需求对比:")
    print(f"{'型号':<15} {'飞行员':<8} {'工程师':<8} {'装载员':<8} {'总人数':<8}")
    print("-" * 50)
    
    for item in aircraft_configs:
        config = item["config"]
        crew = config.crew_requirements
        total_crew = sum(crew.values())
        print(f"{config.aircraft_model.value:<15} "
              f"{crew.get('pilots', 0):<8} "
              f"{crew.get('flight_engineers', 0):<8} "
              f"{crew.get('loadmasters', 0):<8} "
              f"{total_crew:<8}")
    
    # 4. 效率指标计算
    print(f"\n4. 效率指标对比:")
    print(f"{'型号':<15} {'载重效率':<15} {'速度效率':<15} {'燃油效率':<15}")
    print("-" * 65)
    
    for item in aircraft_configs:
        config = item["config"]
        # 载重效率：载重/机组人员
        crew_count = sum(config.crew_requirements.values())
        payload_per_crew = config.payload_capacity / crew_count
        
        # 速度效率：速度/燃油消耗
        speed_efficiency = config.cruise_speed / config.fuel_consumption
        
        # 燃油效率：载重/燃油消耗
        fuel_efficiency = config.payload_capacity / config.fuel_consumption
        
        print(f"{config.aircraft_model.value:<15} "
              f"{payload_per_crew:.0f}kg/人{'':<6} "
              f"{speed_efficiency:.2f}km/L{'':<7} "
              f"{fuel_efficiency:.1f}kg/L{'':<7}")


async def demonstrate_aircraft_queries():
    """
    演示飞机配置查询操作
    """
    print("\n=== 飞机配置查询操作示例 ===\n")
    
    from sqlalchemy import select, and_, or_, func
    
    async for db_session in get_database_session():
        try:
            # 1. 按飞机型号查询
            print("1. 查询所有运-20飞机")
            query = select(AircraftConfig).where(
                AircraftConfig.aircraft_model == AircraftModelEnum.Y_20
            )
            result = await db_session.execute(query)
            y20_aircraft = result.scalars().all()
            
            print(f"找到 {len(y20_aircraft)} 个运-20配置:")
            for aircraft in y20_aircraft:
                print(f"  - 数量: {aircraft.quantity}, 状态: {aircraft.status}")
            
            # 2. 按载重能力和状态查询
            print("\n2. 查询可用且载重大于30吨的飞机")
            query = select(AircraftConfig).where(
                and_(
                    AircraftConfig.status == AircraftStatusEnum.AVAILABLE,
                    AircraftConfig.payload_capacity > 30000
                )
            )
            result = await db_session.execute(query)
            heavy_aircraft = result.scalars().all()
            
            print(f"找到 {len(heavy_aircraft)} 个重型飞机:")
            for aircraft in heavy_aircraft:
                print(f"  - {aircraft.aircraft_model} ({aircraft.payload_capacity}kg)")
            
            # 3. 统计查询
            print("\n3. 飞机统计信息")
            
            # 按型号统计
            query = select(
                AircraftConfig.aircraft_model,
                func.count(AircraftConfig.id).label('config_count'),
                func.sum(AircraftConfig.quantity).label('total_quantity'),
                func.sum(AircraftConfig.payload_capacity * AircraftConfig.quantity).label('total_capacity')
            ).group_by(AircraftConfig.aircraft_model)
            
            result = await db_session.execute(query)
            statistics = result.all()
            
            print("按型号统计:")
            for stat in statistics:
                print(f"  - {stat.aircraft_model}: {stat.config_count}种配置, "
                      f"总数量{stat.total_quantity}, "
                      f"总载重{stat.total_capacity}kg")
            
            # 4. 复合条件查询
            print("\n4. 查询长航程或大载重的飞机")
            query = select(AircraftConfig).where(
                or_(
                    AircraftConfig.operational_range > 4000,
                    AircraftConfig.payload_capacity > 50000
                )
            )
            result = await db_session.execute(query)
            long_range_or_heavy = result.scalars().all()
            
            print(f"找到 {len(long_range_or_heavy)} 个长航程或大载重飞机")
            
        except Exception as e:
            print(f"查询操作错误: {e}")
        finally:
            await db_session.close()
            break


if __name__ == "__main__":
    asyncio.run(demonstrate_aircraft_model_usage())
    asyncio.run(demonstrate_aircraft_models_comparison())
    asyncio.run(demonstrate_aircraft_queries())
