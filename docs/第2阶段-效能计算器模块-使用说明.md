# 第2阶段 - 效能计算器模块使用说明

## 模块概述

效能计算器模块提供了5个专业的计算器类，分别对应5大效能指标分类。每个计算器都实现了该分类下的具体指标计算逻辑，所有方法都是静态方法，可以直接调用而无需实例化。

### 计算器分类
- **TimelinessCalculator**: 时效性指标计算器
- **CapabilityCalculator**: 能力指标计算器  
- **EconomyCalculator**: 经济性指标计算器
- **RobustnessCalculator**: 鲁棒性指标计算器
- **SafetyCalculator**: 安全性指标计算器

## 核心类详细说明

### 1. TimelinessCalculator - 时效性指标计算器

#### 类的用途和职责
计算与时间效率相关的指标，包括任务完成时间、响应时间、准时交付率等。

#### 主要方法说明

##### calculate_task_completion_time()
```python
@staticmethod
def calculate_task_completion_time(start_time: datetime, end_time: datetime) -> Decimal:
    """
    计算任务完成时间
    
    Args:
        start_time: 任务开始时间
        end_time: 任务结束时间
        
    Returns:
        Decimal: 任务完成时间（小时）
        
    Raises:
        ValueError: 当结束时间早于或等于开始时间时
    """
```

##### calculate_average_response_time()
```python
@staticmethod
def calculate_average_response_time(response_times: List[float]) -> Decimal:
    """
    计算平均响应时间
    
    Args:
        response_times: 响应时间列表（小时）
        
    Returns:
        Decimal: 平均响应时间（小时）
    """
```

#### 完整使用示例

```python
from datetime import datetime, timedelta
from decimal import Decimal
from src.services.efficiency_calculator import TimelinessCalculator

def timeliness_calculator_example():
    """时效性计算器使用示例"""
    
    print("=== 时效性指标计算示例 ===")
    
    # 1. 计算任务完成时间
    print("\n1. 任务完成时间计算")
    start_time = datetime(2024, 1, 1, 9, 0, 0)  # 上午9点开始
    end_time = datetime(2024, 1, 1, 17, 30, 0)  # 下午5点30分结束
    
    completion_time = TimelinessCalculator.calculate_task_completion_time(start_time, end_time)
    print(f"任务开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"任务结束时间: {end_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"任务完成时间: {completion_time} 小时")
    
    # 2. 计算平均响应时间
    print("\n2. 平均响应时间计算")
    response_times = [1.5, 2.0, 1.8, 2.2, 1.9, 2.1, 1.7]  # 小时
    avg_response = TimelinessCalculator.calculate_average_response_time(response_times)
    print(f"响应时间列表: {response_times}")
    print(f"平均响应时间: {avg_response} 小时")
    
    # 3. 计算准时交付率
    print("\n3. 准时交付率计算")
    total_tasks = 150
    on_time_tasks = 142
    delivery_rate = TimelinessCalculator.calculate_on_time_delivery_rate(total_tasks, on_time_tasks)
    print(f"总任务数: {total_tasks}")
    print(f"准时完成任务数: {on_time_tasks}")
    print(f"准时交付率: {delivery_rate}%")
    
    # 4. 计算单位时间运输量
    print("\n4. 单位时间运输量计算")
    total_volume = Decimal('2400')  # 总运输量2400吨
    duration_hours = Decimal('24')  # 持续24小时
    volume_per_hour = TimelinessCalculator.calculate_transport_volume_per_hour(
        total_volume, duration_hours
    )
    print(f"总运输量: {total_volume} 吨")
    print(f"持续时间: {duration_hours} 小时")
    print(f"单位时间运输量: {volume_per_hour} 吨/小时")
    
    # 5. 异常处理示例
    print("\n5. 异常处理示例")
    try:
        # 错误的时间顺序
        invalid_completion_time = TimelinessCalculator.calculate_task_completion_time(
            end_time, start_time  # 颠倒时间顺序
        )
    except ValueError as e:
        print(f"捕获异常: {e}")

if __name__ == "__main__":
    timeliness_calculator_example()
```

### 2. CapabilityCalculator - 能力指标计算器

#### 类的用途和职责
计算与系统能力相关的指标，包括最大持续运输能力、任务达成率、装备利用率等。

#### 主要方法说明

##### calculate_max_sustained_capacity()
```python
@staticmethod
def calculate_max_sustained_capacity(peak_capacity: Decimal, sustainability_factor: Decimal) -> Decimal:
    """
    计算最大持续运输能力
    
    Args:
        peak_capacity: 峰值运输能力
        sustainability_factor: 可持续性系数（0-1之间）
        
    Returns:
        Decimal: 最大持续运输能力
        
    Raises:
        ValueError: 当可持续性系数不在0-1范围内时
    """
```

#### 使用示例

```python
from decimal import Decimal
from src.services.efficiency_calculator import CapabilityCalculator

def capability_calculator_example():
    """能力指标计算器使用示例"""
    
    print("=== 能力指标计算示例 ===")
    
    # 1. 计算最大持续运输能力
    print("\n1. 最大持续运输能力计算")
    peak_capacity = Decimal('1000')  # 峰值能力1000吨
    sustainability_factor = Decimal('0.85')  # 85%的可持续性
    
    sustained_capacity = CapabilityCalculator.calculate_max_sustained_capacity(
        peak_capacity, sustainability_factor
    )
    print(f"峰值运输能力: {peak_capacity} 吨")
    print(f"可持续性系数: {sustainability_factor}")
    print(f"最大持续运输能力: {sustained_capacity} 吨")
    
    # 2. 计算任务达成率
    print("\n2. 任务达成率计算")
    total_missions = 80
    successful_missions = 76
    success_rate = CapabilityCalculator.calculate_mission_success_rate(
        total_missions, successful_missions
    )
    print(f"总任务数: {total_missions}")
    print(f"成功任务数: {successful_missions}")
    print(f"任务达成率: {success_rate}%")
    
    # 3. 计算可用架次率
    print("\n3. 可用架次率计算")
    total_sorties = 120
    available_sorties = 108
    sortie_rate = CapabilityCalculator.calculate_available_sortie_rate(
        total_sorties, available_sorties
    )
    print(f"总架次数: {total_sorties}")
    print(f"可用架次数: {available_sorties}")
    print(f"可用架次率: {sortie_rate}%")
    
    # 4. 计算装备利用率
    print("\n4. 装备利用率计算")
    actual_usage_hours = Decimal('180')  # 实际使用180小时
    available_hours = Decimal('240')     # 可用240小时
    utilization_rate = CapabilityCalculator.calculate_equipment_utilization_rate(
        actual_usage_hours, available_hours
    )
    print(f"实际使用时间: {actual_usage_hours} 小时")
    print(f"可用时间: {available_hours} 小时")
    print(f"装备利用率: {utilization_rate}%")

if __name__ == "__main__":
    capability_calculator_example()
```

### 3. EconomyCalculator - 经济性指标计算器

#### 类的用途和职责
计算与经济效益相关的指标，包括成本效率、资源利用率、燃油效率等。

#### 使用示例

```python
from decimal import Decimal
from src.services.efficiency_calculator import EconomyCalculator

def economy_calculator_example():
    """经济性指标计算器使用示例"""
    
    print("=== 经济性指标计算示例 ===")
    
    # 1. 计算吨公里成本
    print("\n1. 吨公里成本计算")
    total_cost = Decimal('50000')      # 总成本5万元
    transport_volume = Decimal('500')   # 运输量500吨
    distance = Decimal('1000')         # 距离1000公里
    
    ton_km_cost = EconomyCalculator.calculate_ton_km_cost(
        total_cost, transport_volume, distance
    )
    print(f"总成本: {total_cost} 元")
    print(f"运输量: {transport_volume} 吨")
    print(f"运输距离: {distance} 公里")
    print(f"吨公里成本: {ton_km_cost} 元/吨公里")
    
    # 2. 计算资源利用率
    print("\n2. 资源利用率计算")
    used_resources = Decimal('850')    # 已使用资源
    total_resources = Decimal('1000')  # 总资源
    
    utilization_rate = EconomyCalculator.calculate_resource_utilization_rate(
        used_resources, total_resources
    )
    print(f"已使用资源: {used_resources}")
    print(f"总资源: {total_resources}")
    print(f"资源利用率: {utilization_rate}%")
    
    # 3. 计算冗余度
    print("\n3. 冗余度计算")
    reserved_resources = Decimal('200')  # 预留资源
    required_resources = Decimal('800')  # 所需资源
    
    redundancy = EconomyCalculator.calculate_redundancy_ratio(
        reserved_resources, required_resources
    )
    print(f"预留资源: {reserved_resources}")
    print(f"所需资源: {required_resources}")
    print(f"冗余度: {redundancy}%")
    
    # 4. 计算燃油效率
    print("\n4. 燃油效率计算")
    transport_volume = Decimal('1200')  # 运输量1200吨
    fuel_consumption = Decimal('600')   # 燃油消耗600升
    
    fuel_efficiency = EconomyCalculator.calculate_fuel_efficiency(
        transport_volume, fuel_consumption
    )
    print(f"运输量: {transport_volume} 吨")
    print(f"燃油消耗: {fuel_consumption} 升")
    print(f"燃油效率: {fuel_efficiency} 吨/升")

if __name__ == "__main__":
    economy_calculator_example()
```

### 4. RobustnessCalculator - 鲁棒性指标计算器

#### 类的用途和职责
计算与系统鲁棒性相关的指标，包括场景适应度、抗毁伤能力、恢复时间等。

#### 使用示例

```python
from datetime import datetime, timedelta
from decimal import Decimal
from src.services.efficiency_calculator import RobustnessCalculator

def robustness_calculator_example():
    """鲁棒性指标计算器使用示例"""
    
    print("=== 鲁棒性指标计算示例 ===")
    
    # 1. 计算场景适应度
    print("\n1. 场景适应度计算")
    performance_scores = [
        Decimal('85'), Decimal('88'), Decimal('82'), 
        Decimal('87'), Decimal('84'), Decimal('86')
    ]
    
    adaptability = RobustnessCalculator.calculate_scenario_adaptability(performance_scores)
    print(f"不同场景性能评分: {[float(score) for score in performance_scores]}")
    print(f"场景适应度: {adaptability}")
    
    # 2. 计算抗毁伤能力
    print("\n2. 抗毁伤能力计算")
    original_capacity = Decimal('1000')     # 原始运行能力
    damaged_capacity = Decimal('750')       # 受损后运行能力
    
    damage_resistance = RobustnessCalculator.calculate_damage_resistance(
        damaged_capacity, original_capacity
    )
    print(f"原始运行能力: {original_capacity}")
    print(f"受损后运行能力: {damaged_capacity}")
    print(f"抗毁伤能力: {damage_resistance}%")
    
    # 3. 计算恢复时间
    print("\n3. 恢复时间计算")
    failure_time = datetime(2024, 1, 1, 10, 0, 0)    # 故障时间
    recovery_time = datetime(2024, 1, 1, 14, 30, 0)  # 恢复时间
    
    recovery_duration = RobustnessCalculator.calculate_recovery_time(
        failure_time, recovery_time
    )
    print(f"故障发生时间: {failure_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"恢复完成时间: {recovery_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"恢复时间: {recovery_duration} 小时")
    
    # 4. 计算风险抵抗力
    print("\n4. 风险抵抗力计算")
    risk_mitigation_score = Decimal('90')   # 风险缓解评分
    risk_exposure_score = Decimal('70')     # 风险暴露评分
    
    risk_resistance = RobustnessCalculator.calculate_risk_resistance(
        risk_mitigation_score, risk_exposure_score
    )
    print(f"风险缓解评分: {risk_mitigation_score}")
    print(f"风险暴露评分: {risk_exposure_score}")
    print(f"风险抵抗力: {risk_resistance}")

if __name__ == "__main__":
    robustness_calculator_example()
```

### 5. SafetyCalculator - 安全性指标计算器

#### 类的用途和职责
计算与安全性相关的指标，包括风险概率、损失率、安全裕度等。

#### 使用示例

```python
from decimal import Decimal
from src.services.efficiency_calculator import SafetyCalculator

def safety_calculator_example():
    """安全性指标计算器使用示例"""
    
    print("=== 安全性指标计算示例 ===")
    
    # 1. 计算任务风险概率
    print("\n1. 任务风险概率计算")
    risk_events = 3        # 风险事件数量
    total_missions = 200   # 总任务数
    
    risk_probability = SafetyCalculator.calculate_mission_risk_probability(
        risk_events, total_missions
    )
    print(f"风险事件数量: {risk_events}")
    print(f"总任务数: {total_missions}")
    print(f"任务风险概率: {risk_probability}%")
    
    # 2. 计算损失率
    print("\n2. 损失率计算")
    lost_volume = Decimal('8')      # 损失量8吨
    total_volume = Decimal('2000')  # 总量2000吨
    
    loss_rate = SafetyCalculator.calculate_loss_rate(lost_volume, total_volume)
    print(f"损失量: {lost_volume} 吨")
    print(f"总量: {total_volume} 吨")
    print(f"损失率: {loss_rate}%")
    
    # 3. 计算安全裕度
    print("\n3. 安全裕度计算")
    safety_measures_score = Decimal('95')    # 安全措施评分
    minimum_required_score = Decimal('70')   # 最低要求评分
    
    safety_margin = SafetyCalculator.calculate_safety_margin(
        safety_measures_score, minimum_required_score
    )
    print(f"安全措施评分: {safety_measures_score}")
    print(f"最低要求评分: {minimum_required_score}")
    print(f"安全裕度: {safety_margin}%")
    
    # 4. 计算应急响应能力
    print("\n4. 应急响应能力计算")
    actual_response_time = Decimal('15')    # 实际响应时间15分钟
    standard_response_time = Decimal('20')  # 标准响应时间20分钟
    
    response_capability = SafetyCalculator.calculate_emergency_response_capability(
        actual_response_time, standard_response_time
    )
    print(f"实际响应时间: {actual_response_time} 分钟")
    print(f"标准响应时间: {standard_response_time} 分钟")
    print(f"应急响应能力: {response_capability}")

if __name__ == "__main__":
    safety_calculator_example()
```

## 综合使用示例

```python
from src.services.efficiency_calculator import *
from datetime import datetime
from decimal import Decimal

def comprehensive_calculator_example():
    """综合计算器使用示例"""
    
    print("=== 综合效能指标计算示例 ===")
    
    # 模拟一个完整的运输任务场景
    print("\n场景：某次重要物资运输任务的效能评估")
    
    # 时效性指标
    start_time = datetime(2024, 1, 1, 8, 0, 0)
    end_time = datetime(2024, 1, 2, 20, 0, 0)
    completion_time = TimelinessCalculator.calculate_task_completion_time(start_time, end_time)
    
    # 能力指标
    success_rate = CapabilityCalculator.calculate_mission_success_rate(10, 9)
    
    # 经济性指标
    fuel_efficiency = EconomyCalculator.calculate_fuel_efficiency(
        Decimal('500'), Decimal('200')
    )
    
    # 鲁棒性指标
    damage_resistance = RobustnessCalculator.calculate_damage_resistance(
        Decimal('400'), Decimal('500')
    )
    
    # 安全性指标
    loss_rate = SafetyCalculator.calculate_loss_rate(
        Decimal('2'), Decimal('500')
    )
    
    print(f"任务完成时间: {completion_time} 小时")
    print(f"任务达成率: {success_rate}%")
    print(f"燃油效率: {fuel_efficiency} 吨/升")
    print(f"抗毁伤能力: {damage_resistance}%")
    print(f"损失率: {loss_rate}%")

if __name__ == "__main__":
    comprehensive_calculator_example()
```

## 常见问题和故障排除

### Q1: 计算结果精度问题
**解决方案**: 所有计算器都使用Decimal类型确保精度，结果自动量化到小数点后2位。

### Q2: 时间计算异常
**解决方案**: 确保开始时间早于结束时间，使用正确的datetime格式。

### Q3: 除零错误
**解决方案**: 计算器内部已处理除零情况，会返回Decimal('0')而不是抛出异常。

### Q4: 参数范围验证
**解决方案**: 某些方法（如可持续性系数）有参数范围限制，超出范围会抛出ValueError。
