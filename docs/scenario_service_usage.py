#!/usr/bin/env python3
"""
场景服务层使用示例

演示ScenarioService的初始化和主要方法调用
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.services.scenario import ScenarioService
from src.schemas.scenario import (
    ScenarioCreateSchema,
    ScenarioUpdateSchema,
    EnvironmentConditionsSchema,
    ResourceConstraintsSchema,
    MissionRequirementsSchema,
    ThreatFactorsSchema
)
from src.database.models.scenario import TaskTypeEnum, ThreatLevelEnum, ScenarioStatusEnum
from src.database.connection import get_database_session


async def demonstrate_scenario_service_usage():
    """
    演示ScenarioService的完整使用流程
    """
    print("=== ScenarioService 使用示例 ===\n")
    
    async for db_session in get_database_session():
        try:
            # 1. 初始化服务
            print("1. 初始化ScenarioService")
            scenario_service = ScenarioService(db_session)
            print("✅ ScenarioService初始化完成")
            
            # 2. 创建场景
            print("\n2. 创建新场景")
            
            # 构建创建数据
            create_data = ScenarioCreateSchema(
                scenario_name="技术文档示例场景",
                scenario_type="documentation_example",
                description="用于技术文档演示的示例场景",
                task_type=TaskTypeEnum.EQUIPMENT_TRANSPORT,
                environment_conditions=EnvironmentConditionsSchema(
                    weather={
                        "visibility": 12000,
                        "wind_speed": 10,
                        "precipitation": 0,
                        "temperature": 20,
                        "humidity": 55
                    },
                    terrain="plain",
                    airspace_control={"control_level": "normal"}
                ),
                resource_constraints=ResourceConstraintsSchema(
                    transport_capacity={"max_aircraft": 3, "max_equipment": 8},
                    support_resources={"fuel_capacity": 80000},
                    time_window={"max_duration": 18}
                ),
                mission_requirements=MissionRequirementsSchema(
                    cargo_weight=120000,
                    personnel_count=50,
                    origin="doc_base_001",
                    destination="doc_base_002",
                    time_window={
                        "start_time": "2025-08-01T08:00:00Z",
                        "end_time": "2025-08-01T20:00:00Z"
                    },
                    priority="high"
                ),
                threat_factors=ThreatFactorsSchema(
                    enemy_threats={"air_defense": "low"},
                    threat_probabilities={"air_defense_threat": 0.2},
                    countermeasures=["route_planning", "timing_optimization"]
                ),
                threat_level=ThreatLevelEnum.LOW,
                created_by="doc_generator"
            )
            
            # 调用创建方法
            created_scenario = await scenario_service.create_scenario(create_data)
            scenario_id = created_scenario.id
            
            print(f"✅ 场景创建成功")
            print(f"   场景ID: {scenario_id}")
            print(f"   场景名称: {created_scenario.scenario_name}")
            print(f"   创建时间: {created_scenario.created_at}")
            
            # 3. 获取场景详情
            print("\n3. 获取场景详情")
            scenario_detail = await scenario_service.get_scenario_by_id(scenario_id)
            
            if scenario_detail:
                print(f"✅ 场景详情获取成功")
                print(f"   场景名称: {scenario_detail.scenario_name}")
                print(f"   任务类型: {scenario_detail.task_type}")
                print(f"   威胁等级: {scenario_detail.threat_level}")
                print(f"   状态: {scenario_detail.status}")
            else:
                print("❌ 场景不存在")
            
            # 4. 获取场景列表
            print("\n4. 获取场景列表")
            scenario_list = await scenario_service.get_scenario_list(
                page=1,
                page_size=10,
                scenario_type="documentation_example",
                task_type=TaskTypeEnum.EQUIPMENT_TRANSPORT.value,
                search_keyword="技术文档"
            )
            
            print(f"✅ 场景列表获取成功")
            print(f"   总数量: {scenario_list.total}")
            print(f"   当前页: {scenario_list.page}")
            print(f"   每页数量: {scenario_list.page_size}")
            print(f"   总页数: {scenario_list.total_pages}")
            print(f"   场景数量: {len(scenario_list.scenarios)}")
            
            for scenario in scenario_list.scenarios:
                print(f"   - {scenario.scenario_name} ({scenario.task_type})")
            
            # 5. 更新场景
            print("\n5. 更新场景")
            update_data = ScenarioUpdateSchema(
                scenario_name="更新后的技术文档示例场景",
                description="已更新的技术文档演示场景描述",
                status=ScenarioStatusEnum.ACTIVE
            )
            
            updated_scenario = await scenario_service.update_scenario(scenario_id, update_data)
            
            if updated_scenario:
                print(f"✅ 场景更新成功")
                print(f"   新名称: {updated_scenario.scenario_name}")
                print(f"   新状态: {updated_scenario.status}")
                print(f"   更新时间: {updated_scenario.updated_at}")
            else:
                print("❌ 场景更新失败")
            
            # 6. 验证场景参数
            print("\n6. 验证场景参数")
            validation_errors = await scenario_service.validate_scenario_parameters(
                create_data.model_dump()
            )
            
            if validation_errors:
                print("❌ 场景参数验证失败:")
                for error in validation_errors:
                    print(f"   - {error}")
            else:
                print("✅ 场景参数验证通过")
            
            # 7. 删除场景（软删除）
            print("\n7. 删除场景")
            delete_success = await scenario_service.delete_scenario(scenario_id)
            
            if delete_success:
                print("✅ 场景删除成功（软删除）")
                
                # 验证软删除
                deleted_scenario = await scenario_service.get_scenario_by_id(scenario_id)
                if deleted_scenario and deleted_scenario.status == ScenarioStatusEnum.DELETED:
                    print("✅ 确认场景已标记为删除状态")
            else:
                print("❌ 场景删除失败")
            
        except Exception as e:
            print(f"❌ 操作过程中发生错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


async def demonstrate_scenario_service_error_handling():
    """
    演示ScenarioService的错误处理
    """
    print("\n=== ScenarioService 错误处理示例 ===\n")
    
    async for db_session in get_database_session():
        try:
            scenario_service = ScenarioService(db_session)
            
            # 1. 查询不存在的场景
            print("1. 查询不存在的场景")
            non_existent_id = "non-existent-id"
            result = await scenario_service.get_scenario_by_id(non_existent_id)
            
            if result is None:
                print("✅ 正确处理了不存在的场景查询，返回None")
            
            # 2. 更新不存在的场景
            print("\n2. 更新不存在的场景")
            update_data = ScenarioUpdateSchema(scenario_name="测试更新")
            result = await scenario_service.update_scenario(non_existent_id, update_data)
            
            if result is None:
                print("✅ 正确处理了不存在场景的更新，返回None")
            
            # 3. 删除不存在的场景
            print("\n3. 删除不存在的场景")
            result = await scenario_service.delete_scenario(non_existent_id)
            
            if not result:
                print("✅ 正确处理了不存在场景的删除，返回False")
            
            # 4. 参数验证错误
            print("\n4. 参数验证错误")
            invalid_data = {
                "mission_requirements": {
                    "cargo_weight": -1000,  # 无效的负重量
                    "origin": "",           # 空的出发地
                    "destination": ""       # 空的目的地
                }
            }
            
            validation_errors = await scenario_service.validate_scenario_parameters(invalid_data)
            
            if validation_errors:
                print("✅ 正确识别了参数验证错误:")
                for error in validation_errors:
                    print(f"   - {error}")
            
        except Exception as e:
            print(f"❌ 错误处理测试中发生异常: {e}")
        finally:
            await db_session.close()
            break


if __name__ == "__main__":
    asyncio.run(demonstrate_scenario_service_usage())
    asyncio.run(demonstrate_scenario_service_error_handling())
