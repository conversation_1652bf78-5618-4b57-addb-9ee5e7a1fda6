#!/usr/bin/env python3
"""
设备配置数据模型使用示例

演示EquipmentConfig模型的创建、属性访问和方法调用
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.models.equipment import (
    EquipmentConfig,
    EquipmentTypeEnum,
    EquipmentStatusEnum
)
from src.database.connection import get_database_session


async def demonstrate_equipment_model_usage():
    """
    演示EquipmentConfig模型的使用方法
    """
    print("=== EquipmentConfig 数据模型使用示例 ===\n")
    
    # 1. 创建设备配置实例
    print("1. 创建设备配置实例")
    equipment_config = EquipmentConfig(
        equipment_type=EquipmentTypeEnum.LOADING_VEHICLE,
        equipment_model="重型装载车-ZC2025",
        quantity=3,
        max_load_capacity=25000,      # 最大载重能力（kg）
        loading_speed=6000,           # 装载速度（kg/h）
        unloading_speed=7500,         # 卸载速度（kg/h）
        operation_radius=150,         # 作业半径（m）
        power_consumption=120,        # 功耗（kW）
        unit_operation_duration=1.5,  # 单位作业时长（h）
        efficiency_factor=0.92,       # 效率系数（0-1）
        maintenance_interval=300,     # 维护间隔（小时）
        operational_status=EquipmentStatusEnum.AVAILABLE,
        maintenance_info={
            "last_maintenance_date": "2025-07-15",
            "next_maintenance_date": "2025-08-15",
            "maintenance_type": "routine",
            "maintenance_hours": 8
        },
        remarks="技术文档演示用重型装载车配置"
    )
    
    print(f"设备类型: {equipment_config.equipment_type}")
    print(f"设备型号: {equipment_config.equipment_model}")
    print(f"设备数量: {equipment_config.quantity}")
    print(f"最大载重: {equipment_config.max_load_capacity}kg")
    print(f"装载速度: {equipment_config.loading_speed}kg/h")
    print(f"卸载速度: {equipment_config.unloading_speed}kg/h")
    print(f"作业半径: {equipment_config.operation_radius}m")
    print(f"效率系数: {equipment_config.efficiency_factor}")
    print(f"运行状态: {equipment_config.operational_status}")
    
    # 2. 访问维护信息
    print("\n2. 访问维护信息")
    maintenance_info = equipment_config.maintenance_info
    print(f"维护信息:")
    print(f"  - 上次维护: {maintenance_info.get('last_maintenance_date')}")
    print(f"  - 下次维护: {maintenance_info.get('next_maintenance_date')}")
    print(f"  - 维护类型: {maintenance_info.get('maintenance_type')}")
    print(f"  - 维护时长: {maintenance_info.get('maintenance_hours')}小时")
    
    # 3. 计算性能指标
    print("\n3. 计算性能指标")
    total_capacity = equipment_config.max_load_capacity * equipment_config.quantity
    effective_capacity = total_capacity * equipment_config.efficiency_factor
    
    print(f"总载重能力: {total_capacity}kg")
    print(f"有效载重能力: {effective_capacity:.0f}kg")
    print(f"总装载速度: {equipment_config.loading_speed * equipment_config.quantity}kg/h")
    print(f"有效装载速度: {equipment_config.loading_speed * equipment_config.quantity * equipment_config.efficiency_factor:.0f}kg/h")
    
    # 4. 使用模型方法
    print("\n4. 使用模型方法")
    print(f"字符串表示: {equipment_config}")
    
    # 转换为字典
    equipment_dict = equipment_config.to_dict()
    print(f"字典格式包含字段: {list(equipment_dict.keys())}")
    
    # 5. 数据库操作示例
    print("\n5. 数据库操作示例")
    async for db_session in get_database_session():
        try:
            # 保存到数据库
            db_session.add(equipment_config)
            await db_session.commit()
            await db_session.refresh(equipment_config)
            
            print(f"设备配置已保存到数据库，ID: {equipment_config.id}")
            print(f"创建时间: {equipment_config.created_at}")
            print(f"更新时间: {equipment_config.updated_at}")
            
            # 更新设备状态
            equipment_config.operational_status = EquipmentStatusEnum.MAINTENANCE
            equipment_config.maintenance_info.update({
                "current_maintenance_start": "2025-08-01",
                "estimated_completion": "2025-08-03"
            })
            await db_session.commit()
            print(f"设备状态已更新为: {equipment_config.operational_status}")
            
        except Exception as e:
            print(f"数据库操作错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


async def demonstrate_equipment_types_and_validation():
    """
    演示不同设备类型和参数验证
    """
    print("\n=== 设备类型和参数验证示例 ===\n")
    
    # 1. 不同类型的设备配置
    equipment_configs = [
        {
            "name": "装载车配置",
            "config": EquipmentConfig(
                equipment_type=EquipmentTypeEnum.LOADING_VEHICLE,
                equipment_model="装载车-LC001",
                quantity=2,
                max_load_capacity=20000,
                loading_speed=5000,
                unloading_speed=6000,
                operation_radius=100,
                unit_operation_duration=1.2,
                efficiency_factor=0.88,
                maintenance_interval=250,
                operational_status=EquipmentStatusEnum.AVAILABLE
            )
        },
        {
            "name": "叉车配置",
            "config": EquipmentConfig(
                equipment_type=EquipmentTypeEnum.HANDLING_EQUIPMENT,
                equipment_model="叉车-FC002",
                quantity=5,
                max_load_capacity=3000,
                loading_speed=1500,
                unloading_speed=2000,
                operation_radius=50,
                unit_operation_duration=0.8,
                efficiency_factor=0.85,
                maintenance_interval=150,
                operational_status=EquipmentStatusEnum.AVAILABLE
            )
        },
        {
            "name": "牵引车配置",
            "config": EquipmentConfig(
                equipment_type=EquipmentTypeEnum.SUPPORT_EQUIPMENT,
                equipment_model="牵引车-TC003",
                quantity=1,
                max_load_capacity=50000,
                loading_speed=8000,
                unloading_speed=10000,
                operation_radius=200,
                unit_operation_duration=2.0,
                efficiency_factor=0.90,
                maintenance_interval=400,
                operational_status=EquipmentStatusEnum.AVAILABLE
            )
        }
    ]
    
    print("1. 不同类型设备配置展示:")
    for item in equipment_configs:
        config = item["config"]
        print(f"\n{item['name']}:")
        print(f"  - 类型: {config.equipment_type}")
        print(f"  - 型号: {config.equipment_model}")
        print(f"  - 数量: {config.quantity}")
        print(f"  - 单机载重: {config.max_load_capacity}kg")
        print(f"  - 总载重: {config.max_load_capacity * config.quantity}kg")
        print(f"  - 装载效率: {config.loading_speed}kg/h")
        print(f"  - 作业半径: {config.operation_radius}m")
    
    # 2. 参数合理性验证示例
    print("\n2. 参数合理性验证:")
    
    def validate_equipment_parameters(config: EquipmentConfig) -> list:
        """验证设备参数的合理性"""
        errors = []
        
        # 验证载重和速度的匹配性
        if config.max_load_capacity < 1000 and config.loading_speed > 10000:
            errors.append("小型设备的装载速度设置过高")
        
        # 验证效率系数
        if not (0 <= config.efficiency_factor <= 1):
            errors.append("效率系数必须在0-1之间")
        
        # 验证维护间隔
        if config.maintenance_interval < 24:
            errors.append("维护间隔不能少于24小时")
        
        # 验证作业半径
        if config.operation_radius <= 0:
            errors.append("作业半径必须大于0")
        
        return errors
    
    for item in equipment_configs:
        config = item["config"]
        errors = validate_equipment_parameters(config)
        
        if errors:
            print(f"{item['name']} 验证失败:")
            for error in errors:
                print(f"  - {error}")
        else:
            print(f"{item['name']} 验证通过 ✅")


async def demonstrate_equipment_queries():
    """
    演示设备配置查询操作
    """
    print("\n=== 设备配置查询操作示例 ===\n")
    
    from sqlalchemy import select, and_, or_, func
    
    async for db_session in get_database_session():
        try:
            # 1. 按设备类型查询
            print("1. 查询所有装载车设备")
            query = select(EquipmentConfig).where(
                EquipmentConfig.equipment_type == EquipmentTypeEnum.LOADING_VEHICLE
            )
            result = await db_session.execute(query)
            loading_vehicles = result.scalars().all()
            
            print(f"找到 {len(loading_vehicles)} 个装载车配置:")
            for equipment in loading_vehicles:
                print(f"  - {equipment.equipment_model} (数量: {equipment.quantity})")
            
            # 2. 按状态和载重能力查询
            print("\n2. 查询可用且载重大于15吨的设备")
            query = select(EquipmentConfig).where(
                and_(
                    EquipmentConfig.operational_status == EquipmentStatusEnum.AVAILABLE,
                    EquipmentConfig.max_load_capacity > 15000
                )
            )
            result = await db_session.execute(query)
            heavy_equipment = result.scalars().all()
            
            print(f"找到 {len(heavy_equipment)} 个重型设备:")
            for equipment in heavy_equipment:
                print(f"  - {equipment.equipment_model} ({equipment.max_load_capacity}kg)")
            
            # 3. 统计查询
            print("\n3. 设备统计信息")
            
            # 按类型统计数量
            query = select(
                EquipmentConfig.equipment_type,
                func.count(EquipmentConfig.id).label('count'),
                func.sum(EquipmentConfig.quantity).label('total_quantity')
            ).group_by(EquipmentConfig.equipment_type)
            
            result = await db_session.execute(query)
            statistics = result.all()
            
            print("按类型统计:")
            for stat in statistics:
                print(f"  - {stat.equipment_type}: {stat.count}种配置, 总数量{stat.total_quantity}")
            
            # 4. 复合条件查询
            print("\n4. 查询高效率或大载重的设备")
            query = select(EquipmentConfig).where(
                or_(
                    EquipmentConfig.efficiency_factor > 0.9,
                    EquipmentConfig.max_load_capacity > 30000
                )
            )
            result = await db_session.execute(query)
            efficient_or_heavy = result.scalars().all()
            
            print(f"找到 {len(efficient_or_heavy)} 个高效率或大载重设备")
            
        except Exception as e:
            print(f"查询操作错误: {e}")
        finally:
            await db_session.close()
            break


if __name__ == "__main__":
    asyncio.run(demonstrate_equipment_model_usage())
    asyncio.run(demonstrate_equipment_types_and_validation())
    asyncio.run(demonstrate_equipment_queries())
