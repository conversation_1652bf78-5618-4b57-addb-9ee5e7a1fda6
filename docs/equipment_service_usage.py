#!/usr/bin/env python3
"""
设备配置服务层使用示例

演示EquipmentService的初始化和主要方法调用
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.services.equipment import EquipmentService
from src.schemas.equipment import (
    EquipmentCreateSchema,
    EquipmentUpdateSchema
)
from src.database.models.equipment import EquipmentTypeEnum, EquipmentStatusEnum
from src.database.connection import get_database_session


async def demonstrate_equipment_service_usage():
    """
    演示EquipmentService的完整使用流程
    """
    print("=== EquipmentService 使用示例 ===\n")
    
    async for db_session in get_database_session():
        try:
            # 1. 初始化服务
            print("1. 初始化EquipmentService")
            equipment_service = EquipmentService(db_session)
            print("✅ EquipmentService初始化完成")
            
            # 2. 创建设备配置
            print("\n2. 创建新设备配置")
            
            # 构建创建数据
            create_data = EquipmentCreateSchema(
                equipment_type=EquipmentTypeEnum.LOADING_VEHICLE,
                equipment_model="技术文档示例装载车-DOC001",
                quantity=2,
                max_load_capacity=18000,      # 最大载重能力（kg）
                loading_speed=4500,           # 装载速度（kg/h）
                unloading_speed=5500,         # 卸载速度（kg/h）
                operation_radius=120,         # 作业半径（m）
                power_consumption=95,         # 功耗（kW）
                unit_operation_duration=1.3,  # 单位作业时长（h）
                efficiency_factor=0.89,       # 效率系数（0-1）
                maintenance_interval=280,     # 维护间隔（小时）
                operational_status=EquipmentStatusEnum.AVAILABLE,
                maintenance_info={
                    "last_maintenance_date": "2025-07-20",
                    "next_maintenance_date": "2025-08-20",
                    "maintenance_type": "routine",
                    "responsible_technician": "张工程师"
                },
                remarks="技术文档演示用设备配置"
            )
            
            # 调用创建方法
            created_equipment = await equipment_service.create_equipment_config(create_data)
            equipment_id = created_equipment.id
            
            print(f"✅ 设备配置创建成功")
            print(f"   设备ID: {equipment_id}")
            print(f"   设备型号: {created_equipment.equipment_model}")
            print(f"   设备类型: {created_equipment.equipment_type}")
            print(f"   数量: {created_equipment.quantity}")
            print(f"   最大载重: {created_equipment.max_load_capacity}kg")
            print(f"   创建时间: {created_equipment.created_at}")
            
            # 3. 获取设备配置详情
            print("\n3. 获取设备配置详情")
            equipment_detail = await equipment_service.get_equipment_config_by_id(equipment_id)
            
            if equipment_detail:
                print(f"✅ 设备配置详情获取成功")
                print(f"   设备型号: {equipment_detail.equipment_model}")
                print(f"   装载速度: {equipment_detail.loading_speed}kg/h")
                print(f"   卸载速度: {equipment_detail.unloading_speed}kg/h")
                print(f"   效率系数: {equipment_detail.efficiency_factor}")
                print(f"   运行状态: {equipment_detail.operational_status}")
                
                # 显示维护信息
                maintenance = equipment_detail.maintenance_info
                print(f"   维护信息:")
                print(f"     - 上次维护: {maintenance.get('last_maintenance_date')}")
                print(f"     - 下次维护: {maintenance.get('next_maintenance_date')}")
                print(f"     - 负责技师: {maintenance.get('responsible_technician')}")
            else:
                print("❌ 设备配置不存在")
            
            # 4. 获取设备配置列表
            print("\n4. 获取设备配置列表")
            equipment_list = await equipment_service.get_equipment_config_list(
                page=1,
                page_size=10,
                equipment_type=EquipmentTypeEnum.LOADING_VEHICLE,
                status=EquipmentStatusEnum.AVAILABLE,
                search_keyword="技术文档"
            )
            
            print(f"✅ 设备配置列表获取成功")
            print(f"   总数量: {equipment_list.total}")
            print(f"   当前页: {equipment_list.page}")
            print(f"   每页数量: {equipment_list.page_size}")
            print(f"   总页数: {equipment_list.total_pages}")
            print(f"   设备配置数量: {len(equipment_list.equipment_configs)}")
            
            for equipment in equipment_list.equipment_configs:
                print(f"   - {equipment.equipment_model} ({equipment.equipment_type}, 数量: {equipment.quantity})")
            
            # 5. 更新设备配置
            print("\n5. 更新设备配置")
            update_data = EquipmentUpdateSchema(
                equipment_model="更新后的技术文档示例装载车-DOC001-V2",
                quantity=3,  # 增加数量
                efficiency_factor=0.92,  # 提高效率
                operational_status=EquipmentStatusEnum.MAINTENANCE,  # 更改状态
                maintenance_info={
                    "last_maintenance_date": "2025-07-20",
                    "next_maintenance_date": "2025-08-20",
                    "maintenance_type": "upgrade",  # 升级维护
                    "responsible_technician": "李工程师",
                    "upgrade_details": "效率优化升级"
                },
                remarks="已升级的技术文档演示用设备配置"
            )
            
            updated_equipment = await equipment_service.update_equipment_config(
                equipment_id, update_data
            )
            
            if updated_equipment:
                print(f"✅ 设备配置更新成功")
                print(f"   新型号: {updated_equipment.equipment_model}")
                print(f"   新数量: {updated_equipment.quantity}")
                print(f"   新效率: {updated_equipment.efficiency_factor}")
                print(f"   新状态: {updated_equipment.operational_status}")
                print(f"   更新时间: {updated_equipment.updated_at}")
            else:
                print("❌ 设备配置更新失败")
            
            # 6. 验证设备配置参数
            print("\n6. 验证设备配置参数")
            validation_errors = await equipment_service.validate_equipment_parameters(
                create_data
            )
            
            if validation_errors:
                print("❌ 设备配置参数验证失败:")
                for error in validation_errors:
                    print(f"   - {error}")
            else:
                print("✅ 设备配置参数验证通过")
            
            # 7. 计算设备性能指标
            print("\n7. 计算设备性能指标")
            if updated_equipment:
                total_capacity = updated_equipment.max_load_capacity * updated_equipment.quantity
                effective_capacity = total_capacity * updated_equipment.efficiency_factor
                total_loading_speed = updated_equipment.loading_speed * updated_equipment.quantity
                effective_loading_speed = total_loading_speed * updated_equipment.efficiency_factor
                
                print(f"性能指标计算:")
                print(f"   - 总载重能力: {total_capacity}kg")
                print(f"   - 有效载重能力: {effective_capacity:.0f}kg")
                print(f"   - 总装载速度: {total_loading_speed}kg/h")
                print(f"   - 有效装载速度: {effective_loading_speed:.0f}kg/h")
                print(f"   - 单位时间处理能力: {effective_loading_speed * updated_equipment.unit_operation_duration:.0f}kg")
            
            # 8. 删除设备配置
            print("\n8. 删除设备配置")
            delete_success = await equipment_service.delete_equipment_config(equipment_id)
            
            if delete_success:
                print("✅ 设备配置删除成功")
                
                # 验证删除
                deleted_equipment = await equipment_service.get_equipment_config_by_id(equipment_id)
                if deleted_equipment is None:
                    print("✅ 确认设备配置已从数据库中删除")
            else:
                print("❌ 设备配置删除失败")
            
        except Exception as e:
            print(f"❌ 操作过程中发生错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


async def demonstrate_equipment_service_batch_operations():
    """
    演示EquipmentService的批量操作
    """
    print("\n=== EquipmentService 批量操作示例 ===\n")
    
    async for db_session in get_database_session():
        try:
            equipment_service = EquipmentService(db_session)
            
            # 1. 批量创建不同类型的设备配置
            print("1. 批量创建不同类型的设备配置")
            
            equipment_configs = [
                EquipmentCreateSchema(
                    equipment_type=EquipmentTypeEnum.LOADING_VEHICLE,
                    equipment_model="批量测试装载车-BATCH001",
                    quantity=2,
                    max_load_capacity=20000,
                    loading_speed=5000,
                    unloading_speed=6000,
                    operation_radius=100,
                    unit_operation_duration=1.2,
                    efficiency_factor=0.88,
                    maintenance_interval=250,
                    operational_status=EquipmentStatusEnum.AVAILABLE
                ),
                EquipmentCreateSchema(
                    equipment_type=EquipmentTypeEnum.HANDLING_EQUIPMENT,
                    equipment_model="批量测试叉车-BATCH002",
                    quantity=4,
                    max_load_capacity=3000,
                    loading_speed=1500,
                    unloading_speed=2000,
                    operation_radius=50,
                    unit_operation_duration=0.8,
                    efficiency_factor=0.85,
                    maintenance_interval=150,
                    operational_status=EquipmentStatusEnum.AVAILABLE
                ),
                EquipmentCreateSchema(
                    equipment_type=EquipmentTypeEnum.SUPPORT_EQUIPMENT,
                    equipment_model="批量测试牵引车-BATCH003",
                    quantity=1,
                    max_load_capacity=50000,
                    loading_speed=8000,
                    unloading_speed=10000,
                    operation_radius=200,
                    unit_operation_duration=2.0,
                    efficiency_factor=0.90,
                    maintenance_interval=400,
                    operational_status=EquipmentStatusEnum.AVAILABLE
                )
            ]
            
            created_ids = []
            for i, config_data in enumerate(equipment_configs, 1):
                created_equipment = await equipment_service.create_equipment_config(config_data)
                created_ids.append(created_equipment.id)
                print(f"   ✅ 创建设备配置 {i}: {created_equipment.equipment_model}")
            
            # 2. 批量查询和统计
            print(f"\n2. 批量查询和统计")
            
            # 查询所有可用设备
            available_equipment_list = await equipment_service.get_equipment_config_list(
                page=1,
                page_size=50,
                status=EquipmentStatusEnum.AVAILABLE
            )
            
            print(f"   可用设备总数: {available_equipment_list.total}")
            
            # 按类型统计
            type_stats = {}
            for equipment in available_equipment_list.equipment_configs:
                equipment_type = equipment.equipment_type
                if equipment_type not in type_stats:
                    type_stats[equipment_type] = {
                        'count': 0,
                        'total_quantity': 0,
                        'total_capacity': 0
                    }
                type_stats[equipment_type]['count'] += 1
                type_stats[equipment_type]['total_quantity'] += equipment.quantity
                type_stats[equipment_type]['total_capacity'] += equipment.max_load_capacity * equipment.quantity
            
            print("   按类型统计:")
            for equipment_type, stats in type_stats.items():
                print(f"     - {equipment_type}: {stats['count']}种配置, "
                      f"总数量{stats['total_quantity']}, "
                      f"总载重{stats['total_capacity']}kg")
            
            # 3. 批量更新状态
            print(f"\n3. 批量更新设备状态")
            
            for equipment_id in created_ids:
                update_data = EquipmentUpdateSchema(
                    operational_status=EquipmentStatusEnum.MAINTENANCE,
                    maintenance_info={
                        "maintenance_start": "2025-08-01",
                        "maintenance_type": "scheduled",
                        "estimated_duration": "2天"
                    }
                )
                
                updated_equipment = await equipment_service.update_equipment_config(
                    equipment_id, update_data
                )
                
                if updated_equipment:
                    print(f"   ✅ 设备 {updated_equipment.equipment_model} 状态更新为维护中")
            
            # 4. 批量删除
            print(f"\n4. 批量删除设备配置")
            
            deleted_count = 0
            for equipment_id in created_ids:
                if await equipment_service.delete_equipment_config(equipment_id):
                    deleted_count += 1
            
            print(f"   ✅ 成功删除 {deleted_count} 个设备配置")
            
        except Exception as e:
            print(f"❌ 批量操作过程中发生错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


async def demonstrate_equipment_service_advanced_queries():
    """
    演示EquipmentService的高级查询功能
    """
    print("\n=== EquipmentService 高级查询示例 ===\n")
    
    async for db_session in get_database_session():
        try:
            equipment_service = EquipmentService(db_session)
            
            # 先创建一些测试数据
            test_configs = [
                EquipmentCreateSchema(
                    equipment_type=EquipmentTypeEnum.LOADING_VEHICLE,
                    equipment_model="高效装载车-HE001",
                    quantity=1,
                    max_load_capacity=25000,
                    loading_speed=7000,
                    unloading_speed=8000,
                    operation_radius=150,
                    unit_operation_duration=1.0,
                    efficiency_factor=0.95,  # 高效率
                    maintenance_interval=300,
                    operational_status=EquipmentStatusEnum.AVAILABLE
                ),
                EquipmentCreateSchema(
                    equipment_type=EquipmentTypeEnum.LOADING_VEHICLE,
                    equipment_model="标准装载车-ST001",
                    quantity=2,
                    max_load_capacity=15000,
                    loading_speed=4000,
                    unloading_speed=5000,
                    operation_radius=100,
                    unit_operation_duration=1.5,
                    efficiency_factor=0.80,  # 标准效率
                    maintenance_interval=200,
                    operational_status=EquipmentStatusEnum.AVAILABLE
                )
            ]
            
            created_ids = []
            for config in test_configs:
                created = await equipment_service.create_equipment_config(config)
                created_ids.append(created.id)
            
            # 1. 按效率筛选查询
            print("1. 高级筛选查询示例")
            
            # 查询高效率设备（效率 > 0.9）
            high_efficiency_list = await equipment_service.get_equipment_config_list(
                page=1,
                page_size=10,
                equipment_type=EquipmentTypeEnum.LOADING_VEHICLE
            )
            
            high_efficiency_equipment = [
                eq for eq in high_efficiency_list.equipment_configs
                if eq.efficiency_factor > 0.9
            ]
            
            print(f"   高效率设备（效率>0.9）: {len(high_efficiency_equipment)}个")
            for equipment in high_efficiency_equipment:
                print(f"     - {equipment.equipment_model}: 效率{equipment.efficiency_factor}")
            
            # 2. 按载重能力筛选
            heavy_equipment = [
                eq for eq in high_efficiency_list.equipment_configs
                if eq.max_load_capacity > 20000
            ]
            
            print(f"   重型设备（载重>20吨）: {len(heavy_equipment)}个")
            for equipment in heavy_equipment:
                print(f"     - {equipment.equipment_model}: 载重{equipment.max_load_capacity}kg")
            
            # 3. 综合性能评估
            print(f"\n2. 综合性能评估")
            
            all_equipment = high_efficiency_list.equipment_configs
            
            for equipment in all_equipment:
                # 计算综合性能指标
                total_capacity = equipment.max_load_capacity * equipment.quantity
                effective_capacity = total_capacity * equipment.efficiency_factor
                hourly_throughput = equipment.loading_speed * equipment.quantity * equipment.efficiency_factor
                
                performance_score = (
                    effective_capacity / 1000 * 0.4 +  # 载重权重40%
                    hourly_throughput / 1000 * 0.4 +   # 速度权重40%
                    equipment.efficiency_factor * 100 * 0.2  # 效率权重20%
                )
                
                print(f"   {equipment.equipment_model}:")
                print(f"     - 有效载重: {effective_capacity:.0f}kg")
                print(f"     - 小时吞吐: {hourly_throughput:.0f}kg/h")
                print(f"     - 综合评分: {performance_score:.1f}")
            
            # 清理测试数据
            for equipment_id in created_ids:
                await equipment_service.delete_equipment_config(equipment_id)
            
        except Exception as e:
            print(f"❌ 高级查询过程中发生错误: {e}")
            await db_session.rollback()
        finally:
            await db_session.close()
            break


if __name__ == "__main__":
    asyncio.run(demonstrate_equipment_service_usage())
    asyncio.run(demonstrate_equipment_service_batch_operations())
    asyncio.run(demonstrate_equipment_service_advanced_queries())
