#!/usr/bin/env python3
"""
API客户端使用示例

演示如何通过HTTP客户端调用各个模块的API接口
"""

import asyncio
import json
from typing import Dict, Any, Optional
import httpx


class XiaonengAPIClient:
    """
    小能航空运输保障效能算法库API客户端
    
    提供对所有API接口的封装调用方法
    """
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化API客户端
        
        Args:
            base_url: API服务器基础URL
        """
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"
        self.timeout = httpx.Timeout(30.0)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.client = httpx.AsyncClient(timeout=self.timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.client.aclose()
    
    # ==================== 场景管理API ====================
    
    async def create_scenario(self, scenario_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建场景
        
        Args:
            scenario_data: 场景创建数据
            
        Returns:
            Dict[str, Any]: 创建的场景信息
        """
        response = await self.client.post(
            f"{self.api_base}/scenarios/",
            json=scenario_data
        )
        response.raise_for_status()
        return response.json()
    
    async def get_scenario_list(
        self,
        page: int = 1,
        page_size: int = 20,
        scenario_type: Optional[str] = None,
        task_type: Optional[str] = None,
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取场景列表
        
        Args:
            page: 页码
            page_size: 每页数量
            scenario_type: 场景类型筛选
            task_type: 任务类型筛选
            search: 搜索关键词
            
        Returns:
            Dict[str, Any]: 场景列表响应
        """
        params = {"page": page, "page_size": page_size}
        if scenario_type:
            params["scenario_type"] = scenario_type
        if task_type:
            params["task_type"] = task_type
        if search:
            params["search"] = search
        
        response = await self.client.get(
            f"{self.api_base}/scenarios/",
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    async def get_scenario_detail(self, scenario_id: str) -> Dict[str, Any]:
        """
        获取场景详情
        
        Args:
            scenario_id: 场景ID
            
        Returns:
            Dict[str, Any]: 场景详细信息
        """
        response = await self.client.get(f"{self.api_base}/scenarios/{scenario_id}")
        response.raise_for_status()
        return response.json()
    
    async def update_scenario(
        self,
        scenario_id: str,
        update_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        更新场景
        
        Args:
            scenario_id: 场景ID
            update_data: 更新数据
            
        Returns:
            Dict[str, Any]: 更新后的场景信息
        """
        response = await self.client.put(
            f"{self.api_base}/scenarios/{scenario_id}",
            json=update_data
        )
        response.raise_for_status()
        return response.json()
    
    async def delete_scenario(self, scenario_id: str) -> bool:
        """
        删除场景
        
        Args:
            scenario_id: 场景ID
            
        Returns:
            bool: 删除成功返回True
        """
        response = await self.client.delete(f"{self.api_base}/scenarios/{scenario_id}")
        return response.status_code == 204
    
    # ==================== 设备配置管理API ====================
    
    async def create_equipment_config(self, equipment_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建设备配置
        
        Args:
            equipment_data: 设备配置创建数据
            
        Returns:
            Dict[str, Any]: 创建的设备配置信息
        """
        response = await self.client.post(
            f"{self.api_base}/equipment-configs/",
            json=equipment_data
        )
        response.raise_for_status()
        return response.json()
    
    async def get_equipment_config_list(
        self,
        page: int = 1,
        page_size: int = 20,
        equipment_type: Optional[str] = None,
        status: Optional[str] = None,
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取设备配置列表
        
        Args:
            page: 页码
            page_size: 每页数量
            equipment_type: 设备类型筛选
            status: 状态筛选
            search: 搜索关键词
            
        Returns:
            Dict[str, Any]: 设备配置列表响应
        """
        params = {"page": page, "page_size": page_size}
        if equipment_type:
            params["equipment_type"] = equipment_type
        if status:
            params["status"] = status
        if search:
            params["search"] = search
        
        response = await self.client.get(
            f"{self.api_base}/equipment-configs/",
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    async def get_equipment_config_detail(self, equipment_id: str) -> Dict[str, Any]:
        """
        获取设备配置详情
        
        Args:
            equipment_id: 设备配置ID
            
        Returns:
            Dict[str, Any]: 设备配置详细信息
        """
        response = await self.client.get(f"{self.api_base}/equipment-configs/{equipment_id}")
        response.raise_for_status()
        return response.json()
    
    async def update_equipment_config(
        self,
        equipment_id: str,
        update_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        更新设备配置
        
        Args:
            equipment_id: 设备配置ID
            update_data: 更新数据
            
        Returns:
            Dict[str, Any]: 更新后的设备配置信息
        """
        response = await self.client.put(
            f"{self.api_base}/equipment-configs/{equipment_id}",
            json=update_data
        )
        response.raise_for_status()
        return response.json()
    
    async def delete_equipment_config(self, equipment_id: str) -> bool:
        """
        删除设备配置
        
        Args:
            equipment_id: 设备配置ID
            
        Returns:
            bool: 删除成功返回True
        """
        response = await self.client.delete(f"{self.api_base}/equipment-configs/{equipment_id}")
        return response.status_code == 204
    
    # ==================== 飞机配置管理API ====================
    
    async def create_aircraft_config(self, aircraft_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建飞机配置
        
        Args:
            aircraft_data: 飞机配置创建数据
            
        Returns:
            Dict[str, Any]: 创建的飞机配置信息
        """
        response = await self.client.post(
            f"{self.api_base}/aircraft-configs/",
            json=aircraft_data
        )
        response.raise_for_status()
        return response.json()
    
    async def get_aircraft_config_list(
        self,
        page: int = 1,
        page_size: int = 20,
        aircraft_model: Optional[str] = None,
        status: Optional[str] = None,
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取飞机配置列表
        
        Args:
            page: 页码
            page_size: 每页数量
            aircraft_model: 飞机型号筛选
            status: 状态筛选
            search: 搜索关键词
            
        Returns:
            Dict[str, Any]: 飞机配置列表响应
        """
        params = {"page": page, "page_size": page_size}
        if aircraft_model:
            params["aircraft_model"] = aircraft_model
        if status:
            params["status"] = status
        if search:
            params["search"] = search
        
        response = await self.client.get(
            f"{self.api_base}/aircraft-configs/",
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    async def get_aircraft_config_detail(self, aircraft_id: str) -> Dict[str, Any]:
        """
        获取飞机配置详情
        
        Args:
            aircraft_id: 飞机配置ID
            
        Returns:
            Dict[str, Any]: 飞机配置详细信息
        """
        response = await self.client.get(f"{self.api_base}/aircraft-configs/{aircraft_id}")
        response.raise_for_status()
        return response.json()
    
    async def update_aircraft_config(
        self,
        aircraft_id: str,
        update_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        更新飞机配置
        
        Args:
            aircraft_id: 飞机配置ID
            update_data: 更新数据
            
        Returns:
            Dict[str, Any]: 更新后的飞机配置信息
        """
        response = await self.client.put(
            f"{self.api_base}/aircraft-configs/{aircraft_id}",
            json=update_data
        )
        response.raise_for_status()
        return response.json()
    
    async def delete_aircraft_config(self, aircraft_id: str) -> bool:
        """
        删除飞机配置
        
        Args:
            aircraft_id: 飞机配置ID
            
        Returns:
            bool: 删除成功返回True
        """
        response = await self.client.delete(f"{self.api_base}/aircraft-configs/{aircraft_id}")
        return response.status_code == 204
    
    # ==================== 配置方案管理API ====================
    
    async def create_configuration_scheme(self, scheme_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建配置方案
        
        Args:
            scheme_data: 配置方案创建数据
            
        Returns:
            Dict[str, Any]: 创建的配置方案信息
        """
        response = await self.client.post(
            f"{self.api_base}/configuration-schemes/",
            json=scheme_data
        )
        response.raise_for_status()
        return response.json()
    
    async def get_configuration_scheme_list(
        self,
        page: int = 1,
        page_size: int = 20,
        scheme_type: Optional[str] = None,
        status: Optional[str] = None,
        category: Optional[str] = None,
        search: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        获取配置方案列表
        
        Args:
            page: 页码
            page_size: 每页数量
            scheme_type: 方案类型筛选
            status: 状态筛选
            category: 分类筛选
            search: 搜索关键词
            
        Returns:
            Dict[str, Any]: 配置方案列表响应
        """
        params = {"page": page, "page_size": page_size}
        if scheme_type:
            params["scheme_type"] = scheme_type
        if status:
            params["status"] = status
        if category:
            params["category"] = category
        if search:
            params["search"] = search
        
        response = await self.client.get(
            f"{self.api_base}/configuration-schemes/",
            params=params
        )
        response.raise_for_status()
        return response.json()
    
    async def get_configuration_scheme_detail(self, scheme_id: str) -> Dict[str, Any]:
        """
        获取配置方案详情
        
        Args:
            scheme_id: 配置方案ID
            
        Returns:
            Dict[str, Any]: 配置方案详细信息
        """
        response = await self.client.get(f"{self.api_base}/configuration-schemes/{scheme_id}")
        response.raise_for_status()
        return response.json()
    
    async def update_configuration_scheme(
        self,
        scheme_id: str,
        update_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        更新配置方案
        
        Args:
            scheme_id: 配置方案ID
            update_data: 更新数据
            
        Returns:
            Dict[str, Any]: 更新后的配置方案信息
        """
        response = await self.client.put(
            f"{self.api_base}/configuration-schemes/{scheme_id}",
            json=update_data
        )
        response.raise_for_status()
        return response.json()
    
    async def delete_configuration_scheme(self, scheme_id: str) -> bool:
        """
        删除配置方案
        
        Args:
            scheme_id: 配置方案ID
            
        Returns:
            bool: 删除成功返回True
        """
        response = await self.client.delete(f"{self.api_base}/configuration-schemes/{scheme_id}")
        return response.status_code == 204
    
    async def validate_configuration_scheme(self, scheme_id: str) -> Dict[str, Any]:
        """
        验证配置方案
        
        Args:
            scheme_id: 配置方案ID
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        response = await self.client.post(f"{self.api_base}/configuration-schemes/{scheme_id}/validate")
        response.raise_for_status()
        return response.json()
    
    async def get_scheme_versions(self, scheme_id: str) -> Dict[str, Any]:
        """
        获取方案版本列表
        
        Args:
            scheme_id: 配置方案ID
            
        Returns:
            Dict[str, Any]: 版本列表响应
        """
        response = await self.client.get(f"{self.api_base}/configuration-schemes/{scheme_id}/versions")
        response.raise_for_status()
        return response.json()


async def demonstrate_api_client_usage():
    """
    演示API客户端的完整使用流程
    """
    print("=== API客户端使用示例 ===\n")
    
    async with XiaonengAPIClient() as client:
        try:
            # 1. 场景管理API演示
            print("1. 场景管理API演示")
            
            # 创建场景
            scenario_data = {
                "scenario_name": "API客户端测试场景",
                "scenario_type": "api_test",
                "description": "用于API客户端测试的示例场景",
                "task_type": "equipment_transport",
                "environment_conditions": {
                    "weather": {"visibility": 10000, "wind_speed": 12},
                    "terrain": "plain"
                },
                "resource_constraints": {
                    "transport_capacity": {"max_aircraft": 3}
                },
                "mission_requirements": {
                    "cargo_weight": 80000,
                    "origin": "api_test_A",
                    "destination": "api_test_B"
                },
                "threat_factors": {
                    "enemy_threats": {"air_defense": "low"}
                },
                "threat_level": "low",
                "created_by": "api_client_demo"
            }
            
            created_scenario = await client.create_scenario(scenario_data)
            scenario_id = created_scenario["id"]
            print(f"   ✅ 创建场景成功: {created_scenario['scenario_name']} (ID: {scenario_id})")
            
            # 获取场景列表
            scenario_list = await client.get_scenario_list(
                page=1,
                page_size=10,
                search="API客户端"
            )
            print(f"   ✅ 获取场景列表成功: 共{scenario_list['total']}个场景")
            
            # 获取场景详情
            scenario_detail = await client.get_scenario_detail(scenario_id)
            print(f"   ✅ 获取场景详情成功: {scenario_detail['scenario_name']}")
            
            # 更新场景
            update_data = {
                "scenario_name": "更新后的API客户端测试场景",
                "description": "已更新的API客户端测试场景描述"
            }
            updated_scenario = await client.update_scenario(scenario_id, update_data)
            print(f"   ✅ 更新场景成功: {updated_scenario['scenario_name']}")
            
            # 删除场景
            delete_success = await client.delete_scenario(scenario_id)
            print(f"   ✅ 删除场景成功: {delete_success}")
            
            # 2. 设备配置管理API演示
            print(f"\n2. 设备配置管理API演示")
            
            # 创建设备配置
            equipment_data = {
                "equipment_type": "loading_vehicle",
                "equipment_model": "API测试装载车-CLIENT001",
                "quantity": 2,
                "max_load_capacity": 18000,
                "loading_speed": 4500,
                "unloading_speed": 5500,
                "operation_radius": 120,
                "power_consumption": 90,
                "unit_operation_duration": 1.3,
                "efficiency_factor": 0.87,
                "maintenance_interval": 280,
                "operational_status": "available",
                "maintenance_info": {"test": "api_client"},
                "remarks": "API客户端测试用设备配置"
            }
            
            created_equipment = await client.create_equipment_config(equipment_data)
            equipment_id = created_equipment["id"]
            print(f"   ✅ 创建设备配置成功: {created_equipment['equipment_model']} (ID: {equipment_id})")
            
            # 获取设备配置列表
            equipment_list = await client.get_equipment_config_list(
                equipment_type="loading_vehicle",
                search="API测试"
            )
            print(f"   ✅ 获取设备配置列表成功: 共{equipment_list['total']}个配置")
            
            # 删除设备配置
            delete_success = await client.delete_equipment_config(equipment_id)
            print(f"   ✅ 删除设备配置成功: {delete_success}")
            
            # 3. 飞机配置管理API演示
            print(f"\n3. 飞机配置管理API演示")
            
            # 创建飞机配置
            aircraft_data = {
                "aircraft_model": "Y-8",
                "quantity": 1,
                "payload_capacity": 20000,
                "operational_range": 5700,
                "cruise_speed": 500,
                "fuel_consumption": 1800,
                "loading_time": 40,
                "unloading_time": 30,
                "ground_taxi_time": 15,
                "crew_requirements": {
                    "pilots": 2,
                    "flight_engineers": 0,
                    "loadmasters": 1
                },
                "compatible_equipment": ["API测试装载车"],
                "maintenance_schedule": {
                    "routine_maintenance_hours": 100,
                    "major_maintenance_hours": 1000,
                    "last_maintenance_date": "2025-07-15"
                },
                "status": "available",
                "remarks": "API客户端测试用飞机配置"
            }
            
            created_aircraft = await client.create_aircraft_config(aircraft_data)
            aircraft_id = created_aircraft["id"]
            print(f"   ✅ 创建飞机配置成功: {created_aircraft['aircraft_model']} (ID: {aircraft_id})")
            
            # 获取飞机配置详情
            aircraft_detail = await client.get_aircraft_config_detail(aircraft_id)
            print(f"   ✅ 获取飞机配置详情成功: 载重{aircraft_detail['payload_capacity']}kg")
            
            # 删除飞机配置
            delete_success = await client.delete_aircraft_config(aircraft_id)
            print(f"   ✅ 删除飞机配置成功: {delete_success}")
            
            # 4. 配置方案管理API演示
            print(f"\n4. 配置方案管理API演示")
            
            # 创建配置方案
            scheme_data = {
                "scheme_name": "API客户端测试配置方案",
                "scheme_type": "mixed_transport",
                "description": "用于API客户端测试的配置方案",
                "equipment_config_ids": [],
                "aircraft_config_ids": [],
                "personnel_config_ids": [],
                "weather_condition_ids": [],
                "scenario_parameters": {"test": "api_client"},
                "operational_parameters": {
                    "max_operation_duration": 12,
                    "safety_margin": 0.2,
                    "efficiency_target": 0.85,
                    "priority_level": "medium"
                },
                "tags": ["API测试", "客户端演示"],
                "category": "测试",
                "created_by": "api_client_demo"
            }
            
            created_scheme = await client.create_configuration_scheme(scheme_data)
            scheme_id = created_scheme["id"]
            print(f"   ✅ 创建配置方案成功: {created_scheme['scheme_name']} (ID: {scheme_id})")
            
            # 验证配置方案
            validation_result = await client.validate_configuration_scheme(scheme_id)
            print(f"   ✅ 验证配置方案成功: 状态{validation_result['overall_status']}")
            
            # 获取方案版本列表
            version_list = await client.get_scheme_versions(scheme_id)
            print(f"   ✅ 获取方案版本列表成功: 共{version_list['total']}个版本")
            
            # 删除配置方案
            delete_success = await client.delete_configuration_scheme(scheme_id)
            print(f"   ✅ 删除配置方案成功: {delete_success}")
            
            print(f"\n🎉 API客户端演示完成！所有接口调用成功")
            
        except httpx.HTTPStatusError as e:
            print(f"❌ HTTP错误: {e.response.status_code} - {e.response.text}")
        except Exception as e:
            print(f"❌ 客户端错误: {e}")


if __name__ == "__main__":
    asyncio.run(demonstrate_api_client_usage())
