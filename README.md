# 航空运输保障效能算法库

## 项目简介

航空运输保障效能算法库是一个专业的航空运输保障效能分析和优化系统，提供完整的场景建模、资源配置管理和效能评估功能。

## 第一阶段功能特性

### 核心功能模块

1. **场景定义与参数化模块**
   - 支持多维度场景参数定义
   - 环境条件建模（气象、地形、空域等）
   - 任务需求配置
   - 威胁因素建模

2. **装卸载配置管理模块**
   - 设备配置管理（装载车、叉车、牵引车等）
   - 飞机配置管理（运-20、运-8、运-7等）
   - 人员配置管理（操作员、地勤、指挥员等）
   - 气象条件管理

3. **基础配置方案管理模块**
   - 配置方案创建和编辑
   - 方案版本控制
   - 配置验证和优化建议
   - 方案分类和标签管理

4. **核心API接口**
   - RESTful API设计
   - 标准化响应格式
   - 完整的错误处理
   - API文档自动生成

## 技术架构

### 后端技术栈
- **框架**: FastAPI (Python 3.9+)
- **数据库**: PostgreSQL + SQLAlchemy (异步ORM)
- **缓存**: Redis
- **消息队列**: RabbitMQ
- **数据验证**: Pydantic
- **测试框架**: pytest + httpx

### 数据库设计
- 场景数据表 (scenarios)
- 设备配置表 (equipment_configs)
- 飞机配置表 (aircraft_configs)
- 人员配置表 (personnel_configs)
- 气象条件表 (weather_conditions)
- 配置方案表 (configuration_schemes)
- 方案版本表 (scheme_versions)

## 快速开始

### 环境要求
- Python 3.9+
- PostgreSQL 12+
- Redis 6+
- RabbitMQ 3.8+

### 安装依赖

```bash
# 安装Python依赖
pip install -r requirements.txt

# 或使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows
pip install -r requirements.txt
```

### 环境配置

1. 复制环境配置文件：
```bash
cp .env.example .env
```

2. 修改 `.env` 文件中的数据库连接信息

### 使用Docker启动服务

```bash
# 启动所有服务（数据库、缓存、消息队列）
docker-compose up -d

# 查看服务状态
docker-compose ps
```

### 初始化数据库

```bash
# 初始化数据库表结构
python scripts/init_db.py

# 或重置数据库（删除所有数据后重新创建）
python scripts/init_db.py --reset
```

### 生成测试数据

```bash
# 生成测试数据
python scripts/generate_test_data.py
```

### 启动应用

```bash
# 开发模式启动
uvicorn src.main:app --host 0.0.0.0 --port 8000 --reload

# 生产模式启动
uvicorn src.main:app --host 0.0.0.0 --port 8000
```

### 运行测试

```bash
# 运行完整测试流程（推荐）
python scripts/run_tests.py

# 或分别运行各类测试
python scripts/init_db.py --reset          # 初始化数据库
python scripts/generate_test_data.py       # 生成测试数据
pytest tests/ -v                          # 运行单元测试
python scripts/validate_functionality.py  # 运行功能验证测试
```

## API文档

启动应用后，可以通过以下地址访问API文档：

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc
- **OpenAPI JSON**: http://localhost:8000/openapi.json

## 主要API端点

### 场景管理
- `POST /api/v1/scenarios/` - 创建场景
- `GET /api/v1/scenarios/` - 获取场景列表
- `GET /api/v1/scenarios/{id}` - 获取场景详情
- `PUT /api/v1/scenarios/{id}` - 更新场景
- `DELETE /api/v1/scenarios/{id}` - 删除场景

### 设备配置管理
- `POST /api/v1/equipment-configs/` - 创建设备配置
- `GET /api/v1/equipment-configs/` - 获取设备配置列表
- `GET /api/v1/equipment-configs/{id}` - 获取设备配置详情
- `PUT /api/v1/equipment-configs/{id}` - 更新设备配置
- `DELETE /api/v1/equipment-configs/{id}` - 删除设备配置

### 飞机配置管理
- `POST /api/v1/aircraft-configs/` - 创建飞机配置
- `GET /api/v1/aircraft-configs/` - 获取飞机配置列表
- `GET /api/v1/aircraft-configs/{id}` - 获取飞机配置详情
- `PUT /api/v1/aircraft-configs/{id}` - 更新飞机配置
- `DELETE /api/v1/aircraft-configs/{id}` - 删除飞机配置

### 配置方案管理
- `POST /api/v1/configuration-schemes/` - 创建配置方案
- `GET /api/v1/configuration-schemes/` - 获取配置方案列表
- `GET /api/v1/configuration-schemes/{id}` - 获取配置方案详情
- `PUT /api/v1/configuration-schemes/{id}` - 更新配置方案
- `DELETE /api/v1/configuration-schemes/{id}` - 删除配置方案
- `POST /api/v1/configuration-schemes/{id}/validate` - 验证配置方案
- `GET /api/v1/configuration-schemes/{id}/versions` - 获取方案版本列表

## 项目结构

```
xiaoneng/
├── src/                          # 源代码目录
│   ├── api/                      # API路由层
│   │   ├── v1/                   # API v1版本
│   │   ├── dependencies.py       # API依赖项
│   │   ├── exceptions.py         # 异常处理
│   │   └── middleware.py         # 中间件
│   ├── config/                   # 配置模块
│   │   └── settings.py           # 应用配置
│   ├── database/                 # 数据库模块
│   │   ├── models/               # 数据模型
│   │   └── connection.py         # 数据库连接
│   ├── schemas/                  # 数据传输对象
│   ├── services/                 # 业务逻辑层
│   └── main.py                   # 应用入口
├── tests/                        # 测试目录
├── scripts/                      # 脚本目录
├── migrations/                   # 数据库迁移
├── docker-compose.yml            # Docker编排文件
├── requirements.txt              # Python依赖
└── README.md                     # 项目说明
```

## 开发规范

项目严格遵循 `rules/imported/docs/base-rules.md` 中定义的开发规范：

- 函数复杂度控制（圈复杂度不超过10）
- 函数参数限制（不超过5个）
- 文件长度限制（不超过500行）
- 统一的命名规范和代码风格
- 完整的错误处理和日志记录
- 全面的测试覆盖

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues: [GitHub Issues](https://github.com/your-repo/xiaoneng/issues)
- 邮箱: <EMAIL>
