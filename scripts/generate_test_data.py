#!/usr/bin/env python3
"""
测试数据生成脚本

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 使用描述性的函数命名
- 不使用硬编码数据，通过配置文件管理
"""

import asyncio
import sys
from pathlib import Path
from typing import List, Dict, Any
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.connection import get_database_session
from src.database.models import (
    Scenario, TaskTypeEnum, ScenarioStatusEnum, ThreatLevelEnum,
    EquipmentConfig, EquipmentTypeEnum, EquipmentStatusEnum,
    AircraftConfig, AircraftModelEnum, AircraftStatusEnum,
    PersonnelConfig, PersonnelTypeEnum, SkillLevelEnum,
    WeatherCondition, WeatherTypeEnum, SeverityLevelEnum,
    ConfigurationScheme, SchemeTypeEnum, SchemeStatusEnum
)


class TestDataGenerator:
    """
    测试数据生成器
    
    生成各种类型的测试数据，用于验证系统功能
    """
    
    def __init__(self):
        """初始化测试数据生成器"""
        self.generated_ids = {
            'scenarios': [],
            'equipment_configs': [],
            'aircraft_configs': [],
            'personnel_configs': [],
            'weather_conditions': [],
            'configuration_schemes': []
        }
    
    async def generate_all_test_data(self) -> None:
        """
        生成所有类型的测试数据
        """
        print("开始生成测试数据...")
        
        async for db_session in get_database_session():
            try:
                # 生成场景数据
                await self.generate_scenario_data(db_session)
                
                # 生成设备配置数据
                await self.generate_equipment_config_data(db_session)
                
                # 生成飞机配置数据
                await self.generate_aircraft_config_data(db_session)
                
                # 生成人员配置数据
                await self.generate_personnel_config_data(db_session)
                
                # 生成气象条件数据
                await self.generate_weather_condition_data(db_session)
                
                # 生成配置方案数据
                await self.generate_configuration_scheme_data(db_session)
                
                print("✅ 所有测试数据生成完成")
                
            except Exception as e:
                print(f"❌ 测试数据生成失败: {e}")
                await db_session.rollback()
                raise
            finally:
                await db_session.close()
    
    async def generate_scenario_data(self, db_session) -> None:
        """
        生成场景测试数据
        
        Args:
            db_session: 数据库会话
        """
        print("生成场景数据...")
        
        scenario_templates = [
            {
                "scenario_name": "重型装备战略投送场景",
                "scenario_type": "strategic_transport",
                "description": "大型重型装备的远程战略投送任务",
                "task_type": TaskTypeEnum.EQUIPMENT_TRANSPORT,
                "threat_level": ThreatLevelEnum.HIGH
            },
            {
                "scenario_name": "人员快速投送场景",
                "scenario_type": "tactical_transport",
                "description": "紧急情况下的人员快速投送任务",
                "task_type": TaskTypeEnum.PERSONNEL_TRANSPORT,
                "threat_level": ThreatLevelEnum.MEDIUM
            },
            {
                "scenario_name": "物资补给保障场景",
                "scenario_type": "supply_mission",
                "description": "前线部队的物资补给保障任务",
                "task_type": TaskTypeEnum.MATERIAL_SUPPLY,
                "threat_level": ThreatLevelEnum.LOW
            },
            {
                "scenario_name": "医疗后送救援场景",
                "scenario_type": "medical_evacuation",
                "description": "伤员医疗后送和救援任务",
                "task_type": TaskTypeEnum.MEDICAL_EVACUATION,
                "threat_level": ThreatLevelEnum.MEDIUM
            }
        ]
        
        for template in scenario_templates:
            scenario = Scenario(
                scenario_name=template["scenario_name"],
                scenario_type=template["scenario_type"],
                description=template["description"],
                task_type=template["task_type"],
                environment_conditions=self._generate_environment_conditions(),
                resource_constraints=self._generate_resource_constraints(),
                mission_requirements=self._generate_mission_requirements(),
                threat_factors=self._generate_threat_factors(),
                threat_level=template["threat_level"],
                created_by="test_generator",
                status=ScenarioStatusEnum.ACTIVE
            )
            
            db_session.add(scenario)
            await db_session.flush()
            self.generated_ids['scenarios'].append(scenario.id)
        
        await db_session.commit()
        print(f"✅ 生成了 {len(scenario_templates)} 个场景")
    
    async def generate_equipment_config_data(self, db_session) -> None:
        """
        生成设备配置测试数据
        
        Args:
            db_session: 数据库会话
        """
        print("生成设备配置数据...")
        
        equipment_templates = [
            {
                "equipment_type": EquipmentTypeEnum.LOADING_VEHICLE,
                "equipment_model": "装载车-ZC001",
                "max_load_capacity": 20000,
                "loading_speed": 5000,
                "unloading_speed": 6000
            },
            {
                "equipment_type": EquipmentTypeEnum.HANDLING_EQUIPMENT,
                "equipment_model": "叉车-FC002",
                "max_load_capacity": 5000,
                "loading_speed": 2000,
                "unloading_speed": 2500
            },
            {
                "equipment_type": EquipmentTypeEnum.SUPPORT_EQUIPMENT,
                "equipment_model": "牵引车-QY003",
                "max_load_capacity": 50000,
                "loading_speed": 8000,
                "unloading_speed": 10000
            }
        ]
        
        for template in equipment_templates:
            equipment_config = EquipmentConfig(
                equipment_type=template["equipment_type"],
                equipment_model=template["equipment_model"],
                quantity=random.randint(2, 8),
                max_load_capacity=template["max_load_capacity"],
                loading_speed=template["loading_speed"],
                unloading_speed=template["unloading_speed"],
                operation_radius=random.uniform(50, 200),
                power_consumption=random.uniform(50, 150),
                unit_operation_duration=random.uniform(0.5, 2.0),
                efficiency_factor=random.uniform(0.8, 1.0),
                maintenance_interval=random.randint(100, 500),
                operational_status=EquipmentStatusEnum.AVAILABLE,
                maintenance_info={"last_maintenance": "2025-07-01", "next_maintenance": "2025-08-01"},
                remarks=f"测试用{template['equipment_model']}"
            )
            
            db_session.add(equipment_config)
            await db_session.flush()
            self.generated_ids['equipment_configs'].append(equipment_config.id)
        
        await db_session.commit()
        print(f"✅ 生成了 {len(equipment_templates)} 个设备配置")
    
    async def generate_aircraft_config_data(self, db_session) -> None:
        """
        生成飞机配置测试数据
        
        Args:
            db_session: 数据库会话
        """
        print("生成飞机配置数据...")
        
        aircraft_templates = [
            {
                "aircraft_model": AircraftModelEnum.Y_20,
                "payload_capacity": 66000,
                "operational_range": 4400,
                "cruise_speed": 700,
                "fuel_consumption": 3500
            },
            {
                "aircraft_model": AircraftModelEnum.Y_8,
                "payload_capacity": 20000,
                "operational_range": 5700,
                "cruise_speed": 500,
                "fuel_consumption": 1800
            },
            {
                "aircraft_model": AircraftModelEnum.Y_7,
                "payload_capacity": 5500,
                "operational_range": 1400,
                "cruise_speed": 350,
                "fuel_consumption": 800
            }
        ]
        
        for template in aircraft_templates:
            aircraft_config = AircraftConfig(
                aircraft_model=template["aircraft_model"],
                quantity=random.randint(1, 4),
                payload_capacity=template["payload_capacity"],
                operational_range=template["operational_range"],
                cruise_speed=template["cruise_speed"],
                fuel_consumption=template["fuel_consumption"],
                loading_time=random.uniform(30, 90),
                unloading_time=random.uniform(20, 60),
                ground_taxi_time=random.uniform(10, 30),
                crew_requirements={
                    "pilots": 2,
                    "flight_engineers": 1 if template["aircraft_model"] in [AircraftModelEnum.Y_20] else 0,
                    "loadmasters": 2
                },
                compatible_equipment=["装载车-ZC001", "叉车-FC002"],
                maintenance_schedule={
                    "routine_maintenance_hours": 100,
                    "major_maintenance_hours": 1000,
                    "last_maintenance_date": "2025-07-01"
                },
                status=AircraftStatusEnum.AVAILABLE,
                remarks=f"测试用{template['aircraft_model']}"
            )
            
            db_session.add(aircraft_config)
            await db_session.flush()
            self.generated_ids['aircraft_configs'].append(aircraft_config.id)
        
        await db_session.commit()
        print(f"✅ 生成了 {len(aircraft_templates)} 个飞机配置")
    
    def _generate_environment_conditions(self) -> Dict[str, Any]:
        """生成环境条件数据"""
        return {
            "weather": {
                "visibility": random.randint(5000, 15000),
                "wind_speed": random.uniform(5, 25),
                "precipitation": random.uniform(0, 10),
                "temperature": random.uniform(-10, 35),
                "humidity": random.uniform(30, 90)
            },
            "terrain": random.choice(["plain", "mountain", "desert", "coastal"]),
            "airspace_control": {
                "control_level": random.choice(["normal", "restricted", "prohibited"])
            },
            "electromagnetic_environment": {
                "interference_level": random.choice(["low", "medium", "high"])
            },
            "airport_condition": {
                "runway_length": random.randint(2000, 4000),
                "runway_width": random.randint(30, 60)
            }
        }
    
    def _generate_resource_constraints(self) -> Dict[str, Any]:
        """生成资源约束数据"""
        return {
            "transport_capacity": {
                "max_aircraft": random.randint(3, 10),
                "max_equipment": random.randint(5, 15)
            },
            "support_resources": {
                "fuel_capacity": random.randint(50000, 200000),
                "personnel_capacity": random.randint(50, 200)
            },
            "time_window": {
                "max_duration": random.randint(12, 48)
            }
        }
    
    def _generate_mission_requirements(self) -> Dict[str, Any]:
        """生成任务需求数据"""
        return {
            "cargo_weight": random.randint(10000, 100000),
            "personnel_count": random.randint(20, 150),
            "origin": f"base_{random.randint(1, 10):03d}",
            "destination": f"base_{random.randint(11, 20):03d}",
            "time_window": {
                "start_time": "2025-08-01T06:00:00Z",
                "end_time": "2025-08-01T18:00:00Z"
            },
            "priority": random.choice(["low", "medium", "high", "urgent"]),
            "safety_requirements": {
                "safety_level": random.choice(["standard", "enhanced", "maximum"])
            }
        }
    
    def _generate_threat_factors(self) -> Dict[str, Any]:
        """生成威胁因素数据"""
        return {
            "enemy_threats": {
                "air_defense": random.choice(["low", "medium", "high"]),
                "electronic_warfare": random.choice(["low", "medium", "high"])
            },
            "threat_probabilities": {
                "air_defense_threat": random.uniform(0.1, 0.8),
                "weather_threat": random.uniform(0.2, 0.6)
            },
            "countermeasures": random.sample(
                ["electronic_warfare", "route_planning", "timing_optimization", "escort_protection"],
                k=random.randint(1, 3)
            )
        }

    async def generate_personnel_config_data(self, db_session) -> None:
        """
        生成人员配置测试数据

        Args:
            db_session: 数据库会话
        """
        print("生成人员配置数据...")

        personnel_templates = [
            {
                "personnel_type": PersonnelTypeEnum.EQUIPMENT_OPERATOR,
                "skill_level": SkillLevelEnum.SENIOR,
                "base_efficiency": 0.9
            },
            {
                "personnel_type": PersonnelTypeEnum.GROUND_CREW,
                "skill_level": SkillLevelEnum.INTERMEDIATE,
                "base_efficiency": 0.8
            },
            {
                "personnel_type": PersonnelTypeEnum.COMMAND_COORDINATOR,
                "skill_level": SkillLevelEnum.EXPERT,
                "base_efficiency": 0.95
            },
            {
                "personnel_type": PersonnelTypeEnum.SAFETY_SUPERVISOR,
                "skill_level": SkillLevelEnum.SENIOR,
                "base_efficiency": 0.85
            }
        ]

        for template in personnel_templates:
            personnel_config = PersonnelConfig(
                personnel_type=template["personnel_type"],
                quantity=random.randint(5, 20),
                skill_level=template["skill_level"],
                base_efficiency=template["base_efficiency"],
                skill_bonus=random.uniform(0.05, 0.15),
                experience_bonus=random.uniform(0.02, 0.08),
                work_hours_per_shift=8.0,
                rest_hours_between_shifts=8.0,
                shift_rotation_type="continuous",
                equipment_assignments=["装载车-ZC001", "叉车-FC002"],
                certification_requirements=["基础操作证", "安全培训证"],
                training_records={
                    "last_training_date": "2025-06-01",
                    "next_training_date": "2025-12-01",
                    "training_hours": random.randint(40, 120)
                },
                remarks=f"测试用{template['personnel_type']}"
            )

            db_session.add(personnel_config)
            await db_session.flush()
            self.generated_ids['personnel_configs'].append(personnel_config.id)

        await db_session.commit()
        print(f"✅ 生成了 {len(personnel_templates)} 个人员配置")

    async def generate_weather_condition_data(self, db_session) -> None:
        """
        生成气象条件测试数据

        Args:
            db_session: 数据库会话
        """
        print("生成气象条件数据...")

        weather_templates = [
            {
                "weather_type": WeatherTypeEnum.SUNNY,
                "severity_level": SeverityLevelEnum.LIGHT,
                "visibility": 15000,
                "wind_speed": 8
            },
            {
                "weather_type": WeatherTypeEnum.RAINY,
                "severity_level": SeverityLevelEnum.MODERATE,
                "visibility": 5000,
                "wind_speed": 15
            },
            {
                "weather_type": WeatherTypeEnum.FOGGY,
                "severity_level": SeverityLevelEnum.SEVERE,
                "visibility": 1000,
                "wind_speed": 5
            },
            {
                "weather_type": WeatherTypeEnum.WINDY,
                "severity_level": SeverityLevelEnum.MODERATE,
                "visibility": 10000,
                "wind_speed": 25
            }
        ]

        for template in weather_templates:
            weather_condition = WeatherCondition(
                weather_type=template["weather_type"],
                visibility=template["visibility"],
                wind_speed=template["wind_speed"],
                precipitation=random.uniform(0, 20) if template["weather_type"] == WeatherTypeEnum.RAINY else 0,
                temperature=random.uniform(-5, 30),
                humidity=random.uniform(40, 95),
                loading_efficiency_factor=random.uniform(0.7, 1.0),
                safety_factor=random.uniform(0.8, 1.0),
                equipment_performance_factor=random.uniform(0.75, 1.0),
                personnel_efficiency_factor=random.uniform(0.8, 1.0),
                duration_hours=random.uniform(2, 12),
                severity_level=template["severity_level"],
                additional_impact_factors={
                    "runway_condition": random.choice(["dry", "wet", "icy"]),
                    "lighting_condition": random.choice(["good", "poor", "night"])
                },
                remarks=f"测试用{template['weather_type']}天气"
            )

            db_session.add(weather_condition)
            await db_session.flush()
            self.generated_ids['weather_conditions'].append(weather_condition.id)

        await db_session.commit()
        print(f"✅ 生成了 {len(weather_templates)} 个气象条件")

    async def generate_configuration_scheme_data(self, db_session) -> None:
        """
        生成配置方案测试数据

        Args:
            db_session: 数据库会话
        """
        print("生成配置方案数据...")

        scheme_templates = [
            {
                "scheme_name": "重型装备运输标准方案",
                "scheme_type": SchemeTypeEnum.HEAVY_EQUIPMENT_TRANSPORT,
                "description": "适用于重型装备运输的标准配置方案"
            },
            {
                "scheme_name": "人员快速投送方案",
                "scheme_type": SchemeTypeEnum.PERSONNEL_TRANSPORT,
                "description": "适用于人员快速投送的优化配置方案"
            },
            {
                "scheme_name": "物资补给保障方案",
                "scheme_type": SchemeTypeEnum.MATERIAL_SUPPLY,
                "description": "适用于物资补给保障的综合配置方案"
            }
        ]

        for template in scheme_templates:
            configuration_scheme = ConfigurationScheme(
                scheme_name=template["scheme_name"],
                scheme_type=template["scheme_type"],
                description=template["description"],
                version="1.0.0",
                equipment_config_ids=random.sample(
                    self.generated_ids['equipment_configs'],
                    k=min(2, len(self.generated_ids['equipment_configs']))
                ),
                aircraft_config_ids=random.sample(
                    self.generated_ids['aircraft_configs'],
                    k=min(2, len(self.generated_ids['aircraft_configs']))
                ),
                personnel_config_ids=random.sample(
                    self.generated_ids['personnel_configs'],
                    k=min(3, len(self.generated_ids['personnel_configs']))
                ),
                weather_condition_ids=random.sample(
                    self.generated_ids['weather_conditions'],
                    k=min(2, len(self.generated_ids['weather_conditions']))
                ),
                scenario_parameters={
                    "max_operation_time": random.randint(8, 24),
                    "safety_priority": random.choice(["standard", "high", "maximum"])
                },
                operational_parameters={
                    "max_operation_duration": random.uniform(8, 16),
                    "safety_margin": random.uniform(0.1, 0.3),
                    "efficiency_target": random.uniform(0.8, 0.95),
                    "priority_level": random.choice(["low", "medium", "high"])
                },
                tags=random.sample(
                    ["标准", "优化", "快速", "安全", "经济", "高效"],
                    k=random.randint(2, 4)
                ),
                category=random.choice(["运输", "保障", "应急", "训练"]),
                created_by="test_generator",
                status=SchemeStatusEnum.VALIDATED,
                usage_count=random.randint(0, 50),
                rating=random.uniform(3.5, 5.0)
            )

            db_session.add(configuration_scheme)
            await db_session.flush()
            self.generated_ids['configuration_schemes'].append(configuration_scheme.id)

        await db_session.commit()
        print(f"✅ 生成了 {len(scheme_templates)} 个配置方案")

    def print_summary(self) -> None:
        """打印生成数据的摘要"""
        print("\n" + "="*50)
        print("测试数据生成摘要")
        print("="*50)

        for data_type, ids in self.generated_ids.items():
            print(f"{data_type}: {len(ids)} 条记录")

        total_records = sum(len(ids) for ids in self.generated_ids.values())
        print(f"\n总计: {total_records} 条记录")
        print("="*50)


async def main() -> None:
    """
    主函数
    """
    generator = TestDataGenerator()

    try:
        await generator.generate_all_test_data()
        generator.print_summary()
        print("\n🎉 测试数据生成成功！")

    except Exception as e:
        print(f"\n❌ 测试数据生成失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
