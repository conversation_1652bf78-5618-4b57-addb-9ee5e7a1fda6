#!/usr/bin/env python3
"""
数据库初始化脚本

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 使用描述性的函数命名
- 不使用硬编码数据，通过配置文件管理
"""

import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.database.connection import create_database_tables, drop_database_tables
from src.config.settings import settings


async def initialize_database() -> None:
    """
    初始化数据库
    
    创建所有数据表和索引
    """
    print("开始初始化数据库...")
    print(f"数据库连接: {settings.database.host}:{settings.database.port}/{settings.database.name}")
    
    try:
        await create_database_tables()
        print("✅ 数据库表创建成功")
    except Exception as e:
        print(f"❌ 数据库表创建失败: {e}")
        raise


async def reset_database() -> None:
    """
    重置数据库
    
    删除所有表后重新创建
    """
    print("开始重置数据库...")
    
    try:
        await drop_database_tables()
        print("✅ 数据库表删除成功")
        
        await create_database_tables()
        print("✅ 数据库表重新创建成功")
    except Exception as e:
        print(f"❌ 数据库重置失败: {e}")
        raise


async def main() -> None:
    """
    主函数
    
    根据命令行参数执行相应操作
    """
    if len(sys.argv) > 1 and sys.argv[1] == "--reset":
        await reset_database()
    else:
        await initialize_database()
    
    print("数据库操作完成")


if __name__ == "__main__":
    asyncio.run(main())
