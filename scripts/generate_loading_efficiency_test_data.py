"""
装卸载作业效能测试数据生成脚本

遵循base-rules.md规范：
- 不使用硬编码数据，通过脚本生成真实测试数据
- 函数复杂度控制，每个函数圈复杂度不超过10
- 使用描述性的函数命名
"""

import asyncio
import sys
from pathlib import Path
from decimal import Decimal
from datetime import datetime, timedelta
import random

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session
from src.database.models.loading_efficiency import (
    LoadingEfficiencyTask,
    LoadingEfficiencyResult,
    LoadingPhaseEnum,
    TaskStatusEnum,
    TaskPriorityEnum
)
from src.database.models.scenario import Scenario, TaskTypeEnum, ScenarioStatusEnum, ThreatLevelEnum
from src.database.models.efficiency import EfficiencyIndicator, IndicatorCategoryEnum, IndicatorStatusEnum


class LoadingEfficiencyTestDataGenerator:
    """
    装卸载作业效能测试数据生成器
    
    生成完整的装卸载作业效能测试数据，包括：
    - 装卸载作业场景数据
    - 效能指标定义数据
    - 装卸载作业任务数据
    - 计算结果数据
    """
    
    def __init__(self, db_session: AsyncSession) -> None:
        """
        初始化数据生成器
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def generate_all_test_data(self) -> None:
        """
        生成所有装卸载作业效能测试数据
        """
        print("开始生成装卸载作业效能测试数据...")
        
        # 生成装卸载作业场景
        scenarios = await self.generate_loading_scenarios()
        print(f"生成了 {len(scenarios)} 个装卸载作业场景")
        
        # 生成装卸载作业效能指标
        indicators = await self.generate_loading_efficiency_indicators()
        print(f"生成了 {len(indicators)} 个装卸载作业效能指标")
        
        # 生成装卸载作业任务
        tasks = await self.generate_loading_efficiency_tasks(scenarios)
        print(f"生成了 {len(tasks)} 个装卸载作业任务")
        
        # 生成计算结果
        results = await self.generate_calculation_results(tasks, indicators)
        print(f"生成了 {len(results)} 个计算结果")
        
        print("装卸载作业效能测试数据生成完成！")
    
    async def generate_loading_scenarios(self) -> list[Scenario]:
        """
        生成装卸载作业场景数据
        
        Returns:
            list[Scenario]: 生成的场景列表
        """
        scenarios_data = [
            {
                "scenario_name": "大型装备装载作业",
                "scenario_description": "重型装备从仓库到飞机的完整装载作业流程",
                "task_type": TaskTypeEnum.EQUIPMENT_TRANSPORT,
                "threat_level": ThreatLevelEnum.LOW,
                "scenario_parameters": {
                    "cargo_type": "heavy_equipment",
                    "cargo_weight": 15000,  # 15吨
                    "loading_phases": ["warehouse_loading", "ground_transport", "aircraft_loading"],
                    "equipment_count": 8,
                    "personnel_count": 12
                }
            },
            {
                "scenario_name": "人员快速投送装载",
                "scenario_description": "紧急人员投送的快速装载作业",
                "task_type": TaskTypeEnum.PERSONNEL_TRANSPORT,
                "threat_level": ThreatLevelEnum.MEDIUM,
                "scenario_parameters": {
                    "cargo_type": "personnel_equipment",
                    "cargo_weight": 5000,  # 5吨
                    "loading_phases": ["warehouse_loading", "ground_transport", "aircraft_loading"],
                    "equipment_count": 6,
                    "personnel_count": 15,
                    "urgency_level": "high"
                }
            },
            {
                "scenario_name": "医疗物资装载作业",
                "scenario_description": "医疗救援物资的精确装载作业",
                "task_type": TaskTypeEnum.MATERIAL_SUPPLY,
                "threat_level": ThreatLevelEnum.LOW,
                "scenario_parameters": {
                    "cargo_type": "medical_supplies",
                    "cargo_weight": 3000,  # 3吨
                    "loading_phases": ["warehouse_loading", "ground_transport", "aircraft_loading"],
                    "equipment_count": 4,
                    "personnel_count": 8,
                    "special_requirements": ["temperature_control", "fragile_handling"]
                }
            },
            {
                "scenario_name": "混合货物装载作业",
                "scenario_description": "多种类型货物的综合装载作业",
                "task_type": TaskTypeEnum.MATERIAL_SUPPLY,
                "threat_level": ThreatLevelEnum.MEDIUM,
                "scenario_parameters": {
                    "cargo_type": "mixed_cargo",
                    "cargo_weight": 8000,  # 8吨
                    "loading_phases": ["warehouse_loading", "ground_transport", "aircraft_loading"],
                    "equipment_count": 10,
                    "personnel_count": 18,
                    "cargo_categories": ["equipment", "supplies", "materials"]
                }
            },
            {
                "scenario_name": "特种装备装载作业",
                "scenario_description": "特种装备的专业化装载作业",
                "task_type": TaskTypeEnum.SPECIAL_TRANSPORT,
                "threat_level": ThreatLevelEnum.HIGH,
                "scenario_parameters": {
                    "cargo_type": "special_equipment",
                    "cargo_weight": 12000,  # 12吨
                    "loading_phases": ["warehouse_loading", "ground_transport", "aircraft_loading"],
                    "equipment_count": 12,
                    "personnel_count": 20,
                    "security_level": "classified"
                }
            }
        ]
        
        scenarios = []
        for data in scenarios_data:
            scenario = Scenario(
                **data,
                status=ScenarioStatusEnum.ACTIVE,
                created_by="system",
                metadata_info={"source": "loading_efficiency_test_data", "version": "1.0"}
            )
            self.db_session.add(scenario)
            scenarios.append(scenario)
        
        await self.db_session.commit()
        return scenarios
    
    async def generate_loading_efficiency_indicators(self) -> list[EfficiencyIndicator]:
        """
        生成装卸载作业效能指标定义数据
        
        Returns:
            list[EfficiencyIndicator]: 生成的指标列表
        """
        indicators_data = [
            # 时效性指标
            {
                "indicator_name": "作业完成时间",
                "indicator_code": "OPERATION_COMPLETION_TIME",
                "category": IndicatorCategoryEnum.TIMELINESS,
                "description": "从开始装载到完成装载的总耗时",
                "calculation_formula": "结束时间 - 开始时间",
                "unit": "小时",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('12'),
                "target_value": Decimal('4'),
                "default_weight": Decimal('0.3000'),
                "priority_level": 1
            },
            {
                "indicator_name": "平均响应时间",
                "indicator_code": "AVERAGE_RESPONSE_TIME",
                "category": IndicatorCategoryEnum.TIMELINESS,
                "description": "从指令下达到开始执行的平均时间",
                "calculation_formula": "sum(响应时间列表) / len(响应时间列表)",
                "unit": "分钟",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('30'),
                "target_value": Decimal('5'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 2
            },
            {
                "indicator_name": "准时完成率",
                "indicator_code": "ON_TIME_COMPLETION_RATE",
                "category": IndicatorCategoryEnum.TIMELINESS,
                "description": "在规定时间内完成作业的比例",
                "calculation_formula": "(准时完成作业数 / 总作业数) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('95'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 1
            },
            {
                "indicator_name": "单位时间处理量",
                "indicator_code": "PROCESSING_VOLUME_PER_HOUR",
                "category": IndicatorCategoryEnum.TIMELINESS,
                "description": "每小时完成的货物处理量",
                "calculation_formula": "总处理量 / 持续时间",
                "unit": "吨/小时",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('50'),
                "target_value": Decimal('10'),
                "default_weight": Decimal('0.2000'),
                "priority_level": 3
            },
            
            # 效率指标
            {
                "indicator_name": "设备利用率",
                "indicator_code": "EQUIPMENT_UTILIZATION_RATE",
                "category": IndicatorCategoryEnum.EFFICIENCY,
                "description": "设备实际使用时间占可用时间的比例",
                "calculation_formula": "(实际使用时间 / 可用时间) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('85'),
                "default_weight": Decimal('0.3000'),
                "priority_level": 1
            },
            {
                "indicator_name": "人员利用率",
                "indicator_code": "PERSONNEL_UTILIZATION_RATE",
                "category": IndicatorCategoryEnum.EFFICIENCY,
                "description": "人员实际工作时间占计划时间的比例",
                "calculation_formula": "(实际工作时间 / 计划工作时间) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('80'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 2
            },
            {
                "indicator_name": "作业成功率",
                "indicator_code": "OPERATION_SUCCESS_RATE",
                "category": IndicatorCategoryEnum.EFFICIENCY,
                "description": "成功完成作业的比例",
                "calculation_formula": "(成功作业数 / 总作业数) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('98'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 1
            },
            {
                "indicator_name": "设备作业效率",
                "indicator_code": "EQUIPMENT_OPERATION_EFFICIENCY",
                "category": IndicatorCategoryEnum.EFFICIENCY,
                "description": "设备单位时间的作业效率",
                "calculation_formula": "处理量 / 作业时间",
                "unit": "吨/小时",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('20'),
                "target_value": Decimal('5'),
                "default_weight": Decimal('0.2000'),
                "priority_level": 3
            },
            
            # 质量指标
            {
                "indicator_name": "货物完好率",
                "indicator_code": "CARGO_INTEGRITY_RATE",
                "category": IndicatorCategoryEnum.QUALITY,
                "description": "货物在装载过程中保持完好的比例",
                "calculation_formula": "(完好货物数 / 总货物数) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('99'),
                "default_weight": Decimal('0.3000'),
                "priority_level": 1
            },
            {
                "indicator_name": "作业精度",
                "indicator_code": "OPERATION_ACCURACY",
                "category": IndicatorCategoryEnum.QUALITY,
                "description": "货物放置位置的准确度",
                "calculation_formula": "(准确位置数 / 目标位置数) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('95'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 2
            },
            {
                "indicator_name": "安全事故率",
                "indicator_code": "SAFETY_INCIDENT_RATE",
                "category": IndicatorCategoryEnum.QUALITY,
                "description": "作业过程中发生安全事故的比例",
                "calculation_formula": "(安全事故数 / 总作业数) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('5'),
                "target_value": Decimal('0.1'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 1
            },
            {
                "indicator_name": "返工率",
                "indicator_code": "REWORK_RATE",
                "category": IndicatorCategoryEnum.QUALITY,
                "description": "需要重新作业的比例",
                "calculation_formula": "(返工作业数 / 总作业数) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('10'),
                "target_value": Decimal('2'),
                "default_weight": Decimal('0.2000'),
                "priority_level": 3
            }
        ]
        
        indicators = []
        for data in indicators_data:
            indicator = EfficiencyIndicator(
                **data,
                status=IndicatorStatusEnum.ACTIVE,
                created_by="system",
                metadata_info={"source": "loading_efficiency_test_data", "version": "1.0"}
            )
            self.db_session.add(indicator)
            indicators.append(indicator)
        
        await self.db_session.commit()
        return indicators
    
    async def generate_loading_efficiency_tasks(self, scenarios: list[Scenario]) -> list[LoadingEfficiencyTask]:
        """
        生成装卸载作业任务数据
        
        Args:
            scenarios: 场景列表
            
        Returns:
            list[LoadingEfficiencyTask]: 生成的任务列表
        """
        tasks = []
        
        for scenario in scenarios:
            # 为每个场景生成2-3个任务
            task_count = random.randint(2, 3)
            
            for i in range(task_count):
                task_name = f"{scenario.scenario_name} - 任务{i+1}"
                
                # 生成装载阶段配置
                loading_phases = [
                    {
                        "phase_type": LoadingPhaseEnum.WAREHOUSE_LOADING.value,
                        "phase_name": "仓库装载",
                        "sequence_order": 1,
                        "expected_duration": random.uniform(0.5, 2.0)
                    },
                    {
                        "phase_type": LoadingPhaseEnum.GROUND_TRANSPORT.value,
                        "phase_name": "地面运输",
                        "sequence_order": 2,
                        "expected_duration": random.uniform(0.3, 1.0)
                    },
                    {
                        "phase_type": LoadingPhaseEnum.AIRCRAFT_LOADING.value,
                        "phase_name": "飞机装载",
                        "sequence_order": 3,
                        "expected_duration": random.uniform(1.0, 3.0)
                    }
                ]
                
                # 生成输入数据
                input_data = self._generate_task_input_data(scenario)
                
                task = LoadingEfficiencyTask(
                    task_name=task_name,
                    task_description=f"基于{scenario.scenario_name}的装卸载作业效能计算任务",
                    scenario_id=scenario.id,
                    loading_phases=loading_phases,
                    input_data=input_data,
                    calculation_parameters={"precision": "high", "include_phases": True},
                    status=TaskStatusEnum.COMPLETED,  # 设置为已完成以便生成结果
                    priority=random.choice(list(TaskPriorityEnum)),
                    progress_percentage=100,
                    started_at=(datetime.now() - timedelta(hours=random.randint(1, 24))).isoformat(),
                    completed_at=datetime.now().isoformat(),
                    execution_duration=random.randint(300, 1800),  # 5-30分钟
                    created_by="system"
                )
                
                self.db_session.add(task)
                tasks.append(task)
        
        await self.db_session.commit()
        return tasks
    
    def _generate_task_input_data(self, scenario: Scenario) -> dict:
        """
        生成任务输入数据
        
        Args:
            scenario: 场景信息
            
        Returns:
            dict: 输入数据
        """
        params = scenario.scenario_parameters
        
        return {
            "start_time": (datetime.now() - timedelta(hours=random.randint(1, 48))).isoformat(),
            "end_time": (datetime.now() - timedelta(minutes=random.randint(30, 300))).isoformat(),
            "total_operations": random.randint(10, 50),
            "successful_operations": random.randint(8, 48),
            "on_time_operations": random.randint(8, 45),
            "total_volume": params.get("cargo_weight", random.randint(1000, 20000)),
            "duration_hours": random.uniform(2.0, 8.0),
            "response_times": [random.uniform(1.0, 10.0) for _ in range(random.randint(5, 15))],
            "equipment_usage_hours": random.uniform(50, 200),
            "equipment_available_hours": random.uniform(200, 300),
            "personnel_work_hours": random.uniform(80, 160),
            "personnel_scheduled_hours": random.uniform(160, 200),
            "processed_volume": random.uniform(1000, 15000),
            "operation_time": random.uniform(4.0, 12.0),
            "total_cargo": random.randint(50, 500),
            "intact_cargo": random.randint(48, 498),
            "target_positions": random.randint(20, 100),
            "accurate_positions": random.randint(18, 98),
            "safety_incidents": random.randint(0, 2),
            "rework_operations": random.randint(0, 5),
            "equipment_data": self._generate_equipment_data(params.get("equipment_count", 5)),
            "personnel_data": self._generate_personnel_data(params.get("personnel_count", 10))
        }
    
    def _generate_equipment_data(self, equipment_count: int) -> dict:
        """生成设备数据"""
        equipment_data = {}
        for i in range(equipment_count):
            equipment_id = f"equipment_{i+1:03d}"
            equipment_data[equipment_id] = {
                "usage_time": random.uniform(2.0, 8.0),
                "processed_volume": random.uniform(500, 3000),
                "efficiency_score": random.uniform(70, 95)
            }
        return equipment_data
    
    def _generate_personnel_data(self, personnel_count: int) -> dict:
        """生成人员数据"""
        personnel_data = {}
        for i in range(personnel_count):
            personnel_id = f"personnel_{i+1:03d}"
            personnel_data[personnel_id] = {
                "work_time": random.uniform(4.0, 10.0),
                "completed_tasks": random.randint(3, 15),
                "quality_score": random.uniform(75, 98)
            }
        return personnel_data
    
    async def generate_calculation_results(self, tasks: list[LoadingEfficiencyTask], 
                                         indicators: list[EfficiencyIndicator]) -> list[LoadingEfficiencyResult]:
        """
        生成计算结果数据
        
        Args:
            tasks: 任务列表
            indicators: 指标列表
            
        Returns:
            list[LoadingEfficiencyResult]: 生成的计算结果列表
        """
        results = []
        
        for task in tasks:
            for indicator in indicators:
                # 根据指标类型生成合理的测试值
                calculated_value = self._generate_indicator_value(indicator)
                
                result = LoadingEfficiencyResult(
                    task_id=task.id,
                    indicator_code=indicator.indicator_code,
                    indicator_name=indicator.indicator_name,
                    calculated_value=calculated_value,
                    confidence_level=Decimal(str(random.uniform(0.85, 0.99))),
                    phase_results={
                        "warehouse_loading": float(calculated_value) * random.uniform(0.8, 1.2),
                        "ground_transport": float(calculated_value) * random.uniform(0.9, 1.1),
                        "aircraft_loading": float(calculated_value) * random.uniform(0.85, 1.15)
                    },
                    equipment_contributions={"equipment_001": random.uniform(0.2, 0.4)},
                    personnel_contributions={"personnel_001": random.uniform(0.15, 0.35)},
                    calculation_metadata={
                        "algorithm": "loading_efficiency_calculator",
                        "version": "1.0",
                        "calculation_time_ms": random.randint(50, 500)
                    }
                )
                
                self.db_session.add(result)
                results.append(result)
        
        await self.db_session.commit()
        return results
    
    def _generate_indicator_value(self, indicator: EfficiencyIndicator) -> Decimal:
        """
        根据指标类型生成合理的测试值
        
        Args:
            indicator: 效能指标
            
        Returns:
            Decimal: 生成的指标值
        """
        # 根据指标的目标值和阈值生成合理的随机值
        if indicator.target_value:
            base_value = float(indicator.target_value)
            variation = base_value * 0.15  # 15%的波动范围
            value = random.uniform(base_value - variation, base_value + variation)
        elif indicator.max_threshold:
            max_val = float(indicator.max_threshold)
            min_val = float(indicator.min_threshold) if indicator.min_threshold else 0
            value = random.uniform(min_val, max_val * 0.85)
        else:
            value = random.uniform(0, 100)
        
        # 确保值在合理范围内
        if indicator.min_threshold and value < float(indicator.min_threshold):
            value = float(indicator.min_threshold)
        if indicator.max_threshold and value > float(indicator.max_threshold):
            value = float(indicator.max_threshold)
        
        return Decimal(str(value)).quantize(Decimal('0.01'))


async def main():
    """
    主函数：执行测试数据生成
    """
    print("装卸载作业效能测试数据生成脚本启动...")
    
    # 获取数据库会话
    async for db_session in get_database_session():
        try:
            generator = LoadingEfficiencyTestDataGenerator(db_session)
            await generator.generate_all_test_data()
            print("测试数据生成成功完成！")
        except Exception as e:
            print(f"测试数据生成失败: {e}")
            await db_session.rollback()
            raise
        finally:
            await db_session.close()
        break


if __name__ == "__main__":
    asyncio.run(main())
