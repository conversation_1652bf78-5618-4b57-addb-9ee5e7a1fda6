#!/usr/bin/env python3
"""
功能验证测试脚本

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 使用描述性的函数命名
- 测试独立性规则：每个测试用例必须独立运行
"""

import asyncio
import sys
from pathlib import Path
from typing import Dict, Any, List
import httpx

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config.settings import settings


class FunctionalityValidator:
    """
    功能验证器
    
    通过API调用验证各模块功能的正确性
    """
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        """
        初始化功能验证器
        
        Args:
            base_url: API基础URL
        """
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"
        self.test_results = {
            'passed': 0,
            'failed': 0,
            'errors': []
        }
    
    async def validate_all_functionality(self) -> None:
        """
        验证所有功能模块
        """
        print("开始功能验证测试...")
        print(f"API基础URL: {self.api_base}")
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            # 验证基础健康检查
            await self.validate_health_check(client)
            
            # 验证场景管理功能
            await self.validate_scenario_management(client)
            
            # 验证设备配置管理功能
            await self.validate_equipment_management(client)
            
            # 验证飞机配置管理功能
            await self.validate_aircraft_management(client)
            
            # 验证配置方案管理功能
            await self.validate_configuration_management(client)
        
        # 打印测试结果
        self.print_test_results()
    
    async def validate_health_check(self, client: httpx.AsyncClient) -> None:
        """
        验证健康检查功能
        
        Args:
            client: HTTP客户端
        """
        print("\n🔍 验证健康检查功能...")
        
        try:
            # 测试根路径
            response = await client.get(f"{self.base_url}/")
            self.assert_response_ok(response, "根路径健康检查")
            
            # 测试健康检查端点
            response = await client.get(f"{self.base_url}/health")
            self.assert_response_ok(response, "健康检查端点")
            
            data = response.json()
            assert data["status"] == "healthy", "健康状态检查失败"
            
            self.test_passed("健康检查功能")
            
        except Exception as e:
            self.test_failed("健康检查功能", str(e))
    
    async def validate_scenario_management(self, client: httpx.AsyncClient) -> None:
        """
        验证场景管理功能
        
        Args:
            client: HTTP客户端
        """
        print("\n🔍 验证场景管理功能...")
        
        try:
            # 测试创建场景
            scenario_data = {
                "scenario_name": "验证测试场景",
                "scenario_type": "test_scenario",
                "description": "用于功能验证的测试场景",
                "task_type": "equipment_transport",
                "environment_conditions": {
                    "weather": {"visibility": 10000, "wind_speed": 10},
                    "terrain": "plain"
                },
                "resource_constraints": {
                    "transport_capacity": {"max_aircraft": 5}
                },
                "mission_requirements": {
                    "cargo_weight": 50000,
                    "origin": "test_base_001",
                    "destination": "test_base_002"
                },
                "threat_factors": {
                    "enemy_threats": {"air_defense": "low"}
                },
                "threat_level": "low",
                "created_by": "validator"
            }
            
            response = await client.post(
                f"{self.api_base}/scenarios/",
                json=scenario_data
            )
            self.assert_response_created(response, "创建场景")
            
            created_scenario = response.json()
            scenario_id = created_scenario["id"]
            
            # 测试获取场景详情
            response = await client.get(f"{self.api_base}/scenarios/{scenario_id}")
            self.assert_response_ok(response, "获取场景详情")
            
            # 测试获取场景列表
            response = await client.get(f"{self.api_base}/scenarios/")
            self.assert_response_ok(response, "获取场景列表")
            
            data = response.json()
            assert len(data["scenarios"]) > 0, "场景列表为空"
            
            # 测试更新场景
            update_data = {
                "scenario_name": "更新后的验证测试场景",
                "description": "已更新的测试场景描述"
            }
            
            response = await client.put(
                f"{self.api_base}/scenarios/{scenario_id}",
                json=update_data
            )
            self.assert_response_ok(response, "更新场景")
            
            # 测试删除场景
            response = await client.delete(f"{self.api_base}/scenarios/{scenario_id}")
            assert response.status_code == 204, f"删除场景失败: {response.status_code}"
            
            self.test_passed("场景管理功能")
            
        except Exception as e:
            self.test_failed("场景管理功能", str(e))
    
    async def validate_equipment_management(self, client: httpx.AsyncClient) -> None:
        """
        验证设备配置管理功能
        
        Args:
            client: HTTP客户端
        """
        print("\n🔍 验证设备配置管理功能...")
        
        try:
            # 测试创建设备配置
            equipment_data = {
                "equipment_type": "loading_vehicle",
                "equipment_model": "验证测试装载车",
                "quantity": 2,
                "max_load_capacity": 15000,
                "loading_speed": 4000,
                "unloading_speed": 5000,
                "operation_radius": 100,
                "unit_operation_duration": 1.5,
                "efficiency_factor": 0.9,
                "maintenance_interval": 200,
                "operational_status": "available",
                "maintenance_info": {"test": "data"},
                "remarks": "验证测试用设备"
            }
            
            response = await client.post(
                f"{self.api_base}/equipment-configs/",
                json=equipment_data
            )
            self.assert_response_created(response, "创建设备配置")
            
            created_equipment = response.json()
            equipment_id = created_equipment["id"]
            
            # 测试获取设备配置详情
            response = await client.get(f"{self.api_base}/equipment-configs/{equipment_id}")
            self.assert_response_ok(response, "获取设备配置详情")
            
            # 测试获取设备配置列表
            response = await client.get(f"{self.api_base}/equipment-configs/")
            self.assert_response_ok(response, "获取设备配置列表")
            
            # 测试更新设备配置
            update_data = {
                "equipment_model": "更新后的验证测试装载车",
                "quantity": 3
            }
            
            response = await client.put(
                f"{self.api_base}/equipment-configs/{equipment_id}",
                json=update_data
            )
            self.assert_response_ok(response, "更新设备配置")
            
            # 测试删除设备配置
            response = await client.delete(f"{self.api_base}/equipment-configs/{equipment_id}")
            assert response.status_code == 204, f"删除设备配置失败: {response.status_code}"
            
            self.test_passed("设备配置管理功能")
            
        except Exception as e:
            self.test_failed("设备配置管理功能", str(e))
    
    async def validate_aircraft_management(self, client: httpx.AsyncClient) -> None:
        """
        验证飞机配置管理功能
        
        Args:
            client: HTTP客户端
        """
        print("\n🔍 验证飞机配置管理功能...")
        
        try:
            # 测试创建飞机配置
            aircraft_data = {
                "aircraft_model": "Y-8",
                "quantity": 1,
                "payload_capacity": 18000,
                "operational_range": 5000,
                "cruise_speed": 480,
                "fuel_consumption": 1600,
                "loading_time": 45,
                "unloading_time": 35,
                "ground_taxi_time": 15,
                "crew_requirements": {
                    "pilots": 2,
                    "flight_engineers": 0,
                    "loadmasters": 1
                },
                "compatible_equipment": ["验证测试装载车"],
                "maintenance_schedule": {
                    "routine_maintenance_hours": 100,
                    "major_maintenance_hours": 1000,
                    "last_maintenance_date": "2025-07-01"
                },
                "status": "available",
                "remarks": "验证测试用飞机"
            }
            
            response = await client.post(
                f"{self.api_base}/aircraft-configs/",
                json=aircraft_data
            )
            self.assert_response_created(response, "创建飞机配置")
            
            created_aircraft = response.json()
            aircraft_id = created_aircraft["id"]
            
            # 测试获取飞机配置详情
            response = await client.get(f"{self.api_base}/aircraft-configs/{aircraft_id}")
            self.assert_response_ok(response, "获取飞机配置详情")
            
            # 测试获取飞机配置列表
            response = await client.get(f"{self.api_base}/aircraft-configs/")
            self.assert_response_ok(response, "获取飞机配置列表")
            
            # 测试删除飞机配置
            response = await client.delete(f"{self.api_base}/aircraft-configs/{aircraft_id}")
            assert response.status_code == 204, f"删除飞机配置失败: {response.status_code}"
            
            self.test_passed("飞机配置管理功能")
            
        except Exception as e:
            self.test_failed("飞机配置管理功能", str(e))
    
    async def validate_configuration_management(self, client: httpx.AsyncClient) -> None:
        """
        验证配置方案管理功能
        
        Args:
            client: HTTP客户端
        """
        print("\n🔍 验证配置方案管理功能...")
        
        try:
            # 测试创建配置方案
            scheme_data = {
                "scheme_name": "验证测试配置方案",
                "scheme_type": "mixed_transport",
                "description": "用于功能验证的测试配置方案",
                "equipment_config_ids": [],
                "aircraft_config_ids": [],
                "personnel_config_ids": [],
                "weather_condition_ids": [],
                "scenario_parameters": {"test": "data"},
                "operational_parameters": {
                    "max_operation_duration": 12,
                    "safety_margin": 0.2,
                    "efficiency_target": 0.85,
                    "priority_level": "medium"
                },
                "tags": ["验证", "测试"],
                "category": "测试",
                "created_by": "validator"
            }
            
            response = await client.post(
                f"{self.api_base}/configuration-schemes/",
                json=scheme_data
            )
            self.assert_response_created(response, "创建配置方案")
            
            created_scheme = response.json()
            scheme_id = created_scheme["id"]
            
            # 测试获取配置方案详情
            response = await client.get(f"{self.api_base}/configuration-schemes/{scheme_id}")
            self.assert_response_ok(response, "获取配置方案详情")
            
            # 测试获取配置方案列表
            response = await client.get(f"{self.api_base}/configuration-schemes/")
            self.assert_response_ok(response, "获取配置方案列表")
            
            # 测试验证配置方案
            response = await client.post(f"{self.api_base}/configuration-schemes/{scheme_id}/validate")
            self.assert_response_ok(response, "验证配置方案")
            
            # 测试获取方案版本列表
            response = await client.get(f"{self.api_base}/configuration-schemes/{scheme_id}/versions")
            self.assert_response_ok(response, "获取方案版本列表")
            
            # 测试删除配置方案
            response = await client.delete(f"{self.api_base}/configuration-schemes/{scheme_id}")
            assert response.status_code == 204, f"删除配置方案失败: {response.status_code}"
            
            self.test_passed("配置方案管理功能")
            
        except Exception as e:
            self.test_failed("配置方案管理功能", str(e))
    
    def assert_response_ok(self, response: httpx.Response, operation: str) -> None:
        """
        断言响应状态为200
        
        Args:
            response: HTTP响应
            operation: 操作名称
        """
        assert response.status_code == 200, f"{operation}失败: {response.status_code} - {response.text}"
    
    def assert_response_created(self, response: httpx.Response, operation: str) -> None:
        """
        断言响应状态为201
        
        Args:
            response: HTTP响应
            operation: 操作名称
        """
        assert response.status_code == 201, f"{operation}失败: {response.status_code} - {response.text}"
    
    def test_passed(self, test_name: str) -> None:
        """
        记录测试通过
        
        Args:
            test_name: 测试名称
        """
        self.test_results['passed'] += 1
        print(f"✅ {test_name} - 通过")
    
    def test_failed(self, test_name: str, error: str) -> None:
        """
        记录测试失败
        
        Args:
            test_name: 测试名称
            error: 错误信息
        """
        self.test_results['failed'] += 1
        self.test_results['errors'].append(f"{test_name}: {error}")
        print(f"❌ {test_name} - 失败: {error}")
    
    def print_test_results(self) -> None:
        """打印测试结果摘要"""
        print("\n" + "="*60)
        print("功能验证测试结果")
        print("="*60)
        
        total_tests = self.test_results['passed'] + self.test_results['failed']
        print(f"总测试数: {total_tests}")
        print(f"通过: {self.test_results['passed']}")
        print(f"失败: {self.test_results['failed']}")
        
        if self.test_results['failed'] > 0:
            print("\n失败详情:")
            for error in self.test_results['errors']:
                print(f"  - {error}")
        
        success_rate = (self.test_results['passed'] / total_tests * 100) if total_tests > 0 else 0
        print(f"\n成功率: {success_rate:.1f}%")
        print("="*60)


async def main() -> None:
    """
    主函数
    """
    validator = FunctionalityValidator()
    
    try:
        await validator.validate_all_functionality()
        
        if validator.test_results['failed'] == 0:
            print("\n🎉 所有功能验证测试通过！")
        else:
            print(f"\n⚠️  有 {validator.test_results['failed']} 个测试失败")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 功能验证测试执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
