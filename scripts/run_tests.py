#!/usr/bin/env python3
"""
测试运行脚本

遵循base-rules.md规范：
- 函数复杂度控制，每个函数圈复杂度不超过10
- 使用描述性的函数命名
- 统一的测试执行流程
"""

import asyncio
import subprocess
import sys
import time
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class TestRunner:
    """
    测试运行器
    
    统一管理和执行各种测试
    """
    
    def __init__(self):
        """初始化测试运行器"""
        self.project_root = project_root
        self.scripts_dir = self.project_root / "scripts"
        self.test_results = {
            'database_init': False,
            'test_data_generation': False,
            'unit_tests': False,
            'functionality_validation': False
        }
    
    async def run_all_tests(self) -> None:
        """
        运行所有测试
        """
        print("🚀 开始执行完整测试流程...")
        print(f"项目根目录: {self.project_root}")
        
        try:
            # 1. 初始化数据库
            await self.initialize_database()
            
            # 2. 生成测试数据
            await self.generate_test_data()
            
            # 3. 运行单元测试
            await self.run_unit_tests()
            
            # 4. 运行功能验证测试
            await self.run_functionality_validation()
            
            # 打印测试结果摘要
            self.print_test_summary()
            
        except Exception as e:
            print(f"❌ 测试执行过程中发生错误: {e}")
            sys.exit(1)
    
    async def initialize_database(self) -> None:
        """
        初始化数据库
        """
        print("\n" + "="*50)
        print("1. 初始化数据库")
        print("="*50)
        
        try:
            # 运行数据库初始化脚本
            init_script = self.scripts_dir / "init_db.py"
            
            result = subprocess.run(
                [sys.executable, str(init_script), "--reset"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=60
            )
            
            if result.returncode == 0:
                print("✅ 数据库初始化成功")
                self.test_results['database_init'] = True
            else:
                print(f"❌ 数据库初始化失败:")
                print(f"stdout: {result.stdout}")
                print(f"stderr: {result.stderr}")
                raise Exception("数据库初始化失败")
                
        except subprocess.TimeoutExpired:
            print("❌ 数据库初始化超时")
            raise Exception("数据库初始化超时")
        except Exception as e:
            print(f"❌ 数据库初始化异常: {e}")
            raise
    
    async def generate_test_data(self) -> None:
        """
        生成测试数据
        """
        print("\n" + "="*50)
        print("2. 生成测试数据")
        print("="*50)
        
        try:
            # 运行测试数据生成脚本
            generate_script = self.scripts_dir / "generate_test_data.py"
            
            result = subprocess.run(
                [sys.executable, str(generate_script)],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=120
            )
            
            if result.returncode == 0:
                print("✅ 测试数据生成成功")
                print(result.stdout)
                self.test_results['test_data_generation'] = True
            else:
                print(f"❌ 测试数据生成失败:")
                print(f"stdout: {result.stdout}")
                print(f"stderr: {result.stderr}")
                raise Exception("测试数据生成失败")
                
        except subprocess.TimeoutExpired:
            print("❌ 测试数据生成超时")
            raise Exception("测试数据生成超时")
        except Exception as e:
            print(f"❌ 测试数据生成异常: {e}")
            raise
    
    async def run_unit_tests(self) -> None:
        """
        运行单元测试
        """
        print("\n" + "="*50)
        print("3. 运行单元测试")
        print("="*50)
        
        try:
            # 运行pytest单元测试
            result = subprocess.run(
                [sys.executable, "-m", "pytest", "tests/", "-v", "--tb=short"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300
            )
            
            print(result.stdout)
            if result.stderr:
                print("stderr:", result.stderr)
            
            if result.returncode == 0:
                print("✅ 单元测试全部通过")
                self.test_results['unit_tests'] = True
            else:
                print(f"❌ 单元测试失败 (返回码: {result.returncode})")
                # 单元测试失败不阻止后续测试
                
        except subprocess.TimeoutExpired:
            print("❌ 单元测试超时")
        except Exception as e:
            print(f"❌ 单元测试异常: {e}")
    
    async def run_functionality_validation(self) -> None:
        """
        运行功能验证测试
        """
        print("\n" + "="*50)
        print("4. 运行功能验证测试")
        print("="*50)
        
        # 首先启动应用服务器
        server_process = None
        
        try:
            print("启动应用服务器...")
            server_process = subprocess.Popen(
                [sys.executable, "-m", "uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8000"],
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            # 等待服务器启动
            await asyncio.sleep(5)
            
            # 检查服务器是否正常启动
            if server_process.poll() is not None:
                stdout, stderr = server_process.communicate()
                print(f"❌ 服务器启动失败:")
                print(f"stdout: {stdout.decode()}")
                print(f"stderr: {stderr.decode()}")
                raise Exception("服务器启动失败")
            
            print("✅ 应用服务器启动成功")
            
            # 运行功能验证脚本
            validate_script = self.scripts_dir / "validate_functionality.py"
            
            result = subprocess.run(
                [sys.executable, str(validate_script)],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=180
            )
            
            print(result.stdout)
            if result.stderr:
                print("stderr:", result.stderr)
            
            if result.returncode == 0:
                print("✅ 功能验证测试全部通过")
                self.test_results['functionality_validation'] = True
            else:
                print(f"❌ 功能验证测试失败 (返回码: {result.returncode})")
                
        except subprocess.TimeoutExpired:
            print("❌ 功能验证测试超时")
        except Exception as e:
            print(f"❌ 功能验证测试异常: {e}")
        finally:
            # 关闭服务器进程
            if server_process and server_process.poll() is None:
                print("关闭应用服务器...")
                server_process.terminate()
                try:
                    server_process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    server_process.kill()
                    server_process.wait()
                print("✅ 应用服务器已关闭")
    
    def print_test_summary(self) -> None:
        """
        打印测试结果摘要
        """
        print("\n" + "="*60)
        print("测试执行结果摘要")
        print("="*60)
        
        test_names = {
            'database_init': '数据库初始化',
            'test_data_generation': '测试数据生成',
            'unit_tests': '单元测试',
            'functionality_validation': '功能验证测试'
        }
        
        passed_count = 0
        total_count = len(self.test_results)
        
        for test_key, result in self.test_results.items():
            test_name = test_names.get(test_key, test_key)
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed_count += 1
        
        print(f"\n总计: {passed_count}/{total_count} 项测试通过")
        
        if passed_count == total_count:
            print("\n🎉 所有测试执行完成，系统功能正常！")
        else:
            print(f"\n⚠️  有 {total_count - passed_count} 项测试未通过，请检查相关功能")
        
        print("="*60)


async def main() -> None:
    """
    主函数
    """
    print("航空运输保障效能算法库 - 测试执行器")
    print("="*60)
    
    runner = TestRunner()
    
    try:
        await runner.run_all_tests()
        
        # 根据测试结果确定退出码
        if all(runner.test_results.values()):
            sys.exit(0)  # 所有测试通过
        else:
            sys.exit(1)  # 有测试失败
            
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(130)
    except Exception as e:
        print(f"\n❌ 测试执行器异常: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
