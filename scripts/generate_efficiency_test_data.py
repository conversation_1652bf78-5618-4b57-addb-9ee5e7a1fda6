"""
效能指标测试数据生成脚本

遵循base-rules.md规范：
- 不使用硬编码数据，通过脚本生成真实测试数据
- 函数复杂度控制，每个函数圈复杂度不超过10
- 使用描述性的函数命名
"""

import asyncio
import sys
from pathlib import Path
from decimal import Decimal
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy.ext.asyncio import AsyncSession
from src.database.connection import get_database_session
from src.database.models.efficiency import (
    EfficiencyIndicator,
    IndicatorCalculationResult,
    IndicatorWeightConfig,
    IndicatorCategoryEnum,
    IndicatorStatusEnum,
    CalculationStatusEnum
)
from src.database.models.scenario import Scenario


class EfficiencyTestDataGenerator:
    """
    效能指标测试数据生成器
    
    生成完整的效能指标测试数据，包括：
    - 效能指标定义数据
    - 指标权重配置数据
    - 指标计算结果数据
    """
    
    def __init__(self, db_session: AsyncSession) -> None:
        """
        初始化数据生成器
        
        Args:
            db_session: 数据库会话
        """
        self.db_session = db_session
    
    async def generate_all_test_data(self) -> None:
        """
        生成所有效能指标测试数据
        """
        print("开始生成效能指标测试数据...")
        
        # 生成效能指标定义
        indicators = await self.generate_efficiency_indicators()
        print(f"生成了 {len(indicators)} 个效能指标定义")
        
        # 生成权重配置
        weight_configs = await self.generate_weight_configs()
        print(f"生成了 {len(weight_configs)} 个权重配置")
        
        # 获取现有场景用于生成计算结果
        scenarios = await self.get_existing_scenarios()
        if scenarios:
            calculation_results = await self.generate_calculation_results(indicators, scenarios)
            print(f"生成了 {len(calculation_results)} 个计算结果")
        else:
            print("未找到现有场景，跳过计算结果生成")
        
        print("效能指标测试数据生成完成！")
    
    async def generate_efficiency_indicators(self) -> list[EfficiencyIndicator]:
        """
        生成效能指标定义数据
        
        Returns:
            list[EfficiencyIndicator]: 生成的指标列表
        """
        indicators_data = [
            # 时效性指标
            {
                "indicator_name": "任务完成时间",
                "indicator_code": "TASK_COMPLETION_TIME",
                "category": IndicatorCategoryEnum.TIMELINESS,
                "description": "从任务开始到完全结束的总耗时",
                "calculation_formula": "结束时间 - 开始时间",
                "unit": "小时",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('72'),
                "target_value": Decimal('24'),
                "default_weight": Decimal('0.3000'),
                "priority_level": 1
            },
            {
                "indicator_name": "平均响应时间",
                "indicator_code": "AVERAGE_RESPONSE_TIME",
                "category": IndicatorCategoryEnum.TIMELINESS,
                "description": "从需求提出到开始执行的平均时间",
                "calculation_formula": "sum(响应时间列表) / len(响应时间列表)",
                "unit": "小时",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('12'),
                "target_value": Decimal('2'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 2
            },
            {
                "indicator_name": "准时交付率",
                "indicator_code": "ON_TIME_DELIVERY_RATE",
                "category": IndicatorCategoryEnum.TIMELINESS,
                "description": "在规定时间内完成任务的比例",
                "calculation_formula": "(准时完成任务数 / 总任务数) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('95'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 1
            },
            {
                "indicator_name": "单位时间运输量",
                "indicator_code": "TRANSPORT_VOLUME_PER_HOUR",
                "category": IndicatorCategoryEnum.TIMELINESS,
                "description": "每小时完成的运输量",
                "calculation_formula": "总运输量 / 持续时间",
                "unit": "吨/小时",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('1000'),
                "target_value": Decimal('100'),
                "default_weight": Decimal('0.2000'),
                "priority_level": 3
            },
            
            # 能力指标
            {
                "indicator_name": "最大持续运输能力",
                "indicator_code": "MAX_SUSTAINED_CAPACITY",
                "category": IndicatorCategoryEnum.CAPABILITY,
                "description": "在给定条件下可持续的最大运输量",
                "calculation_formula": "峰值运输能力 * 可持续性系数",
                "unit": "吨",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('10000'),
                "target_value": Decimal('5000'),
                "default_weight": Decimal('0.3000'),
                "priority_level": 1
            },
            {
                "indicator_name": "任务达成率",
                "indicator_code": "MISSION_SUCCESS_RATE",
                "category": IndicatorCategoryEnum.CAPABILITY,
                "description": "成功完成预定目标的百分比",
                "calculation_formula": "(成功任务数 / 总任务数) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('98'),
                "default_weight": Decimal('0.3000'),
                "priority_level": 1
            },
            {
                "indicator_name": "可用架次率",
                "indicator_code": "AVAILABLE_SORTIE_RATE",
                "category": IndicatorCategoryEnum.CAPABILITY,
                "description": "实际可执行任务的架次占总架次的比例",
                "calculation_formula": "(可用架次数 / 总架次数) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('90'),
                "default_weight": Decimal('0.2000'),
                "priority_level": 2
            },
            {
                "indicator_name": "装备利用率",
                "indicator_code": "EQUIPMENT_UTILIZATION_RATE",
                "category": IndicatorCategoryEnum.CAPABILITY,
                "description": "装备实际使用时间占可用时间的比例",
                "calculation_formula": "(实际使用时间 / 可用时间) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('80'),
                "default_weight": Decimal('0.2000'),
                "priority_level": 3
            },
            
            # 经济性指标
            {
                "indicator_name": "吨公里成本",
                "indicator_code": "TON_KM_COST",
                "category": IndicatorCategoryEnum.ECONOMY,
                "description": "完成单位运输量的综合成本",
                "calculation_formula": "总成本 / (运输量 * 距离)",
                "unit": "元/吨公里",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('10'),
                "default_weight": Decimal('0.3000'),
                "priority_level": 1
            },
            {
                "indicator_name": "资源利用率",
                "indicator_code": "RESOURCE_UTILIZATION_RATE",
                "category": IndicatorCategoryEnum.ECONOMY,
                "description": "各类资源的实际利用效率",
                "calculation_formula": "(已使用资源 / 总资源) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('85'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 2
            },
            {
                "indicator_name": "冗余度",
                "indicator_code": "REDUNDANCY_RATIO",
                "category": IndicatorCategoryEnum.ECONOMY,
                "description": "为应对不确定性预留的资源比例",
                "calculation_formula": "(预留资源 / 所需资源) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('50'),
                "target_value": Decimal('20'),
                "default_weight": Decimal('0.2000'),
                "priority_level": 3
            },
            {
                "indicator_name": "燃油效率",
                "indicator_code": "FUEL_EFFICIENCY",
                "category": IndicatorCategoryEnum.ECONOMY,
                "description": "单位燃油消耗完成的运输量",
                "calculation_formula": "运输量 / 燃油消耗",
                "unit": "吨/升",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('10'),
                "target_value": Decimal('2'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 2
            },
            
            # 鲁棒性指标
            {
                "indicator_name": "场景适应度",
                "indicator_code": "SCENARIO_ADAPTABILITY",
                "category": IndicatorCategoryEnum.ROBUSTNESS,
                "description": "在不同环境条件下保持效能的能力",
                "calculation_formula": "1 / 性能评分标准差",
                "unit": "适应度指数",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('80'),
                "default_weight": Decimal('0.3000'),
                "priority_level": 1
            },
            {
                "indicator_name": "抗毁伤能力",
                "indicator_code": "DAMAGE_RESISTANCE",
                "category": IndicatorCategoryEnum.ROBUSTNESS,
                "description": "部分资源受损后维持运行的能力",
                "calculation_formula": "(受损后运行能力 / 原始运行能力) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('70'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 1
            },
            {
                "indicator_name": "恢复时间",
                "indicator_code": "RECOVERY_TIME",
                "category": IndicatorCategoryEnum.ROBUSTNESS,
                "description": "从故障状态恢复到正常运行的时间",
                "calculation_formula": "恢复时间 - 故障时间",
                "unit": "小时",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('48'),
                "target_value": Decimal('4'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 2
            },
            {
                "indicator_name": "风险抵抗力",
                "indicator_code": "RISK_RESISTANCE",
                "category": IndicatorCategoryEnum.ROBUSTNESS,
                "description": "应对各类风险的综合能力",
                "calculation_formula": "(风险缓解评分 / 风险暴露评分) * 100",
                "unit": "抵抗力指数",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('200'),
                "target_value": Decimal('120'),
                "default_weight": Decimal('0.2000'),
                "priority_level": 3
            },
            
            # 安全性指标
            {
                "indicator_name": "任务风险概率",
                "indicator_code": "MISSION_RISK_PROBABILITY",
                "category": IndicatorCategoryEnum.SAFETY,
                "description": "任务执行过程中发生事故的概率",
                "calculation_formula": "(风险事件数 / 总任务数) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('10'),
                "target_value": Decimal('1'),
                "default_weight": Decimal('0.3000'),
                "priority_level": 1
            },
            {
                "indicator_name": "损失率",
                "indicator_code": "LOSS_RATE",
                "category": IndicatorCategoryEnum.SAFETY,
                "description": "运输过程中损失的物资占总量的比例",
                "calculation_formula": "(损失量 / 总量) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('5'),
                "target_value": Decimal('0.5'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 1
            },
            {
                "indicator_name": "安全裕度",
                "indicator_code": "SAFETY_MARGIN",
                "category": IndicatorCategoryEnum.SAFETY,
                "description": "安全措施的充分程度",
                "calculation_formula": "((安全措施评分 - 最低要求评分) / 最低要求评分) * 100",
                "unit": "百分比",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('100'),
                "target_value": Decimal('30'),
                "default_weight": Decimal('0.2500'),
                "priority_level": 2
            },
            {
                "indicator_name": "应急响应能力",
                "indicator_code": "EMERGENCY_RESPONSE_CAPABILITY",
                "category": IndicatorCategoryEnum.SAFETY,
                "description": "处理突发事件的能力",
                "calculation_formula": "(标准响应时间 / 实际响应时间) * 100",
                "unit": "能力指数",
                "min_threshold": Decimal('0'),
                "max_threshold": Decimal('200'),
                "target_value": Decimal('100'),
                "default_weight": Decimal('0.2000'),
                "priority_level": 3
            }
        ]
        
        indicators = []
        for data in indicators_data:
            indicator = EfficiencyIndicator(
                **data,
                status=IndicatorStatusEnum.ACTIVE,
                created_by="system",
                metadata_info={"source": "test_data_generator", "version": "1.0"}
            )
            self.db_session.add(indicator)
            indicators.append(indicator)
        
        await self.db_session.commit()
        return indicators

    async def generate_weight_configs(self) -> list[IndicatorWeightConfig]:
        """
        生成权重配置数据

        Returns:
            list[IndicatorWeightConfig]: 生成的权重配置列表
        """
        weight_configs_data = [
            {
                "config_name": "人员投送权重配置",
                "scenario_type": "personnel_transport",
                "description": "人员投送场景的效能指标权重配置",
                "weight_settings": {
                    "timeliness": 0.35,
                    "capability": 0.25,
                    "economy": 0.15,
                    "robustness": 0.15,
                    "safety": 0.10
                },
                "is_default": True
            },
            {
                "config_name": "装备运输权重配置",
                "scenario_type": "equipment_transport",
                "description": "装备运输场景的效能指标权重配置",
                "weight_settings": {
                    "timeliness": 0.25,
                    "capability": 0.30,
                    "economy": 0.20,
                    "robustness": 0.15,
                    "safety": 0.10
                },
                "is_default": True
            },
            {
                "config_name": "物资补给权重配置",
                "scenario_type": "material_supply",
                "description": "物资补给场景的效能指标权重配置",
                "weight_settings": {
                    "timeliness": 0.30,
                    "capability": 0.25,
                    "economy": 0.25,
                    "robustness": 0.10,
                    "safety": 0.10
                },
                "is_default": True
            },
            {
                "config_name": "医疗后送权重配置",
                "scenario_type": "medical_evacuation",
                "description": "医疗后送场景的效能指标权重配置",
                "weight_settings": {
                    "timeliness": 0.40,
                    "capability": 0.20,
                    "economy": 0.10,
                    "robustness": 0.15,
                    "safety": 0.15
                },
                "is_default": True
            },
            {
                "config_name": "特种运输权重配置",
                "scenario_type": "special_transport",
                "description": "特种运输场景的效能指标权重配置",
                "weight_settings": {
                    "timeliness": 0.20,
                    "capability": 0.25,
                    "economy": 0.15,
                    "robustness": 0.20,
                    "safety": 0.20
                },
                "is_default": True
            }
        ]

        weight_configs = []
        for data in weight_configs_data:
            config = IndicatorWeightConfig(
                **data,
                is_active=True,
                created_by="system"
            )
            self.db_session.add(config)
            weight_configs.append(config)

        await self.db_session.commit()
        return weight_configs

    async def get_existing_scenarios(self) -> list[Scenario]:
        """
        获取现有场景用于生成计算结果

        Returns:
            list[Scenario]: 现有场景列表
        """
        from sqlalchemy import select

        stmt = select(Scenario).limit(5)  # 限制获取5个场景
        result = await self.db_session.execute(stmt)
        return result.scalars().all()

    async def generate_calculation_results(self, indicators: list[EfficiencyIndicator],
                                         scenarios: list[Scenario]) -> list[IndicatorCalculationResult]:
        """
        生成指标计算结果数据

        Args:
            indicators: 指标列表
            scenarios: 场景列表

        Returns:
            list[IndicatorCalculationResult]: 生成的计算结果列表
        """
        import random

        calculation_results = []

        for scenario in scenarios:
            for indicator in indicators:
                # 根据指标类型生成合理的测试值
                calculated_value = self._generate_indicator_value(indicator)

                result = IndicatorCalculationResult(
                    scenario_id=scenario.id,
                    indicator_id=indicator.id,
                    calculated_value=calculated_value,
                    confidence_level=Decimal(str(random.uniform(0.8, 0.99))),
                    calculation_method="test_simulation",
                    calculation_status=CalculationStatusEnum.COMPLETED,
                    input_parameters={
                        "test_mode": True,
                        "scenario_type": scenario.task_type.value,
                        "generated_at": datetime.now().isoformat()
                    },
                    calculation_metadata={
                        "generator": "test_data_generator",
                        "version": "1.0",
                        "calculation_time_ms": random.randint(100, 5000)
                    }
                )

                self.db_session.add(result)
                calculation_results.append(result)

        await self.db_session.commit()
        return calculation_results

    def _generate_indicator_value(self, indicator: EfficiencyIndicator) -> Decimal:
        """
        根据指标类型生成合理的测试值

        Args:
            indicator: 效能指标

        Returns:
            Decimal: 生成的指标值
        """
        import random

        # 根据指标的目标值和阈值生成合理的随机值
        if indicator.target_value:
            # 在目标值附近生成值，允许一定的波动
            base_value = float(indicator.target_value)
            variation = base_value * 0.2  # 20%的波动范围
            value = random.uniform(base_value - variation, base_value + variation)
        elif indicator.max_threshold:
            # 在最大阈值范围内生成值
            max_val = float(indicator.max_threshold)
            min_val = float(indicator.min_threshold) if indicator.min_threshold else 0
            value = random.uniform(min_val, max_val * 0.8)  # 不超过最大值的80%
        else:
            # 默认生成0-100之间的值
            value = random.uniform(0, 100)

        # 确保值在合理范围内
        if indicator.min_threshold and value < float(indicator.min_threshold):
            value = float(indicator.min_threshold)
        if indicator.max_threshold and value > float(indicator.max_threshold):
            value = float(indicator.max_threshold)

        return Decimal(str(value)).quantize(Decimal('0.01'))


async def main():
    """
    主函数：执行测试数据生成
    """
    print("效能指标测试数据生成脚本启动...")

    # 获取数据库会话
    async for db_session in get_database_session():
        try:
            generator = EfficiencyTestDataGenerator(db_session)
            await generator.generate_all_test_data()
            print("测试数据生成成功完成！")
        except Exception as e:
            print(f"测试数据生成失败: {e}")
            await db_session.rollback()
            raise
        finally:
            await db_session.close()
        break


if __name__ == "__main__":
    asyncio.run(main())
