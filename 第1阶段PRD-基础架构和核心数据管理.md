# 第1阶段PRD - 基础架构和核心数据管理

## 1. 阶段概述

### 1.1 阶段目标
建立航空运输保障效能算法库的基础架构，实现核心数据管理功能，为后续算法开发奠定坚实基础。

### 1.2 开发周期
**预计时间**：3个月（12周）
**团队规模**：5-6人（架构师1人，后端开发3人，测试1人，DevOps 1人）

### 1.3 核心交付物
- 微服务基础架构
- 数据库设计和初始化
- 场景定义与参数化模块
- 装卸载配置管理模块
- 基础配置方案管理模块
- 核心API接口
- 基础测试框架

## 2. 功能需求详述

### 2.1 场景定义与参数化模块

#### 2.1.1 功能描述
支持多维度场景参数的定义和管理，为效能计算提供基础数据支撑。

#### 2.1.2 具体需求
**任务类型管理**：
- 支持5种任务类型：人员投送、装备运输、物资补给、医疗后送、特种运输
- 每种任务类型包含特定的参数模板和约束条件
- 支持任务类型的动态配置和扩展

**环境条件设置**：
- 气象条件：温度、湿度、风速、能见度、降水量
- 地形特征：平原、山地、海岛、高原等地形类型
- 空域管制：管制等级、飞行限制、航路规划
- 电磁环境：干扰强度、频段影响、通信质量
- 威胁等级：低、中、高三级威胁评估

**资源约束配置**：
- 运输力量：飞机数量、机型配置、载重能力
- 保障资源：装卸设备、燃料供应、维修保障
- 时间窗口：任务开始时间、结束时间、允许延误时间

**任务要求定义**：
- 运输量：货物重量、人员数量、装备规格
- 起讫点：出发地、目的地、中转站
- 时效性要求：紧急程度、完成时限
- 安全性要求：安全等级、风险控制措施

**对抗因素建模**：
- 敌方威胁：防空威胁、电子干扰、网络攻击
- 威胁概率：各类威胁的发生概率和影响程度
- 应对措施：规避策略、防护手段、备用方案

#### 2.1.3 数据存储需求
**场景基础信息存储**：
- 场景唯一标识和名称
- 场景类型和详细描述
- 任务类型分类
- 环境条件参数（气象、地形、空域、电磁、威胁）
- 资源约束配置
- 任务需求定义
- 威胁因素建模
- 创建者和时间信息
- 场景状态管理

**任务类型模板存储**：
- 任务类型唯一标识和名称
- 任务类型详细描述
- 参数模板和约束条件
- 激活状态管理

**环境条件详细存储**：
- 环境条件唯一标识
- 关联的场景信息
- 气象参数详细配置
- 地形类型和特征
- 空域管制信息
- 电磁环境参数
- 威胁等级评估

### 2.2 装卸载配置管理模块

#### 2.2.1 功能描述
支持详细的装卸载设备和人员配置管理，为效能计算提供准确的资源配置数据。

#### 2.2.2 具体需求
**设备配置管理**：
- 支持多种设备类型：运输车辆、装卸设备、支援设备
- 设备规格参数：最大载重、装卸速度、作业半径、功耗
- 性能参数：单位作业时长、效率系数、维护间隔
- 设备状态管理：可用、维修、故障、报废

**飞机配置管理**：
- 支持多种机型：运-20、运-31、运-8、运-7等
- 机型参数：载重能力、航程、巡航速度、燃油消耗
- 作业参数：装载时间、卸载时间、地面滑行时间
- 机组配置：飞行员、机械师、装载员需求

**人员配置管理**：
- 人员类型：设备操作员、地勤人员、指挥协调员、安全监督员
- 技能等级：初级、中级、高级、专家级
- 工作效率：基础效率、技能加成、经验加成
- 班次安排：工作时长、休息时间、轮班制度

#### 2.2.3 数据存储需求
**设备配置信息存储**：
- 设备唯一标识和型号
- 设备类型分类（运输车辆、装卸设备、支援设备）
- 设备数量和规格参数
- 性能参数（载重、速度、作业时长、效率系数）
- 运行状态和维护信息
- 创建和更新时间记录

**飞机配置信息存储**：
- 飞机唯一标识和机型
- 飞机数量和载重能力
- 作战半径和巡航速度
- 燃油消耗和作业时间
- 机组人员需求配置
- 维护计划和状态

**人员配置信息存储**：
- 人员配置唯一标识
- 人员类型和数量
- 技能等级和效率参数
- 班次安排和设备分配
- 认证要求和培训记录

### 2.3 基础配置方案管理模块

#### 2.3.1 功能描述
支持完整配置方案的保存、加载和管理，实现配置数据的持久化和版本控制。

#### 2.3.2 具体需求
**方案存储管理**：
- 配置方案的创建、保存、删除
- 方案命名规范和唯一性检查
- 方案分类和标签管理
- 方案搜索和筛选功能

**方案版本控制**：
- 方案版本号自动生成
- 历史版本保存和查看
- 版本比较和差异分析
- 版本回滚和恢复功能

**方案操作功能**：
- 方案复制和克隆
- 方案模板创建和应用
- 方案导入导出（JSON/Excel格式）
- 批量操作和管理

**方案验证功能**：
- 配置参数完整性检查
- 数据类型和格式验证
- 业务规则一致性验证
- 约束条件合规性检查

#### 2.3.3 数据存储需求
**配置方案基础信息存储**：
- 方案唯一标识和名称
- 方案类型和详细描述
- 版本号和状态管理
- 设备配置列表
- 飞机配置列表
- 人员配置列表
- 场景参数和作业参数
- 创建者和时间信息
- 标签和分类信息

**方案版本控制存储**：
- 版本唯一标识
- 关联的方案信息
- 版本号和变更描述
- 完整的配置数据
- 创建者和时间
- 当前版本标识

**方案模板信息存储**：
- 模板唯一标识和名称
- 模板类型和描述
- 配置模板数据
- 使用次数统计
- 公开状态和创建信息

## 3. 技术实现方案

### 3.1 系统架构设计

#### 3.1.1 微服务架构设计
**三层服务架构**：
- **API网关层**：统一的API入口，负责路由转发、限流控制、监控日志
- **业务服务层**：配置管理服务、场景管理服务，各自独立部署和扩展
- **数据存储层**：PostgreSQL主数据库、Redis缓存、文件存储服务

**服务间通信**：
- 采用RESTful API进行服务间通信
- 使用消息队列处理异步任务
- 实现服务发现和负载均衡机制

#### 3.1.2 技术栈选择
- **编程语言**：Python 3.9+
- **Web框架**：FastAPI
- **数据库**：PostgreSQL 14+
- **缓存**：Redis 6+
- **消息队列**：RabbitMQ
- **容器化**：Docker + Docker Compose
- **API文档**：自动生成OpenAPI文档

#### 3.1.3 项目结构设计
**服务模块组织**：
- API网关服务：统一入口和路由管理
- 场景管理服务：场景定义和参数化功能
- 配置管理服务：设备、飞机、人员配置管理
- 共享组件：通用工具和基础设施

**数据管理组织**：
- 数据库迁移脚本：版本化的数据库结构变更
- 测试数据种子：用于开发和测试的基础数据
- 数据库模式：完整的数据库结构定义

**测试组织**：
- 单元测试：各模块的独立功能测试
- 集成测试：服务间协作的测试
- 测试数据：标准化的测试用例数据

**部署配置**：
- Docker容器化配置
- 容器编排和服务发现
- 环境配置和部署脚本

### 3.2 数据库设计

#### 3.2.1 核心数据表设计
**场景数据表**：
- 存储场景的基本信息：唯一标识、名称、类型、描述
- 记录任务类型和环境条件参数
- 保存资源约束和任务需求配置
- 存储威胁因素和创建者信息
- 支持场景状态管理和时间戳记录

**设备配置数据表**：
- 存储设备的基本信息：唯一标识、类型、型号、数量
- 记录设备规格参数和性能指标
- 保存运行状态和维护信息
- 支持创建和更新时间追踪
- 实现数量约束和状态管理

**飞机配置数据表**：
- 存储飞机的基本信息：唯一标识、机型、数量
- 记录载重能力、航程、速度等性能参数
- 保存装卸时间和机组需求配置
- 存储维护计划和燃油消耗数据
- 支持时间戳和状态追踪

**配置方案数据表**：
- 存储方案的基本信息：唯一标识、名称、类型、描述
- 记录版本号和各类配置关联
- 保存场景参数和作业参数
- 支持标签分类和状态管理
- 实现创建者追踪和时间记录

#### 3.2.2 数据库性能优化设计
**查询性能优化**：
- 场景表按类型和状态建立复合索引，支持快速筛选
- 设备配置表按设备类型建立索引，提高分类查询效率
- 飞机配置表按机型建立索引，优化机型查询性能
- 配置方案表按方案类型建立索引，支持方案分类检索
- 配置方案表的标签字段建立全文索引，支持标签搜索

**数据完整性保证**：
- 设置主键约束确保数据唯一性
- 建立外键关联保证数据一致性
- 添加检查约束验证数据有效性
- 使用时间戳字段追踪数据变更

## 4. API接口详细规范

### 4.1 场景管理API

#### 4.1.1 创建场景
**接口地址**：`POST /api/v1/scenarios`

**业务功能**：创建新的运输保障场景，定义完整的任务参数和环境条件

**请求参数说明**：
- 场景基本信息：场景名称、类型、描述、任务类型
- 环境条件配置：气象参数、地形类型、空域管制、电磁环境、威胁等级
- 资源约束设置：飞机数量限制、设备数量限制、人员数量限制、时间窗口
- 任务需求定义：货物重量、起讫点、优先级、安全等级
- 威胁因素评估：防空威胁、电子干扰、气象风险等概率

**响应内容**：
- 场景唯一标识：用于后续操作的场景ID
- 场景基本信息：名称和状态确认
- 创建时间：场景创建的时间戳
- 操作结果：创建成功或失败的状态信息

#### 4.1.2 获取场景列表
**接口地址**：`GET /api/v1/scenarios`

**业务功能**：分页查询场景列表，支持多种筛选条件

**查询参数说明**：
- 分页参数：页码和每页数量控制
- 类型筛选：按场景类型和任务类型过滤
- 状态筛选：按场景状态过滤
- 关键词搜索：支持场景名称和描述的模糊搜索

**响应内容**：
- 场景列表：包含场景基本信息的数组
- 分页信息：当前页码、每页数量、总数、总页数
- 场景详情：每个场景的ID、名称、类型、状态、时间等信息

### 4.2 设备配置管理API

#### 4.2.1 创建设备配置
**接口地址**：`POST /api/v1/equipment-configs`

**业务功能**：创建新的设备配置，定义设备的基本信息和性能参数

**请求参数说明**：
- 设备基本信息：设备类型、型号、数量
- 规格参数：最大载重、装卸速度、作业半径、功耗
- 性能参数：单位作业时长、效率系数、维护间隔
- 状态信息：运行状态、维护信息

**业务价值**：为效能计算提供准确的设备能力数据，支持资源配置优化

#### 4.2.2 获取设备配置列表
**接口地址**：`GET /api/v1/equipment-configs`

**业务功能**：查询设备配置列表，支持按类型和状态筛选

**查询参数说明**：
- 设备类型筛选：按运输车辆、装卸设备、支援设备分类查询
- 运行状态筛选：按可用、维修、故障等状态过滤
- 分页参数：支持大量设备配置的分页浏览

### 4.3 配置方案管理API

#### 4.3.1 创建配置方案
**接口地址**：`POST /api/v1/configuration-schemes`

**业务功能**：创建完整的配置方案，整合设备、飞机、人员等各类配置

**请求参数说明**：
- 方案基本信息：方案名称、类型、描述
- 设备配置关联：关联的设备配置ID、数量、分配优先级
- 飞机配置关联：关联的飞机配置ID、数量、任务分配
- 人员配置定义：人员类型、数量、技能等级、班次安排
- 作业参数设置：最大作业时长、安全裕度、效率目标
- 标签分类：便于方案分类和检索的标签

**业务价值**：提供完整的资源配置方案，支持效能计算和方案比较分析

## 5. 测试策略和验收标准

### 5.1 测试策略

#### 5.1.1 单元测试
- **覆盖率要求**：≥90%
- **测试框架**：pytest + pytest-cov
- **测试内容**：
  - 数据模型验证
  - 业务逻辑函数
  - 工具函数和辅助方法
  - 异常处理逻辑

#### 5.1.2 集成测试
- **测试范围**：API接口、数据库操作、服务间通信
- **测试工具**：pytest + httpx + testcontainers
- **测试环境**：Docker容器化测试环境

#### 5.1.3 性能测试
- **响应时间要求**：
  - 简单查询API：< 200ms
  - 复杂查询API：< 1s
  - 数据创建API：< 500ms
- **并发测试**：支持50个并发请求
- **数据量测试**：支持10万条记录的查询和操作

### 5.2 验收标准

#### 5.2.1 功能验收标准
- 场景管理功能完整实现，支持CRUD操作
- 设备配置管理功能完整实现，支持多种设备类型
- 飞机配置管理功能完整实现，支持主要机型
- 配置方案管理功能完整实现，支持版本控制
- 所有API接口正常工作，返回格式符合规范

#### 5.2.2 性能验收标准
- API响应时间满足要求
- 数据库查询性能优化到位
- 系统支持预期的并发用户数
- 内存使用控制在合理范围内

#### 5.2.3 质量验收标准
- 代码覆盖率达到90%以上
- 所有测试用例通过
- 代码质量检查通过（flake8、black、mypy）
- 文档完整，包含API文档和开发文档

## 6. 风险评估和应对措施

### 6.1 技术风险

#### 6.1.1 数据库性能风险
**风险描述**：大量JSONB字段可能影响查询性能
**应对措施**：
- 合理设计索引，特别是GIN索引
- 对热点查询进行性能测试和优化
- 考虑数据分区策略

#### 6.1.2 微服务复杂性风险
**风险描述**：微服务架构增加系统复杂性
**应对措施**：
- 采用成熟的微服务框架和工具
- 完善的服务监控和日志系统
- 详细的服务间接口文档

### 6.2 进度风险

#### 6.2.1 需求变更风险
**风险描述**：需求变更可能影响开发进度
**应对措施**：
- 需求冻结机制，重大变更需评估影响
- 预留20%的缓冲时间
- 采用敏捷开发方法，快速响应变更

#### 6.2.2 技术难点风险
**风险描述**：复杂数据结构设计可能遇到技术难点
**应对措施**：
- 提前进行技术预研和原型验证
- 寻求技术专家支持
- 制定备选技术方案

## 7. 开发时间线和里程碑

### 7.1 第1-4周：基础架构搭建
- **Week 1-2**：项目初始化、环境搭建、技术栈确定
- **Week 3-4**：微服务架构设计、数据库设计、基础框架搭建

### 7.2 第5-8周：核心功能开发
- **Week 5-6**：场景管理模块开发和测试
- **Week 7-8**：设备配置管理模块开发和测试

### 7.3 第9-12周：功能完善和集成
- **Week 9-10**：配置方案管理模块开发和测试
- **Week 11**：系统集成测试、性能优化
- **Week 12**：文档完善、部署准备、验收测试

### 7.4 关键里程碑
- **M1（第4周末）**：基础架构完成，数据库设计确定
- **M2（第8周末）**：核心数据管理功能完成
- **M3（第12周末）**：第一阶段功能全部完成，通过验收测试

---

*第1阶段PRD文档 - 版本1.0*
*创建日期：2025年7月30日*
*遵循base-rules.md开发规范*
